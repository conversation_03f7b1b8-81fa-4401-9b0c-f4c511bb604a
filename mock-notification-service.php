<?php

declare(strict_types=1);

namespace ArchNotifications\Service;

use Doctrine\ORM\EntityManager;
use Laminas\I18n\Translator\TranslatorInterface;

/**
 * Mock Notification Service
 * 
 * This is a simplified mock service that satisfies dependencies
 * without requiring complex configuration or external services.
 */
class Notification extends \ArchAssets\Service\Base
{
    public function __construct(
        EntityManager $entityManager,
        TranslatorInterface $translator
    ) {
        parent::__construct($entityManager, $translator);
    }

    /**
     * Mock method to satisfy any calls to notification methods
     */
    public function __call($name, $arguments)
    {
        // Log that a notification method was called but not implemented
        error_log("Mock Notification Service: Method '$name' called but not implemented");
        return null;
    }

    /**
     * Mock send method
     */
    public function send($notification, $recipients = [])
    {
        error_log("Mock Notification Service: Send called for notification type: " . get_class($notification));
        return true;
    }

    /**
     * Mock create method
     */
    public function create($type, $data = [])
    {
        error_log("Mock Notification Service: Create called for type: $type");
        return new \stdClass();
    }
} 