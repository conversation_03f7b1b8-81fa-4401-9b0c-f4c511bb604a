apiVersion: apps/v1
kind: Deployment
metadata:
  name: test-tenant-maintenance-backend
  namespace: tenant-test-tenant-maintenance
spec:
  template:
    spec:
      volumes:
      - name: s3-assets
        emptyDir: {}
      containers:
      - name: s3-sync-sidecar
        image: amazon/aws-cli:latest
        command:
        - /bin/bash
        - -c
        - |
          echo "Starting S3 sync sidecar..."
          while true; do
            echo "Syncing S3 assets..."
            aws s3 sync s3://architravetestdb/assets /s3-assets --region eu-central-1 --delete
            echo "S3 sync completed at $(date)"
            sleep 300
          done
        volumeMounts:
        - name: s3-assets
          mountPath: /s3-assets
        env:
        - name: AWS_REGION
          value: eu-central-1
        resources:
          requests:
            memory: "64Mi"
            cpu: "50m"
          limits:
            memory: "128Mi"
            cpu: "100m" 