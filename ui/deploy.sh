#!/bin/bash

# Script to build and deploy the Tenant Management UI
# This script builds the Docker image and deploys it to Kubernetes

set -e

# Build the Docker image
echo "Building Docker image..."
docker build -t tenant-management-ui:latest .

# Tag the image for ECR
echo "Tagging image for ECR..."
docker tag tenant-management-ui:latest 545009857703.dkr.ecr.eu-central-1.amazonaws.com/tenant-management-ui:latest

# Login to ECR
echo "Logging in to ECR..."
aws ecr get-login-password --region eu-central-1 | docker login --username AWS --password-stdin 545009857703.dkr.ecr.eu-central-1.amazonaws.com

# Push the image to ECR
echo "Pushing image to ECR..."
docker push 545009857703.dkr.ecr.eu-central-1.amazonaws.com/tenant-management-ui:latest

# Create the tenant-management namespace if it doesn't exist
echo "Creating tenant-management namespace..."
kubectl create namespace tenant-management --dry-run=client -o yaml | kubectl apply -f -

# Deploy to Kubernetes
echo "Deploying to Kubernetes..."
kubectl apply -f kubernetes/deployment.yaml

# Wait for the deployment to be ready
echo "Waiting for deployment to be ready..."
kubectl rollout status deployment/tenant-management-ui -n tenant-management

echo "Tenant Management UI deployed successfully!"
echo "Access the UI at https://tenant-management.architrave.com"
