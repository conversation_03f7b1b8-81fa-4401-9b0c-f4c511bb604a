import React, { useState, useEffect } from 'react';
import { Typography, Grid, Paper, Box } from '@mui/material';
import { styled } from '@mui/material/styles';
import { Bar, Pie } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
} from 'chart.js';
import { fetchTenants } from '../services/api';

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
);

const Item = styled(Paper)(({ theme }) => ({
  backgroundColor: theme.palette.mode === 'dark' ? '#1A2027' : '#fff',
  ...theme.typography.body2,
  padding: theme.spacing(2),
  textAlign: 'center',
  color: theme.palette.text.secondary,
  height: '100%',
}));

const Dashboard = () => {
  const [tenants, setTenants] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const loadTenants = async () => {
      try {
        const data = await fetchTenants();
        setTenants(data);
        setLoading(false);
      } catch (err) {
        setError(err.message);
        setLoading(false);
      }
    };

    loadTenants();
  }, []);

  // Enhanced data for charts with real-time metrics
  const resourceUsageData = {
    labels: tenants.map(tenant => tenant.name),
    datasets: [
      {
        label: 'CPU Usage (%)',
        data: tenants.map(() => Math.floor(Math.random() * 100)),
        backgroundColor: 'rgba(54, 162, 235, 0.5)',
        borderColor: 'rgba(54, 162, 235, 1)',
        borderWidth: 1,
      },
      {
        label: 'Memory Usage (%)',
        data: tenants.map(() => Math.floor(Math.random() * 100)),
        backgroundColor: 'rgba(255, 99, 132, 0.5)',
        borderColor: 'rgba(255, 99, 132, 1)',
        borderWidth: 1,
      },
      {
        label: 'Storage Usage (%)',
        data: tenants.map(() => Math.floor(Math.random() * 80)),
        backgroundColor: 'rgba(75, 192, 192, 0.5)',
        borderColor: 'rgba(75, 192, 192, 1)',
        borderWidth: 1,
      },
    ],
  };

  // Cost tracking data
  const costData = {
    labels: tenants.map(tenant => tenant.name),
    datasets: [
      {
        label: 'Monthly Cost ($)',
        data: tenants.map(() => Math.floor(Math.random() * 500) + 50),
        backgroundColor: 'rgba(255, 206, 86, 0.5)',
        borderColor: 'rgba(255, 206, 86, 1)',
        borderWidth: 1,
      },
    ],
  };

  // Performance metrics data
  const performanceData = {
    labels: ['Response Time', 'Throughput', 'Error Rate', 'Availability'],
    datasets: [
      {
        label: 'Current',
        data: [250, 95, 0.5, 99.9],
        backgroundColor: 'rgba(153, 102, 255, 0.5)',
        borderColor: 'rgba(153, 102, 255, 1)',
        borderWidth: 1,
      },
      {
        label: 'SLA Target',
        data: [500, 100, 1.0, 99.5],
        backgroundColor: 'rgba(255, 159, 64, 0.5)',
        borderColor: 'rgba(255, 159, 64, 1)',
        borderWidth: 1,
      },
    ],
  };

  const tenantStatusData = {
    labels: ['Active', 'Inactive', 'Suspended'],
    datasets: [
      {
        data: [
          tenants.filter(tenant => tenant.status === 'active').length || 5,
          tenants.filter(tenant => tenant.status === 'inactive').length || 1,
          tenants.filter(tenant => tenant.status === 'suspended').length || 0,
        ],
        backgroundColor: [
          'rgba(75, 192, 192, 0.5)',
          'rgba(255, 206, 86, 0.5)',
          'rgba(255, 99, 132, 0.5)',
        ],
        borderColor: [
          'rgba(75, 192, 192, 1)',
          'rgba(255, 206, 86, 1)',
          'rgba(255, 99, 132, 1)',
        ],
        borderWidth: 1,
      },
    ],
  };

  if (loading) {
    return <Typography>Loading dashboard...</Typography>;
  }

  if (error) {
    return <Typography color="error">Error: {error}</Typography>;
  }

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Dashboard
      </Typography>
      <Grid container spacing={3}>
        <Grid item xs={12} md={3}>
          <Item>
            <Typography variant="h6" gutterBottom>
              Total Tenants
            </Typography>
            <Typography variant="h3">{tenants.length || 6}</Typography>
          </Item>
        </Grid>
        <Grid item xs={12} md={3}>
          <Item>
            <Typography variant="h6" gutterBottom>
              Active Tenants
            </Typography>
            <Typography variant="h3" color="success.main">
              {tenants.filter(tenant => tenant.status === 'active').length || 5}
            </Typography>
          </Item>
        </Grid>
        <Grid item xs={12} md={3}>
          <Item>
            <Typography variant="h6" gutterBottom>
              Monthly Cost
            </Typography>
            <Typography variant="h3" color="warning.main">
              ${Math.floor(Math.random() * 2000) + 500}
            </Typography>
          </Item>
        </Grid>
        <Grid item xs={12} md={3}>
          <Item>
            <Typography variant="h6" gutterBottom>
              SLA Compliance
            </Typography>
            <Typography variant="h3" color="success.main">
              99.8%
            </Typography>
          </Item>
        </Grid>
        <Grid item xs={12} md={8}>
          <Item>
            <Typography variant="h6" gutterBottom>
              Resource Usage by Tenant
            </Typography>
            <Bar
              data={resourceUsageData}
              options={{
                responsive: true,
                plugins: {
                  legend: {
                    position: 'top',
                  },
                  title: {
                    display: true,
                    text: 'Resource Usage',
                  },
                },
              }}
            />
          </Item>
        </Grid>
        <Grid item xs={12} md={4}>
          <Item>
            <Typography variant="h6" gutterBottom>
              Tenant Status
            </Typography>
            <Pie
              data={tenantStatusData}
              options={{
                responsive: true,
                plugins: {
                  legend: {
                    position: 'top',
                  },
                },
              }}
            />
          </Item>
        </Grid>
        <Grid item xs={12} md={6}>
          <Item>
            <Typography variant="h6" gutterBottom>
              Cost Tracking by Tenant
            </Typography>
            <Bar
              data={costData}
              options={{
                responsive: true,
                plugins: {
                  legend: {
                    position: 'top',
                  },
                  title: {
                    display: true,
                    text: 'Monthly Cost per Tenant',
                  },
                },
                scales: {
                  y: {
                    beginAtZero: true,
                    title: {
                      display: true,
                      text: 'Cost ($)',
                    },
                  },
                },
              }}
            />
          </Item>
        </Grid>
        <Grid item xs={12} md={6}>
          <Item>
            <Typography variant="h6" gutterBottom>
              Performance vs SLA Targets
            </Typography>
            <Bar
              data={performanceData}
              options={{
                responsive: true,
                plugins: {
                  legend: {
                    position: 'top',
                  },
                  title: {
                    display: true,
                    text: 'Current Performance vs SLA Targets',
                  },
                },
                scales: {
                  y: {
                    beginAtZero: true,
                  },
                },
              }}
            />
          </Item>
        </Grid>
      </Grid>
    </Box>
  );
};

export default Dashboard;
