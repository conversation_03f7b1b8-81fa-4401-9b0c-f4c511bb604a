import React, { useState } from 'react';
import {
  Typo<PERSON>,
  Box,
  Paper,
  Stepper,
  Step,
  StepLabel,
  StepContent,
  Button,
  TextField,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormControlLabel,
  Checkbox,
  Card,
  CardContent,
  Alert,
  CircularProgress,
  Chip,
  Divider,
} from '@mui/material';
import {
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Info as InfoIcon,
} from '@mui/icons-material';

const steps = [
  'Basic Information',
  'Resource Configuration',
  'Features & Add-ons',
  'Review & Deploy',
  'Deployment Status'
];

const SelfServiceOnboarding = () => {
  const [activeStep, setActiveStep] = useState(0);
  const [formData, setFormData] = useState({
    // Basic Information
    tenantId: '',
    tenantName: '',
    subdomain: '',
    contactEmail: '',
    organization: '',
    
    // Resource Configuration
    tier: 'basic',
    environment: 'production',
    region: 'eu-central-1',
    
    // Features
    enableDms: false,
    enableApi: false,
    enableAnalytics: false,
    enableBackups: true,
    
    // Advanced
    customDomain: '',
    sslCertificate: false,
  });
  
  const [deploymentStatus, setDeploymentStatus] = useState({
    namespace: 'pending',
    database: 'pending',
    storage: 'pending',
    application: 'pending',
    networking: 'pending',
    monitoring: 'pending',
  });
  
  const [estimatedCost, setEstimatedCost] = useState(0);
  const [deploying, setDeploying] = useState(false);

  const tierConfigs = {
    basic: {
      name: 'Basic',
      cpu: '100m',
      memory: '128Mi',
      storage: '5Gi',
      replicas: '1-3',
      cost: 45,
      features: ['Basic monitoring', 'Standard support', 'Daily backups']
    },
    standard: {
      name: 'Standard',
      cpu: '200m',
      memory: '256Mi',
      storage: '10Gi',
      replicas: '2-5',
      cost: 95,
      features: ['Advanced monitoring', 'Priority support', 'Hourly backups', 'Auto-scaling']
    },
    premium: {
      name: 'Premium',
      cpu: '500m',
      memory: '512Mi',
      storage: '20Gi',
      replicas: '3-10',
      cost: 195,
      features: ['Full monitoring suite', '24/7 support', 'Real-time backups', 'Advanced auto-scaling', 'Custom integrations']
    }
  };

  const handleInputChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Calculate estimated cost
    let baseCost = tierConfigs[formData.tier]?.cost || 45;
    let additionalCost = 0;
    
    if (formData.enableDms) additionalCost += 25;
    if (formData.enableApi) additionalCost += 15;
    if (formData.enableAnalytics) additionalCost += 35;
    if (formData.sslCertificate) additionalCost += 10;
    
    setEstimatedCost(baseCost + additionalCost);
  };

  const handleNext = () => {
    setActiveStep((prevActiveStep) => prevActiveStep + 1);
    
    if (activeStep === 3) {
      // Start deployment
      startDeployment();
    }
  };

  const handleBack = () => {
    setActiveStep((prevActiveStep) => prevActiveStep - 1);
  };

  const startDeployment = () => {
    setDeploying(true);
    
    // Simulate deployment steps
    const steps = ['namespace', 'database', 'storage', 'application', 'networking', 'monitoring'];
    
    steps.forEach((step, index) => {
      setTimeout(() => {
        setDeploymentStatus(prev => ({ ...prev, [step]: 'in-progress' }));
        
        setTimeout(() => {
          setDeploymentStatus(prev => ({ ...prev, [step]: 'completed' }));
          
          if (index === steps.length - 1) {
            setDeploying(false);
          }
        }, 2000 + Math.random() * 3000);
      }, index * 1000);
    });
  };

  const getStepContent = (step) => {
    switch (step) {
      case 0:
        return (
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <TextField
                label="Tenant ID"
                value={formData.tenantId}
                onChange={(e) => handleInputChange('tenantId', e.target.value)}
                fullWidth
                required
                helperText="Unique identifier (lowercase, no spaces)"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                label="Tenant Name"
                value={formData.tenantName}
                onChange={(e) => handleInputChange('tenantName', e.target.value)}
                fullWidth
                required
                helperText="Display name for your tenant"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                label="Subdomain"
                value={formData.subdomain}
                onChange={(e) => handleInputChange('subdomain', e.target.value)}
                fullWidth
                required
                helperText="Your tenant will be accessible at subdomain.architrave.com"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                label="Contact Email"
                type="email"
                value={formData.contactEmail}
                onChange={(e) => handleInputChange('contactEmail', e.target.value)}
                fullWidth
                required
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                label="Organization"
                value={formData.organization}
                onChange={(e) => handleInputChange('organization', e.target.value)}
                fullWidth
              />
            </Grid>
          </Grid>
        );

      case 1:
        return (
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                Choose Your Plan
              </Typography>
              <Grid container spacing={2}>
                {Object.entries(tierConfigs).map(([key, config]) => (
                  <Grid item xs={12} md={4} key={key}>
                    <Card 
                      variant={formData.tier === key ? "outlined" : "elevation"}
                      sx={{ 
                        cursor: 'pointer',
                        border: formData.tier === key ? 2 : 1,
                        borderColor: formData.tier === key ? 'primary.main' : 'divider'
                      }}
                      onClick={() => handleInputChange('tier', key)}
                    >
                      <CardContent>
                        <Typography variant="h6" gutterBottom>
                          {config.name}
                        </Typography>
                        <Typography variant="h4" color="primary" gutterBottom>
                          ${config.cost}/mo
                        </Typography>
                        <Typography variant="body2" gutterBottom>
                          CPU: {config.cpu} | Memory: {config.memory}
                        </Typography>
                        <Typography variant="body2" gutterBottom>
                          Storage: {config.storage} | Replicas: {config.replicas}
                        </Typography>
                        <Divider sx={{ my: 1 }} />
                        {config.features.map((feature, index) => (
                          <Typography key={index} variant="body2" sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
                            <CheckCircleIcon sx={{ mr: 1, fontSize: 16, color: 'success.main' }} />
                            {feature}
                          </Typography>
                        ))}
                      </CardContent>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <InputLabel>Environment</InputLabel>
                <Select
                  value={formData.environment}
                  onChange={(e) => handleInputChange('environment', e.target.value)}
                >
                  <MenuItem value="development">Development</MenuItem>
                  <MenuItem value="staging">Staging</MenuItem>
                  <MenuItem value="production">Production</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <InputLabel>Region</InputLabel>
                <Select
                  value={formData.region}
                  onChange={(e) => handleInputChange('region', e.target.value)}
                >
                  <MenuItem value="eu-central-1">Europe (Frankfurt)</MenuItem>
                  <MenuItem value="us-east-1">US East (N. Virginia)</MenuItem>
                  <MenuItem value="us-west-2">US West (Oregon)</MenuItem>
                  <MenuItem value="ap-southeast-1">Asia Pacific (Singapore)</MenuItem>
                </Select>
              </FormControl>
            </Grid>
          </Grid>
        );

      case 2:
        return (
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                Features & Add-ons
              </Typography>
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={formData.enableDms}
                    onChange={(e) => handleInputChange('enableDms', e.target.checked)}
                  />
                }
                label="Document Management System (+$25/mo)"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={formData.enableApi}
                    onChange={(e) => handleInputChange('enableApi', e.target.checked)}
                  />
                }
                label="External API Access (+$15/mo)"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={formData.enableAnalytics}
                    onChange={(e) => handleInputChange('enableAnalytics', e.target.checked)}
                  />
                }
                label="Advanced Analytics (+$35/mo)"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={formData.enableBackups}
                    onChange={(e) => handleInputChange('enableBackups', e.target.checked)}
                  />
                }
                label="Automated Backups (Included)"
                disabled
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                label="Custom Domain (Optional)"
                value={formData.customDomain}
                onChange={(e) => handleInputChange('customDomain', e.target.value)}
                fullWidth
                helperText="e.g., app.yourcompany.com"
              />
            </Grid>
            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={formData.sslCertificate}
                    onChange={(e) => handleInputChange('sslCertificate', e.target.checked)}
                  />
                }
                label="Custom SSL Certificate (+$10/mo)"
              />
            </Grid>
          </Grid>
        );

      case 3:
        return (
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                Review Your Configuration
              </Typography>
            </Grid>
            <Grid item xs={12} md={8}>
              <Paper sx={{ p: 2 }}>
                <Typography variant="subtitle1" gutterBottom>
                  Tenant Details
                </Typography>
                <Typography>ID: {formData.tenantId}</Typography>
                <Typography>Name: {formData.tenantName}</Typography>
                <Typography>Subdomain: {formData.subdomain}.architrave.com</Typography>
                <Typography>Contact: {formData.contactEmail}</Typography>
                <Typography>Organization: {formData.organization}</Typography>
                
                <Divider sx={{ my: 2 }} />
                
                <Typography variant="subtitle1" gutterBottom>
                  Configuration
                </Typography>
                <Typography>Tier: {tierConfigs[formData.tier].name}</Typography>
                <Typography>Environment: {formData.environment}</Typography>
                <Typography>Region: {formData.region}</Typography>
                
                <Divider sx={{ my: 2 }} />
                
                <Typography variant="subtitle1" gutterBottom>
                  Features
                </Typography>
                {formData.enableDms && <Chip label="Document Management" sx={{ mr: 1, mb: 1 }} />}
                {formData.enableApi && <Chip label="External API" sx={{ mr: 1, mb: 1 }} />}
                {formData.enableAnalytics && <Chip label="Advanced Analytics" sx={{ mr: 1, mb: 1 }} />}
                {formData.enableBackups && <Chip label="Automated Backups" sx={{ mr: 1, mb: 1 }} />}
                {formData.sslCertificate && <Chip label="Custom SSL" sx={{ mr: 1, mb: 1 }} />}
              </Paper>
            </Grid>
            <Grid item xs={12} md={4}>
              <Paper sx={{ p: 2 }}>
                <Typography variant="h6" gutterBottom>
                  Estimated Cost
                </Typography>
                <Typography variant="h4" color="primary">
                  ${estimatedCost}/month
                </Typography>
                <Typography variant="body2" color="textSecondary">
                  Billed monthly
                </Typography>
              </Paper>
            </Grid>
          </Grid>
        );

      case 4:
        return (
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                Deployment Status
              </Typography>
              {deploying && (
                <Alert severity="info" sx={{ mb: 2 }}>
                  <Typography>Deploying your tenant... This may take a few minutes.</Typography>
                </Alert>
              )}
            </Grid>
            {Object.entries(deploymentStatus).map(([step, status]) => (
              <Grid item xs={12} md={6} key={step}>
                <Paper sx={{ p: 2, display: 'flex', alignItems: 'center' }}>
                  {status === 'pending' && <InfoIcon color="disabled" sx={{ mr: 2 }} />}
                  {status === 'in-progress' && <CircularProgress size={24} sx={{ mr: 2 }} />}
                  {status === 'completed' && <CheckCircleIcon color="success" sx={{ mr: 2 }} />}
                  <Typography sx={{ textTransform: 'capitalize' }}>
                    {step.replace(/([A-Z])/g, ' $1')}
                  </Typography>
                </Paper>
              </Grid>
            ))}
            {!deploying && Object.values(deploymentStatus).every(status => status === 'completed') && (
              <Grid item xs={12}>
                <Alert severity="success">
                  <Typography variant="h6">Deployment Complete!</Typography>
                  <Typography>
                    Your tenant is now ready at: https://{formData.subdomain}.architrave.com
                  </Typography>
                </Alert>
              </Grid>
            )}
          </Grid>
        );

      default:
        return 'Unknown step';
    }
  };

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Self-Service Tenant Onboarding
      </Typography>
      
      <Stepper activeStep={activeStep} orientation="vertical">
        {steps.map((label, index) => (
          <Step key={label}>
            <StepLabel>{label}</StepLabel>
            <StepContent>
              {getStepContent(index)}
              <Box sx={{ mb: 2, mt: 2 }}>
                <Button
                  variant="contained"
                  onClick={handleNext}
                  sx={{ mr: 1 }}
                  disabled={deploying}
                >
                  {index === steps.length - 1 ? 'Finish' : index === 3 ? 'Deploy' : 'Continue'}
                </Button>
                <Button
                  disabled={index === 0 || deploying}
                  onClick={handleBack}
                  sx={{ mr: 1 }}
                >
                  Back
                </Button>
              </Box>
            </StepContent>
          </Step>
        ))}
      </Stepper>
    </Box>
  );
};

export default SelfServiceOnboarding;
