import React, { useState, useEffect } from 'react';
import {
  Typo<PERSON>,
  Box,
  Paper,
  Grid,
  Card,
  CardContent,
  CardActions,
  Button,
  Slider,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  Alert,
  Chip,
  LinearProgress,
  Divider,
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  Memory as MemoryIcon,
  Storage as StorageIcon,
  Speed as SpeedIcon,
  AttachMoney as MoneyIcon,
  Settings as SettingsIcon,
} from '@mui/icons-material';

const TenantResourceManagement = ({ tenantId }) => {
  const [tenant, setTenant] = useState(null);
  const [loading, setLoading] = useState(true);
  const [scaling, setScaling] = useState({
    minReplicas: 1,
    maxReplicas: 5,
    targetCPU: 70,
    targetMemory: 75,
    autoScaling: true,
  });
  const [resources, setResources] = useState({
    cpuRequest: 100,
    cpuLimit: 200,
    memoryRequest: 128,
    memoryLimit: 256,
    storageSize: 10,
  });
  const [performance, setPerformance] = useState({
    responseTime: 250,
    throughput: 95,
    errorRate: 0.5,
    availability: 99.9,
  });
  const [costs, setCosts] = useState({
    hourly: 0.85,
    daily: 20.40,
    monthly: 612.00,
    projected: 650.00,
  });

  useEffect(() => {
    // Simulate loading tenant data
    setTimeout(() => {
      setTenant({
        id: tenantId || 'demo-tenant',
        name: 'Demo Tenant',
        tier: 'standard',
        status: 'active',
        currentReplicas: 3,
      });
      setLoading(false);
    }, 1000);
  }, [tenantId]);

  const handleScalingChange = (field, value) => {
    setScaling(prev => ({ ...prev, [field]: value }));
  };

  const handleResourceChange = (field, value) => {
    setResources(prev => ({ ...prev, [field]: value }));
    // Simulate cost calculation
    const newHourly = (value * 0.001 + Math.random() * 0.5).toFixed(2);
    setCosts(prev => ({
      ...prev,
      hourly: parseFloat(newHourly),
      daily: parseFloat(newHourly) * 24,
      monthly: parseFloat(newHourly) * 24 * 30,
      projected: parseFloat(newHourly) * 24 * 30 * 1.1,
    }));
  };

  const applyChanges = () => {
    // Simulate applying changes
    alert('Resource configuration updated successfully!');
  };

  if (loading) {
    return <LinearProgress />;
  }

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Resource Management - {tenant.name}
      </Typography>
      
      <Grid container spacing={3}>
        {/* Current Status */}
        <Grid item xs={12}>
          <Paper sx={{ p: 2, mb: 2 }}>
            <Typography variant="h6" gutterBottom>
              Current Status
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={3}>
                <Chip 
                  label={`Status: ${tenant.status}`} 
                  color={tenant.status === 'active' ? 'success' : 'default'}
                  variant="outlined"
                />
              </Grid>
              <Grid item xs={3}>
                <Chip 
                  label={`Tier: ${tenant.tier}`} 
                  color="primary"
                  variant="outlined"
                />
              </Grid>
              <Grid item xs={3}>
                <Chip 
                  label={`Replicas: ${tenant.currentReplicas}`} 
                  color="info"
                  variant="outlined"
                />
              </Grid>
              <Grid item xs={3}>
                <Chip 
                  label={`Cost: $${costs.hourly}/hr`} 
                  color="warning"
                  variant="outlined"
                />
              </Grid>
            </Grid>
          </Paper>
        </Grid>

        {/* Performance Metrics */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                <SpeedIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                Performance Metrics
              </Typography>
              <Box sx={{ mt: 2 }}>
                <Typography variant="body2">Response Time: {performance.responseTime}ms</Typography>
                <LinearProgress 
                  variant="determinate" 
                  value={(500 - performance.responseTime) / 5} 
                  sx={{ mt: 1, mb: 2 }}
                />
                <Typography variant="body2">Throughput: {performance.throughput}%</Typography>
                <LinearProgress 
                  variant="determinate" 
                  value={performance.throughput} 
                  sx={{ mt: 1, mb: 2 }}
                />
                <Typography variant="body2">Error Rate: {performance.errorRate}%</Typography>
                <LinearProgress 
                  variant="determinate" 
                  value={100 - performance.errorRate * 20} 
                  color="success"
                  sx={{ mt: 1, mb: 2 }}
                />
                <Typography variant="body2">Availability: {performance.availability}%</Typography>
                <LinearProgress 
                  variant="determinate" 
                  value={performance.availability} 
                  color="success"
                  sx={{ mt: 1 }}
                />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Cost Tracking */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                <MoneyIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                Cost Tracking
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <Typography variant="body2" color="textSecondary">Hourly</Typography>
                  <Typography variant="h6">${costs.hourly}</Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2" color="textSecondary">Daily</Typography>
                  <Typography variant="h6">${costs.daily.toFixed(2)}</Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2" color="textSecondary">Monthly</Typography>
                  <Typography variant="h6">${costs.monthly.toFixed(2)}</Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2" color="textSecondary">Projected</Typography>
                  <Typography variant="h6" color="warning.main">${costs.projected.toFixed(2)}</Typography>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Auto Scaling Configuration */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                <TrendingUpIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                Auto Scaling Configuration
              </Typography>
              <FormControlLabel
                control={
                  <Switch
                    checked={scaling.autoScaling}
                    onChange={(e) => handleScalingChange('autoScaling', e.target.checked)}
                  />
                }
                label="Enable Auto Scaling"
                sx={{ mb: 2 }}
              />
              <Box sx={{ mb: 2 }}>
                <Typography gutterBottom>Min Replicas: {scaling.minReplicas}</Typography>
                <Slider
                  value={scaling.minReplicas}
                  onChange={(e, value) => handleScalingChange('minReplicas', value)}
                  min={1}
                  max={10}
                  marks
                  disabled={!scaling.autoScaling}
                />
              </Box>
              <Box sx={{ mb: 2 }}>
                <Typography gutterBottom>Max Replicas: {scaling.maxReplicas}</Typography>
                <Slider
                  value={scaling.maxReplicas}
                  onChange={(e, value) => handleScalingChange('maxReplicas', value)}
                  min={1}
                  max={20}
                  marks
                  disabled={!scaling.autoScaling}
                />
              </Box>
              <Box sx={{ mb: 2 }}>
                <Typography gutterBottom>Target CPU: {scaling.targetCPU}%</Typography>
                <Slider
                  value={scaling.targetCPU}
                  onChange={(e, value) => handleScalingChange('targetCPU', value)}
                  min={50}
                  max={90}
                  disabled={!scaling.autoScaling}
                />
              </Box>
              <Box>
                <Typography gutterBottom>Target Memory: {scaling.targetMemory}%</Typography>
                <Slider
                  value={scaling.targetMemory}
                  onChange={(e, value) => handleScalingChange('targetMemory', value)}
                  min={50}
                  max={90}
                  disabled={!scaling.autoScaling}
                />
              </Box>
            </CardContent>
            <CardActions>
              <Button 
                variant="contained" 
                onClick={applyChanges}
                disabled={!scaling.autoScaling}
              >
                Apply Scaling Config
              </Button>
            </CardActions>
          </Card>
        </Grid>

        {/* Resource Limits */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                <SettingsIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                Resource Limits
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <TextField
                    label="CPU Request (m)"
                    type="number"
                    value={resources.cpuRequest}
                    onChange={(e) => handleResourceChange('cpuRequest', parseInt(e.target.value))}
                    fullWidth
                    size="small"
                  />
                </Grid>
                <Grid item xs={6}>
                  <TextField
                    label="CPU Limit (m)"
                    type="number"
                    value={resources.cpuLimit}
                    onChange={(e) => handleResourceChange('cpuLimit', parseInt(e.target.value))}
                    fullWidth
                    size="small"
                  />
                </Grid>
                <Grid item xs={6}>
                  <TextField
                    label="Memory Request (Mi)"
                    type="number"
                    value={resources.memoryRequest}
                    onChange={(e) => handleResourceChange('memoryRequest', parseInt(e.target.value))}
                    fullWidth
                    size="small"
                  />
                </Grid>
                <Grid item xs={6}>
                  <TextField
                    label="Memory Limit (Mi)"
                    type="number"
                    value={resources.memoryLimit}
                    onChange={(e) => handleResourceChange('memoryLimit', parseInt(e.target.value))}
                    fullWidth
                    size="small"
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    label="Storage Size (Gi)"
                    type="number"
                    value={resources.storageSize}
                    onChange={(e) => handleResourceChange('storageSize', parseInt(e.target.value))}
                    fullWidth
                    size="small"
                  />
                </Grid>
              </Grid>
            </CardContent>
            <CardActions>
              <Button variant="contained" onClick={applyChanges}>
                Update Resources
              </Button>
            </CardActions>
          </Card>
        </Grid>

        {/* Recommendations */}
        <Grid item xs={12}>
          <Alert severity="info">
            <Typography variant="h6">Optimization Recommendations</Typography>
            <ul>
              <li>Consider increasing CPU limits during peak hours (9 AM - 5 PM)</li>
              <li>Memory usage is consistently below 60% - you could reduce memory limits to save costs</li>
              <li>Enable auto-scaling to handle traffic spikes more efficiently</li>
              <li>Current configuration costs ~${costs.monthly.toFixed(2)}/month - optimized config could save 15%</li>
            </ul>
          </Alert>
        </Grid>
      </Grid>
    </Box>
  );
};

export default TenantResourceManagement;
