import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Typography,
  Box,
  Paper,
  Grid,
  Chip,
  Button,
  Tabs,
  Tab,
  List,
  ListItem,
  ListItemText,
  Divider,
  CircularProgress,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
} from '@mui/material';
import {
  Edit as EditIcon,
  Delete as DeleteIcon,
  Refresh as RefreshIcon,
  Pause as PauseIcon,
  PlayArrow as PlayArrowIcon,
} from '@mui/icons-material';
import { Line } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';
import { fetchTenant, deleteTenant } from '../services/api';

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend
);

const TabPanel = (props) => {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`tenant-tabpanel-${index}`}
      aria-labelledby={`tenant-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
};

const TenantDetail = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [tenant, setTenant] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [tabValue, setTabValue] = useState(0);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);

  useEffect(() => {
    const loadTenant = async () => {
      try {
        const data = await fetchTenant(id);
        setTenant(data);
        setLoading(false);
      } catch (err) {
        setError(err.message);
        setLoading(false);
      }
    };

    loadTenant();
  }, [id]);

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  const handleEdit = () => {
    navigate(`/tenants/${id}/edit`);
  };

  const handleDeleteClick = () => {
    setDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = async () => {
    try {
      await deleteTenant(id);
      navigate('/tenants');
    } catch (err) {
      setError(err.message);
    }
  };

  const handleDeleteCancel = () => {
    setDeleteDialogOpen(false);
  };

  // Mock data for charts
  const cpuUsageData = {
    labels: ['1h ago', '45m ago', '30m ago', '15m ago', 'Now'],
    datasets: [
      {
        label: 'CPU Usage (%)',
        data: [25, 30, 45, 35, 40],
        borderColor: 'rgba(54, 162, 235, 1)',
        backgroundColor: 'rgba(54, 162, 235, 0.2)',
      },
    ],
  };

  const memoryUsageData = {
    labels: ['1h ago', '45m ago', '30m ago', '15m ago', 'Now'],
    datasets: [
      {
        label: 'Memory Usage (%)',
        data: [40, 45, 50, 55, 60],
        borderColor: 'rgba(255, 99, 132, 1)',
        backgroundColor: 'rgba(255, 99, 132, 0.2)',
      },
    ],
  };

  // Mock tenant data for development
  const mockTenant = {
    id: id,
    name: id === 'demo' ? 'Demo Tenant' : 'Test Tenant',
    subdomain: id,
    status: 'active',
    createdAt: '2023-05-15T10:30:00Z',
    url: `https://${id}.architrave.com`,
    pods: [
      { name: `tenant-${id}-frontend`, status: 'Running', restarts: 0 },
      { name: `tenant-${id}-backend`, status: 'Running', restarts: 0 },
      { name: `tenant-${id}-rabbitmq`, status: 'Running', restarts: 0 },
    ],
    database: {
      name: `tenant_${id}`,
      host: 'production-architrave-db-new.cpmagwki2kv8.eu-central-1.rds.amazonaws.com',
      port: 3306,
      status: 'Connected',
    },
    s3Bucket: `tenant-${id}-assets`,
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" height="100%">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return <Typography color="error">Error: {error}</Typography>;
  }

  // Use mock data if tenant is null
  const tenantData = tenant || mockTenant;

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4">{tenantData.name}</Typography>
        <Box>
          <Button
            variant="outlined"
            color="primary"
            startIcon={<EditIcon />}
            onClick={handleEdit}
            sx={{ mr: 1 }}
          >
            Edit
          </Button>
          <Button
            variant="outlined"
            color="error"
            startIcon={<DeleteIcon />}
            onClick={handleDeleteClick}
          >
            Delete
          </Button>
        </Box>
      </Box>

      <Paper sx={{ mb: 3 }}>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={tabValue} onChange={handleTabChange} aria-label="tenant tabs">
            <Tab label="Overview" id="tenant-tab-0" aria-controls="tenant-tabpanel-0" />
            <Tab label="Monitoring" id="tenant-tab-1" aria-controls="tenant-tabpanel-1" />
            <Tab label="Resources" id="tenant-tab-2" aria-controls="tenant-tabpanel-2" />
            <Tab label="Logs" id="tenant-tab-3" aria-controls="tenant-tabpanel-3" />
          </Tabs>
        </Box>

        <TabPanel value={tabValue} index={0}>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Typography variant="h6" gutterBottom>
                Tenant Information
              </Typography>
              <List>
                <ListItem>
                  <ListItemText primary="ID" secondary={tenantData.id} />
                </ListItem>
                <Divider />
                <ListItem>
                  <ListItemText primary="Name" secondary={tenantData.name} />
                </ListItem>
                <Divider />
                <ListItem>
                  <ListItemText primary="Subdomain" secondary={tenantData.subdomain} />
                </ListItem>
                <Divider />
                <ListItem>
                  <ListItemText primary="URL" secondary={tenantData.url} />
                </ListItem>
                <Divider />
                <ListItem>
                  <ListItemText
                    primary="Status"
                    secondary={
                      <Chip
                        label={tenantData.status}
                        color={
                          tenantData.status === 'active'
                            ? 'success'
                            : tenantData.status === 'inactive'
                            ? 'warning'
                            : 'error'
                        }
                        size="small"
                      />
                    }
                  />
                </ListItem>
                <Divider />
                <ListItem>
                  <ListItemText
                    primary="Created At"
                    secondary={new Date(tenantData.createdAt).toLocaleString()}
                  />
                </ListItem>
              </List>
            </Grid>
            <Grid item xs={12} md={6}>
              <Typography variant="h6" gutterBottom>
                Actions
              </Typography>
              <Box display="flex" flexDirection="column" gap={2}>
                <Button
                  variant="contained"
                  color="primary"
                  startIcon={<RefreshIcon />}
                  fullWidth
                >
                  Restart Tenant
                </Button>
                <Button
                  variant="contained"
                  color={tenantData.status === 'active' ? 'warning' : 'success'}
                  startIcon={
                    tenantData.status === 'active' ? <PauseIcon /> : <PlayArrowIcon />
                  }
                  fullWidth
                >
                  {tenantData.status === 'active' ? 'Suspend Tenant' : 'Activate Tenant'}
                </Button>
              </Box>
            </Grid>
          </Grid>
        </TabPanel>

        <TabPanel value={tabValue} index={1}>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Typography variant="h6" gutterBottom>
                CPU Usage
              </Typography>
              <Line data={cpuUsageData} />
            </Grid>
            <Grid item xs={12} md={6}>
              <Typography variant="h6" gutterBottom>
                Memory Usage
              </Typography>
              <Line data={memoryUsageData} />
            </Grid>
          </Grid>
        </TabPanel>

        <TabPanel value={tabValue} index={2}>
          <Typography variant="h6" gutterBottom>
            Kubernetes Pods
          </Typography>
          <List>
            {tenantData.pods.map((pod) => (
              <React.Fragment key={pod.name}>
                <ListItem>
                  <ListItemText
                    primary={pod.name}
                    secondary={`Status: ${pod.status}, Restarts: ${pod.restarts}`}
                  />
                  <Chip
                    label={pod.status}
                    color={pod.status === 'Running' ? 'success' : 'error'}
                    size="small"
                  />
                </ListItem>
                <Divider />
              </React.Fragment>
            ))}
          </List>

          <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
            Database
          </Typography>
          <List>
            <ListItem>
              <ListItemText primary="Name" secondary={tenantData.database.name} />
            </ListItem>
            <Divider />
            <ListItem>
              <ListItemText primary="Host" secondary={tenantData.database.host} />
            </ListItem>
            <Divider />
            <ListItem>
              <ListItemText primary="Port" secondary={tenantData.database.port} />
            </ListItem>
            <Divider />
            <ListItem>
              <ListItemText
                primary="Status"
                secondary={
                  <Chip
                    label={tenantData.database.status}
                    color={
                      tenantData.database.status === 'Connected' ? 'success' : 'error'
                    }
                    size="small"
                  />
                }
              />
            </ListItem>
          </List>

          <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
            S3 Bucket
          </Typography>
          <List>
            <ListItem>
              <ListItemText primary="Name" secondary={tenantData.s3Bucket} />
            </ListItem>
          </List>
        </TabPanel>

        <TabPanel value={tabValue} index={3}>
          <Typography variant="h6" gutterBottom>
            Logs
          </Typography>
          <Paper
            sx={{
              p: 2,
              backgroundColor: '#f5f5f5',
              fontFamily: 'monospace',
              height: 400,
              overflow: 'auto',
            }}
          >
            <pre>
              {`[2023-05-15T10:30:00Z] INFO: Tenant ${tenantData.id} created
[2023-05-15T10:30:05Z] INFO: Namespace tenant-${tenantData.id} created
[2023-05-15T10:30:10Z] INFO: Database tenant_${tenantData.id} created
[2023-05-15T10:30:15Z] INFO: S3 bucket tenant-${tenantData.id}-assets created
[2023-05-15T10:30:20Z] INFO: Frontend deployment created
[2023-05-15T10:30:25Z] INFO: Backend deployment created
[2023-05-15T10:30:30Z] INFO: RabbitMQ deployment created
[2023-05-15T10:30:35Z] INFO: Istio configuration applied
[2023-05-15T10:30:40Z] INFO: Monitoring setup completed
[2023-05-15T10:30:45Z] INFO: Autoscaling setup completed
[2023-05-15T10:30:50Z] INFO: Tenant ${tenantData.id} is ready`}
            </pre>
          </Paper>
        </TabPanel>
      </Paper>

      <Dialog
        open={deleteDialogOpen}
        onClose={handleDeleteCancel}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
      >
        <DialogTitle id="alert-dialog-title">Confirm Deletion</DialogTitle>
        <DialogContent>
          <DialogContentText id="alert-dialog-description">
            Are you sure you want to delete tenant "{tenantData.name}"? This action cannot be undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleDeleteCancel}>Cancel</Button>
          <Button onClick={handleDeleteConfirm} color="error" autoFocus>
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default TenantDetail;
