import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Typography,
  Box,
  Paper,
  TextField,
  Button,
  Grid,
  FormControlLabel,
  Checkbox,
  Stepper,
  Step,
  StepLabel,
  CircularProgress,
  Alert,
} from '@mui/material';
import { createTenant } from '../services/api';

const steps = ['Basic Information', 'Configuration', 'Confirmation'];

const CreateTenant = () => {
  const navigate = useNavigate();
  const [activeStep, setActiveStep] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [formData, setFormData] = useState({
    tenantId: '',
    tenantName: '',
    subdomain: '',
    environment: 'production',
    language: 'en',
    documentClassSet: 'standard',
    enableDms: false,
    enableExternalApi: false,
    enableHeapTracking: false,
    skipDbImport: false,
    skipS3Setup: false,
    skipIstio: false,
  });

  const handleChange = (e) => {
    const { name, value, checked, type } = e.target;
    setFormData({
      ...formData,
      [name]: type === 'checkbox' ? checked : value,
    });
  };

  const handleNext = () => {
    setActiveStep((prevActiveStep) => prevActiveStep + 1);
  };

  const handleBack = () => {
    setActiveStep((prevActiveStep) => prevActiveStep - 1);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      await createTenant(formData);
      setLoading(false);
      navigate('/tenants');
    } catch (err) {
      setError(err.message);
      setLoading(false);
    }
  };

  const validateStep = () => {
    if (activeStep === 0) {
      return formData.tenantId && formData.tenantName && formData.subdomain;
    }
    return true;
  };

  const getStepContent = (step) => {
    switch (step) {
      case 0:
        return (
          <Grid container spacing={3}>
            <Grid item xs={12} md={4}>
              <TextField
                name="tenantId"
                label="Tenant ID"
                value={formData.tenantId}
                onChange={handleChange}
                fullWidth
                required
                helperText="Unique identifier for the tenant (lowercase, no spaces)"
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <TextField
                name="tenantName"
                label="Tenant Name"
                value={formData.tenantName}
                onChange={handleChange}
                fullWidth
                required
                helperText="Display name for the tenant"
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <TextField
                name="subdomain"
                label="Subdomain"
                value={formData.subdomain}
                onChange={handleChange}
                fullWidth
                required
                helperText="Subdomain for tenant access (e.g., subdomain.architrave.com)"
              />
            </Grid>
          </Grid>
        );
      case 1:
        return (
          <Grid container spacing={3}>
            <Grid item xs={12} md={4}>
              <TextField
                name="environment"
                label="Environment"
                value={formData.environment}
                onChange={handleChange}
                fullWidth
                helperText="Environment for the tenant"
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <TextField
                name="language"
                label="Language"
                value={formData.language}
                onChange={handleChange}
                fullWidth
                helperText="Default language for the tenant"
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <TextField
                name="documentClassSet"
                label="Document Class Set"
                value={formData.documentClassSet}
                onChange={handleChange}
                fullWidth
                helperText="Document class set for the tenant"
              />
            </Grid>
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                Features
              </Typography>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={formData.enableDms}
                    onChange={handleChange}
                    name="enableDms"
                  />
                }
                label="Enable DMS"
              />
              <FormControlLabel
                control={
                  <Checkbox
                    checked={formData.enableExternalApi}
                    onChange={handleChange}
                    name="enableExternalApi"
                  />
                }
                label="Enable External API"
              />
              <FormControlLabel
                control={
                  <Checkbox
                    checked={formData.enableHeapTracking}
                    onChange={handleChange}
                    name="enableHeapTracking"
                  />
                }
                label="Enable Heap Tracking"
              />
            </Grid>
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                Advanced Options
              </Typography>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={formData.skipDbImport}
                    onChange={handleChange}
                    name="skipDbImport"
                  />
                }
                label="Skip Database Import"
              />
              <FormControlLabel
                control={
                  <Checkbox
                    checked={formData.skipS3Setup}
                    onChange={handleChange}
                    name="skipS3Setup"
                  />
                }
                label="Skip S3 Setup"
              />
              <FormControlLabel
                control={
                  <Checkbox
                    checked={formData.skipIstio}
                    onChange={handleChange}
                    name="skipIstio"
                  />
                }
                label="Skip Istio Configuration"
              />
            </Grid>
          </Grid>
        );
      case 2:
        return (
          <Box>
            <Typography variant="h6" gutterBottom>
              Tenant Information
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={6}>
                <Typography variant="subtitle1">Tenant ID:</Typography>
              </Grid>
              <Grid item xs={6}>
                <Typography>{formData.tenantId}</Typography>
              </Grid>
              <Grid item xs={6}>
                <Typography variant="subtitle1">Tenant Name:</Typography>
              </Grid>
              <Grid item xs={6}>
                <Typography>{formData.tenantName}</Typography>
              </Grid>
              <Grid item xs={6}>
                <Typography variant="subtitle1">Subdomain:</Typography>
              </Grid>
              <Grid item xs={6}>
                <Typography>{formData.subdomain}</Typography>
              </Grid>
              <Grid item xs={6}>
                <Typography variant="subtitle1">Environment:</Typography>
              </Grid>
              <Grid item xs={6}>
                <Typography>{formData.environment}</Typography>
              </Grid>
              <Grid item xs={6}>
                <Typography variant="subtitle1">Language:</Typography>
              </Grid>
              <Grid item xs={6}>
                <Typography>{formData.language}</Typography>
              </Grid>
              <Grid item xs={6}>
                <Typography variant="subtitle1">Document Class Set:</Typography>
              </Grid>
              <Grid item xs={6}>
                <Typography>{formData.documentClassSet}</Typography>
              </Grid>
              <Grid item xs={6}>
                <Typography variant="subtitle1">Features:</Typography>
              </Grid>
              <Grid item xs={6}>
                <Typography>
                  {[
                    formData.enableDms && 'DMS',
                    formData.enableExternalApi && 'External API',
                    formData.enableHeapTracking && 'Heap Tracking',
                  ]
                    .filter(Boolean)
                    .join(', ') || 'None'}
                </Typography>
              </Grid>
              <Grid item xs={6}>
                <Typography variant="subtitle1">Skip Options:</Typography>
              </Grid>
              <Grid item xs={6}>
                <Typography>
                  {[
                    formData.skipDbImport && 'Skip Database Import',
                    formData.skipS3Setup && 'Skip S3 Setup',
                    formData.skipIstio && 'Skip Istio Configuration',
                  ]
                    .filter(Boolean)
                    .join(', ') || 'None'}
                </Typography>
              </Grid>
            </Grid>
          </Box>
        );
      default:
        return 'Unknown step';
    }
  };

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Create Tenant
      </Typography>

      <Paper sx={{ p: 3, mb: 3 }}>
        <Stepper activeStep={activeStep} sx={{ mb: 4 }}>
          {steps.map((label) => (
            <Step key={label}>
              <StepLabel>{label}</StepLabel>
            </Step>
          ))}
        </Stepper>

        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        <form onSubmit={handleSubmit}>
          {getStepContent(activeStep)}

          <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 3 }}>
            {activeStep !== 0 && (
              <Button onClick={handleBack} sx={{ mr: 1 }}>
                Back
              </Button>
            )}
            {activeStep === steps.length - 1 ? (
              <Button
                variant="contained"
                color="primary"
                type="submit"
                disabled={loading}
                startIcon={loading && <CircularProgress size={20} />}
              >
                {loading ? 'Creating...' : 'Create Tenant'}
              </Button>
            ) : (
              <Button
                variant="contained"
                color="primary"
                onClick={handleNext}
                disabled={!validateStep()}
              >
                Next
              </Button>
            )}
          </Box>
        </form>
      </Paper>
    </Box>
  );
};

export default CreateTenant;
