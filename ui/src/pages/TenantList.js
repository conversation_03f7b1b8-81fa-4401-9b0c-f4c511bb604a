import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Typography,
  Button,
  Box,
  Paper,
  Chip,
  IconButton,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
} from '@mui/material';
import { DataGrid } from '@mui/x-data-grid';
import {
  Add as AddIcon,
  Visibility as VisibilityIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
} from '@mui/icons-material';
import { fetchTenants, deleteTenant } from '../services/api';

const TenantList = () => {
  const [tenants, setTenants] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [tenantToDelete, setTenantToDelete] = useState(null);
  const navigate = useNavigate();

  useEffect(() => {
    const loadTenants = async () => {
      try {
        const data = await fetchTenants();
        setTenants(data);
        setLoading(false);
      } catch (err) {
        setError(err.message);
        setLoading(false);
      }
    };

    loadTenants();
  }, []);

  const handleViewTenant = (id) => {
    navigate(`/tenants/${id}`);
  };

  const handleEditTenant = (id) => {
    navigate(`/tenants/${id}/edit`);
  };

  const handleDeleteClick = (tenant) => {
    setTenantToDelete(tenant);
    setDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = async () => {
    try {
      await deleteTenant(tenantToDelete.id);
      setTenants(tenants.filter((tenant) => tenant.id !== tenantToDelete.id));
      setDeleteDialogOpen(false);
      setTenantToDelete(null);
    } catch (err) {
      setError(err.message);
    }
  };

  const handleDeleteCancel = () => {
    setDeleteDialogOpen(false);
    setTenantToDelete(null);
  };

  const columns = [
    { field: 'id', headerName: 'ID', width: 100 },
    { field: 'name', headerName: 'Name', width: 200 },
    { field: 'subdomain', headerName: 'Subdomain', width: 150 },
    {
      field: 'status',
      headerName: 'Status',
      width: 120,
      renderCell: (params) => (
        <Chip
          label={params.value}
          color={
            params.value === 'active'
              ? 'success'
              : params.value === 'inactive'
              ? 'warning'
              : 'error'
          }
          size="small"
        />
      ),
    },
    {
      field: 'createdAt',
      headerName: 'Created At',
      width: 180,
      valueFormatter: (params) => new Date(params.value).toLocaleString(),
    },
    {
      field: 'actions',
      headerName: 'Actions',
      width: 150,
      sortable: false,
      renderCell: (params) => (
        <Box>
          <IconButton
            color="primary"
            size="small"
            onClick={() => handleViewTenant(params.row.id)}
          >
            <VisibilityIcon />
          </IconButton>
          <IconButton
            color="secondary"
            size="small"
            onClick={() => handleEditTenant(params.row.id)}
          >
            <EditIcon />
          </IconButton>
          <IconButton
            color="error"
            size="small"
            onClick={() => handleDeleteClick(params.row)}
          >
            <DeleteIcon />
          </IconButton>
        </Box>
      ),
    },
  ];

  // Mock data for development
  const mockTenants = [
    {
      id: 'demo',
      name: 'Demo Tenant',
      subdomain: 'demo',
      status: 'active',
      createdAt: '2023-05-15T10:30:00Z',
    },
    {
      id: 'test',
      name: 'Test Tenant',
      subdomain: 'test',
      status: 'active',
      createdAt: '2023-05-14T09:15:00Z',
    },
    {
      id: 'dev',
      name: 'Development Tenant',
      subdomain: 'dev',
      status: 'active',
      createdAt: '2023-05-13T14:45:00Z',
    },
    {
      id: 'staging',
      name: 'Staging Tenant',
      subdomain: 'staging',
      status: 'active',
      createdAt: '2023-05-12T11:20:00Z',
    },
    {
      id: 'inactive',
      name: 'Inactive Tenant',
      subdomain: 'inactive',
      status: 'inactive',
      createdAt: '2023-05-11T16:10:00Z',
    },
  ];

  if (loading) {
    return <Typography>Loading tenants...</Typography>;
  }

  if (error) {
    return <Typography color="error">Error: {error}</Typography>;
  }

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4">Tenants</Typography>
        <Button
          variant="contained"
          color="primary"
          startIcon={<AddIcon />}
          onClick={() => navigate('/tenants/create')}
        >
          Create Tenant
        </Button>
      </Box>
      <Paper sx={{ height: 400, width: '100%' }}>
        <DataGrid
          rows={tenants.length > 0 ? tenants : mockTenants}
          columns={columns}
          pageSize={5}
          rowsPerPageOptions={[5, 10, 20]}
          checkboxSelection
          disableSelectionOnClick
        />
      </Paper>

      <Dialog
        open={deleteDialogOpen}
        onClose={handleDeleteCancel}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
      >
        <DialogTitle id="alert-dialog-title">Confirm Deletion</DialogTitle>
        <DialogContent>
          <DialogContentText id="alert-dialog-description">
            Are you sure you want to delete tenant "{tenantToDelete?.name}"? This action cannot be undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleDeleteCancel}>Cancel</Button>
          <Button onClick={handleDeleteConfirm} color="error" autoFocus>
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default TenantList;
