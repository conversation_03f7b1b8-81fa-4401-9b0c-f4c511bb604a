import axios from 'axios';

const API_URL = process.env.REACT_APP_API_URL || '/api';

const api = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Mock data for development
const mockTenants = [
  {
    id: 'demo',
    name: 'Demo Tenant',
    subdomain: 'demo',
    status: 'active',
    createdAt: '2023-05-15T10:30:00Z',
  },
  {
    id: 'test',
    name: 'Test Tenant',
    subdomain: 'test',
    status: 'active',
    createdAt: '2023-05-14T09:15:00Z',
  },
  {
    id: 'dev',
    name: 'Development Tenant',
    subdomain: 'dev',
    status: 'active',
    createdAt: '2023-05-13T14:45:00Z',
  },
  {
    id: 'staging',
    name: 'Staging Tenant',
    subdomain: 'staging',
    status: 'active',
    createdAt: '2023-05-12T11:20:00Z',
  },
  {
    id: 'inactive',
    name: 'Inactive Tenant',
    subdomain: 'inactive',
    status: 'inactive',
    createdAt: '2023-05-11T16:10:00Z',
  },
];

// Fetch all tenants
export const fetchTenants = async () => {
  try {
    // For development, return mock data
    if (process.env.NODE_ENV === 'development') {
      return Promise.resolve(mockTenants);
    }

    const response = await api.get('/tenants');
    return response.data;
  } catch (error) {
    console.error('Error fetching tenants:', error);
    throw new Error('Failed to fetch tenants');
  }
};

// Fetch a single tenant by ID
export const fetchTenant = async (id) => {
  try {
    // For development, return mock data
    if (process.env.NODE_ENV === 'development') {
      const tenant = mockTenants.find((t) => t.id === id);
      if (tenant) {
        return Promise.resolve({
          ...tenant,
          url: `https://${tenant.subdomain}.architrave.com`,
          pods: [
            { name: `tenant-${id}-frontend`, status: 'Running', restarts: 0 },
            { name: `tenant-${id}-backend`, status: 'Running', restarts: 0 },
            { name: `tenant-${id}-rabbitmq`, status: 'Running', restarts: 0 },
          ],
          database: {
            name: `tenant_${id}`,
            host: 'production-architrave-db-new.cpmagwki2kv8.eu-central-1.rds.amazonaws.com',
            port: 3306,
            status: 'Connected',
          },
          s3Bucket: `tenant-${id}-assets`,
        });
      }
      throw new Error('Tenant not found');
    }

    const response = await api.get(`/tenants/${id}`);
    return response.data;
  } catch (error) {
    console.error(`Error fetching tenant ${id}:`, error);
    throw new Error('Failed to fetch tenant');
  }
};

// Create a new tenant
export const createTenant = async (tenantData) => {
  try {
    // For development, simulate API call
    if (process.env.NODE_ENV === 'development') {
      console.log('Creating tenant:', tenantData);
      return Promise.resolve({
        id: tenantData.tenantId,
        name: tenantData.tenantName,
        subdomain: tenantData.subdomain,
        status: 'active',
        createdAt: new Date().toISOString(),
      });
    }

    const response = await api.post('/tenants', tenantData);
    return response.data;
  } catch (error) {
    console.error('Error creating tenant:', error);
    throw new Error('Failed to create tenant');
  }
};

// Update an existing tenant
export const updateTenant = async (id, tenantData) => {
  try {
    // For development, simulate API call
    if (process.env.NODE_ENV === 'development') {
      console.log(`Updating tenant ${id}:`, tenantData);
      return Promise.resolve({
        id,
        ...tenantData,
        status: 'active',
        updatedAt: new Date().toISOString(),
      });
    }

    const response = await api.put(`/tenants/${id}`, tenantData);
    return response.data;
  } catch (error) {
    console.error(`Error updating tenant ${id}:`, error);
    throw new Error('Failed to update tenant');
  }
};

// Delete a tenant
export const deleteTenant = async (id) => {
  try {
    // For development, simulate API call
    if (process.env.NODE_ENV === 'development') {
      console.log(`Deleting tenant ${id}`);
      return Promise.resolve({ success: true });
    }

    const response = await api.delete(`/tenants/${id}`);
    return response.data;
  } catch (error) {
    console.error(`Error deleting tenant ${id}:`, error);
    throw new Error('Failed to delete tenant');
  }
};
