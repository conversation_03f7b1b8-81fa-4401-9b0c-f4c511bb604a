apiVersion: apps/v1
kind: Deployment
metadata:
  name: tenant-management-ui
  namespace: tenant-management
  labels:
    app: tenant-management-ui
spec:
  replicas: 1
  selector:
    matchLabels:
      app: tenant-management-ui
  template:
    metadata:
      labels:
        app: tenant-management-ui
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "80"
        prometheus.io/path: "/metrics"
    spec:
      containers:
      - name: tenant-management-ui
        image: 545009857703.dkr.ecr.eu-central-1.amazonaws.com/tenant-management-ui:latest
        ports:
        - containerPort: 80
          name: http
        resources:
          requests:
            cpu: 100m
            memory: 128Mi
          limits:
            cpu: 200m
            memory: 256Mi
        livenessProbe:
          httpGet:
            path: /
            port: 80
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /
            port: 80
          initialDelaySeconds: 5
          periodSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: tenant-management-ui
  namespace: tenant-management
  labels:
    app: tenant-management-ui
spec:
  ports:
  - port: 80
    targetPort: 80
    name: http
  selector:
    app: tenant-management-ui
---
apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: tenant-management-ui-vs
  namespace: tenant-management
spec:
  hosts:
  - "tenant-management.architrave.com"
  gateways:
  - istio-system/tenant-gateway
  http:
  - route:
    - destination:
        host: tenant-management-ui
        port:
          number: 80
