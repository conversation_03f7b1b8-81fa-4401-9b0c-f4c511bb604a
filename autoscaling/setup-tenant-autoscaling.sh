#!/bin/bash

# Script to set up autoscaling for a specific tenant
# This script creates HPA, VPA, and KEDA ScaledObject for a specific tenant

set -e

# Check if tenant ID is provided
if [ -z "$1" ]; then
  echo "Usage: $0 <tenant-id>"
  exit 1
fi

TENANT_ID="$1"
NAMESPACE="tenant-$TENANT_ID"

# Check if tenant namespace exists
if ! kubectl get namespace "$NAMESPACE" &>/dev/null; then
  echo "Tenant namespace $NAMESPACE does not exist"
  exit 1
fi

# Check if autoscaling namespace exists
if ! kubectl get namespace "autoscaling" &>/dev/null; then
  echo "Autoscaling namespace does not exist. Please install the autoscaling stack first."
  exit 1
fi

# Enable Goldilocks for tenant namespace
echo "Enabling Goldilocks for tenant namespace $NAMESPACE..."
kubectl label namespace $NAMESPACE goldilocks.fairwinds.com/enabled=true --overwrite

# Create HPA for tenant backend
echo "Creating HorizontalPodAutoscaler for tenant $TENANT_ID backend..."
cat <<EOF | kubectl apply -f -
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: tenant-$TENANT_ID-backend-hpa
  namespace: $NAMESPACE
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: tenant-$TENANT_ID-backend
  minReplicas: 1
  maxReplicas: 5
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 80
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
EOF

# Create KEDA ScaledObject for RabbitMQ
echo "Creating KEDA ScaledObject for tenant $TENANT_ID RabbitMQ..."
cat <<EOF | kubectl apply -f -
apiVersion: keda.sh/v1alpha1
kind: ScaledObject
metadata:
  name: tenant-$TENANT_ID-backend-rabbitmq-scaler
  namespace: $NAMESPACE
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: tenant-$TENANT_ID-backend
  minReplicaCount: 1
  maxReplicaCount: 10
  pollingInterval: 30
  cooldownPeriod: 300
  triggers:
  - type: rabbitmq
    metadata:
      protocol: http
      queueName: tenant-queue
      mode: QueueLength
      value: "5"
      host: tenant-$TENANT_ID-rabbitmq
      port: "15672"
      username: guest
      password: guest
EOF

# Create VPA for tenant backend
echo "Creating VerticalPodAutoscaler for tenant $TENANT_ID backend..."
cat <<EOF | kubectl apply -f -
apiVersion: autoscaling.k8s.io/v1
kind: VerticalPodAutoscaler
metadata:
  name: tenant-$TENANT_ID-backend-vpa
  namespace: $NAMESPACE
spec:
  targetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: tenant-$TENANT_ID-backend
  updatePolicy:
    updateMode: "Auto"
  resourcePolicy:
    containerPolicies:
    - containerName: "*"
      minAllowed:
        cpu: 50m
        memory: 100Mi
      maxAllowed:
        cpu: 1
        memory: 1Gi
      controlledResources: ["cpu", "memory"]
EOF

# Create VPA for tenant frontend
echo "Creating VerticalPodAutoscaler for tenant $TENANT_ID frontend..."
cat <<EOF | kubectl apply -f -
apiVersion: autoscaling.k8s.io/v1
kind: VerticalPodAutoscaler
metadata:
  name: tenant-$TENANT_ID-frontend-vpa
  namespace: $NAMESPACE
spec:
  targetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: tenant-$TENANT_ID-frontend
  updatePolicy:
    updateMode: "Auto"
  resourcePolicy:
    containerPolicies:
    - containerName: "*"
      minAllowed:
        cpu: 50m
        memory: 100Mi
      maxAllowed:
        cpu: 1
        memory: 1Gi
      controlledResources: ["cpu", "memory"]
EOF

# Create VPA for tenant RabbitMQ
echo "Creating VerticalPodAutoscaler for tenant $TENANT_ID RabbitMQ..."
cat <<EOF | kubectl apply -f -
apiVersion: autoscaling.k8s.io/v1
kind: VerticalPodAutoscaler
metadata:
  name: tenant-$TENANT_ID-rabbitmq-vpa
  namespace: $NAMESPACE
spec:
  targetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: tenant-$TENANT_ID-rabbitmq
  updatePolicy:
    updateMode: "Auto"
  resourcePolicy:
    containerPolicies:
    - containerName: "*"
      minAllowed:
        cpu: 50m
        memory: 100Mi
      maxAllowed:
        cpu: 1
        memory: 1Gi
      controlledResources: ["cpu", "memory"]
EOF

echo "Autoscaling setup for tenant $TENANT_ID completed successfully!"
