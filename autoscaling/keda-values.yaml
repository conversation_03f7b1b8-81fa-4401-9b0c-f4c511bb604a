## KEDA configuration for tenant autoscaling
## This file is used to configure KEDA for tenant autoscaling

keda:
  metricsServer:
    useHostNetwork: false
    replicaCount: 1
    podAnnotations:
      prometheus.io/scrape: "true"
      prometheus.io/port: "8080"
    resources:
      limits:
        cpu: 1000m
        memory: 1000Mi
      requests:
        cpu: 100m
        memory: 100Mi

  operator:
    replicaCount: 1
    podAnnotations:
      prometheus.io/scrape: "true"
      prometheus.io/port: "8080"
    resources:
      limits:
        cpu: 1000m
        memory: 1000Mi
      requests:
        cpu: 100m
        memory: 100Mi

  serviceAccount:
    create: true
    name: keda-operator

  prometheus:
    metricServer:
      enabled: true
      port: 8080
      portName: metrics
      path: /metrics
    operator:
      enabled: true
      port: 8080
      portName: metrics
      path: /metrics

  webhooks:
    enabled: true

  podIdentity:
    activeDirectory:
      identity: ""
    azureWorkload:
      enabled: false
    aws:
      enabled: true
      irsa:
        enabled: true
        serviceAccountName: keda-operator
        roleArn: ""
