#!/bin/bash

# Script to install the autoscaling stack for tenant autoscaling
# This script installs KEDA, VPA, Goldilocks, and Karpenter for tenant autoscaling

set -e

# Create autoscaling namespace
kubectl create namespace autoscaling --dry-run=client -o yaml | kubectl apply -f -

# Add Helm repositories
echo "Adding Helm repositories..."
helm repo add kedacore https://kedacore.github.io/charts
helm repo add fairwinds-stable https://charts.fairwinds.com/stable
helm repo add karpenter https://charts.karpenter.sh
helm repo update

# Install KEDA
echo "Installing KEDA..."
helm upgrade --install keda kedacore/keda \
  --namespace autoscaling \
  -f keda-values.yaml \
  --wait

# Install VPA
echo "Installing VPA..."
helm upgrade --install vpa fairwinds-stable/vpa \
  --namespace autoscaling \
  -f vpa-values.yaml \
  --wait

# Install Goldilocks
echo "Installing Goldilocks..."
helm upgrade --install goldilocks fairwinds-stable/goldilocks \
  --namespace autoscaling \
  -f goldilocks-values.yaml \
  --wait

# Install Karpenter
echo "Installing Karpenter..."
helm upgrade --install karpenter karpenter/karpenter \
  --namespace autoscaling \
  -f karpenter-values.yaml \
  --wait

# Create Karpenter Provisioner
echo "Creating Karpenter Provisioner..."
cat <<EOF | kubectl apply -f -
apiVersion: karpenter.sh/v1alpha5
kind: Provisioner
metadata:
  name: default
spec:
  requirements:
    - key: karpenter.sh/capacity-type
      operator: In
      values: ["on-demand"]
    - key: kubernetes.io/arch
      operator: In
      values: ["amd64"]
    - key: node.kubernetes.io/instance-type
      operator: In
      values: ["t3.medium", "t3.large", "m5.large", "m5.xlarge"]
  limits:
    resources:
      cpu: 100
      memory: 100Gi
  providerRef:
    name: default
  ttlSecondsAfterEmpty: 30
---
apiVersion: karpenter.k8s.aws/v1alpha1
kind: AWSNodeTemplate
metadata:
  name: default
spec:
  subnetSelector:
    karpenter.sh/discovery: "true"
  securityGroupSelector:
    karpenter.sh/discovery: "true"
  tags:
    karpenter.sh/discovery: "true"
EOF

# Create HPA for tenant deployments
echo "Creating HorizontalPodAutoscaler for tenant deployments..."
cat <<EOF | kubectl apply -f -
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: tenant-backend-hpa
  namespace: autoscaling
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: tenant-backend
  minReplicas: 1
  maxReplicas: 5
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 80
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
EOF

# Create KEDA ScaledObject for RabbitMQ
echo "Creating KEDA ScaledObject for RabbitMQ..."
cat <<EOF | kubectl apply -f -
apiVersion: keda.sh/v1alpha1
kind: ScaledObject
metadata:
  name: tenant-backend-rabbitmq-scaler
  namespace: autoscaling
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: tenant-backend
  minReplicaCount: 1
  maxReplicaCount: 10
  pollingInterval: 30
  cooldownPeriod: 300
  triggers:
  - type: rabbitmq
    metadata:
      protocol: http
      queueName: tenant-queue
      mode: QueueLength
      value: "5"
      host: tenant-rabbitmq
      port: "15672"
      username: guest
      password: guest
EOF

# Create VPA for tenant deployments
echo "Creating VerticalPodAutoscaler for tenant deployments..."
cat <<EOF | kubectl apply -f -
apiVersion: autoscaling.k8s.io/v1
kind: VerticalPodAutoscaler
metadata:
  name: tenant-backend-vpa
  namespace: autoscaling
spec:
  targetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: tenant-backend
  updatePolicy:
    updateMode: "Auto"
  resourcePolicy:
    containerPolicies:
    - containerName: "*"
      minAllowed:
        cpu: 50m
        memory: 100Mi
      maxAllowed:
        cpu: 1
        memory: 1Gi
      controlledResources: ["cpu", "memory"]
EOF

# Enable Goldilocks for tenant namespaces
echo "Enabling Goldilocks for tenant namespaces..."
kubectl label namespace --all goldilocks.fairwinds.com/enabled=true --overwrite

echo "Autoscaling stack installed successfully!"
echo "Access Goldilocks dashboard using: kubectl port-forward -n autoscaling svc/goldilocks-dashboard 8080:80"
