## Goldilocks configuration for tenant autoscaling
## This file is used to configure Goldilocks for tenant autoscaling

vpa:
  enabled: false

dashboard:
  replicaCount: 1
  service:
    type: ClusterIP
    port: 80
  resources:
    limits:
      cpu: 200m
      memory: 500Mi
    requests:
      cpu: 50m
      memory: 200Mi
  podAnnotations:
    prometheus.io/scrape: "true"
    prometheus.io/port: "8080"

controller:
  replicaCount: 1
  resources:
    limits:
      cpu: 200m
      memory: 500Mi
    requests:
      cpu: 50m
      memory: 200Mi
  podAnnotations:
    prometheus.io/scrape: "true"
    prometheus.io/port: "8080"
  exclude:
    - kube-system
    - monitoring
    - istio-system
    - cert-manager
    - autoscaling
