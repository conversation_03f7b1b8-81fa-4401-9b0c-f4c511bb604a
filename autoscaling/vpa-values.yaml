## VPA configuration for tenant autoscaling
## This file is used to configure VPA for tenant autoscaling

recommender:
  enabled: true
  extraArgs:
    pod-recommendation-min-cpu-millicores: 10
    pod-recommendation-min-memory-mb: 50
  resources:
    limits:
      cpu: 200m
      memory: 1000Mi
    requests:
      cpu: 50m
      memory: 500Mi
  podAnnotations:
    prometheus.io/scrape: "true"
    prometheus.io/port: "8080"

updater:
  enabled: true
  extraArgs:
    min-replicas: 2
  resources:
    limits:
      cpu: 200m
      memory: 1000Mi
    requests:
      cpu: 50m
      memory: 500Mi
  podAnnotations:
    prometheus.io/scrape: "true"
    prometheus.io/port: "8080"

admissionController:
  enabled: true
  resources:
    limits:
      cpu: 200m
      memory: 500Mi
    requests:
      cpu: 50m
      memory: 200Mi
  podAnnotations:
    prometheus.io/scrape: "true"
    prometheus.io/port: "8080"
