#!/bin/bash

# <PERSON>ript to clean up autoscaling for a specific tenant
# This script removes HPA, VPA, and KEDA ScaledObject for a specific tenant

set -e

# Check if tenant ID is provided
if [ -z "$1" ]; then
  echo "Usage: $0 <tenant-id>"
  exit 1
fi

TENANT_ID="$1"
NAMESPACE="tenant-$TENANT_ID"

# Check if tenant namespace exists
if ! kubectl get namespace "$NAMESPACE" &>/dev/null; then
  echo "Tenant namespace $NAMESPACE does not exist"
  exit 1
fi

# Disable Goldilocks for tenant namespace
echo "Disabling Goldilocks for tenant namespace $NAMESPACE..."
kubectl label namespace $NAMESPACE goldilocks.fairwinds.com/enabled- --overwrite

# Delete HPA for tenant backend
echo "Deleting HorizontalPodAutoscaler for tenant $TENANT_ID backend..."
kubectl delete hpa tenant-$TENANT_ID-backend-hpa -n $NAMESPACE --ignore-not-found

# Delete KEDA ScaledObject for RabbitMQ
echo "Deleting KEDA ScaledObject for tenant $TENANT_ID RabbitMQ..."
kubectl delete scaledobject tenant-$TENANT_ID-backend-rabbitmq-scaler -n $NAMESPACE --ignore-not-found

# Delete VPA for tenant backend
echo "Deleting VerticalPodAutoscaler for tenant $TENANT_ID backend..."
kubectl delete vpa tenant-$TENANT_ID-backend-vpa -n $NAMESPACE --ignore-not-found

# Delete VPA for tenant frontend
echo "Deleting VerticalPodAutoscaler for tenant $TENANT_ID frontend..."
kubectl delete vpa tenant-$TENANT_ID-frontend-vpa -n $NAMESPACE --ignore-not-found

# Delete VPA for tenant RabbitMQ
echo "Deleting VerticalPodAutoscaler for tenant $TENANT_ID RabbitMQ..."
kubectl delete vpa tenant-$TENANT_ID-rabbitmq-vpa -n $NAMESPACE --ignore-not-found

echo "Autoscaling cleanup for tenant $TENANT_ID completed successfully!"
