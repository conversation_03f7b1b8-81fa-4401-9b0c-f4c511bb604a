## Karpenter configuration for tenant autoscaling
## This file is used to configure Karpenter for tenant autoscaling

serviceAccount:
  create: true
  name: karpenter
  annotations:
    eks.amazonaws.com/role-arn: ""

settings:
  aws:
    clusterName: production-wks
    clusterEndpoint: ""
    defaultInstanceProfile: KarpenterNodeInstanceProfile
    interruptionQueueName: ""

controller:
  resources:
    limits:
      cpu: 1000m
      memory: 1Gi
    requests:
      cpu: 100m
      memory: 256Mi

  logLevel: info

  env:
    - name: AWS_REGION
      value: eu-central-1

  podAnnotations:
    prometheus.io/scrape: "true"
    prometheus.io/port: "8080"

metrics:
  enabled: true
  service:
    port: 8080
    portName: metrics
    type: ClusterIP
