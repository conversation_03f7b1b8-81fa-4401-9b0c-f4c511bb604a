apiVersion: v1
kind: Pod
metadata:
  name: debug-backend
  namespace: tenant-tech-solutions
spec:
  containers:
  - name: backend
    image: 545009857703.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:2.0.57-test
    command: ["/bin/bash"]
    args: ["-c", "sleep 3600"]
    securityContext:
      runAsUser: 0
      runAsGroup: 0
      allowPrivilegeEscalation: true
      readOnlyRootFilesystem: false
    resources:
      requests:
        memory: "64Mi"
        cpu: "100m"
      limits:
        memory: "512Mi"
        cpu: "500m"
    env:
    - name: DB_HOST
      value: "architrave-aurora-cluster.cluster-cjbgvhqvlies.eu-central-1.rds.amazonaws.com"
    - name: DB_USER
      value: "architrave"
    - name: DB_PASSWORD
      value: "architrave123"
    - name: DB_NAME
      value: "architrave"
    - name: API_KEY_USER_EMAIL
      value: "<EMAIL>"
    - name: SCIM_USER_EMAIL
      value: "<EMAIL>"
    volumeMounts:
    - name: cli-config
      mountPath: /storage/ArchAssets/config/autoload/local.php
      subPath: local.php
  volumes:
  - name: cli-config
    configMap:
      name: cli-config
  restartPolicy: Never
