name: "Terraform Infrastructure Validation"

on:
  push:
    branches:
      - main
      - develop
  pull_request:
    branches:
      - main
      - develop

env:
  TF_VERSION: "1.11.2"
  AWS_REGION: "eu-central-1"
  ENVIRONMENT: "production"
  TF_VAR_skip_k8s_connection: "true"
  TF_VAR_skip_kubernetes_resources: "true"
  TF_VAR_check_if_cluster_exists: "true"
  TF_VAR_is_ci_cd: "true"

jobs:
  validate:
    name: "Validate"
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v2
        with:
          terraform_version: ${{ env.TF_VERSION }}

      - name: Run Terraform Validation
        run: ./scripts/terraform/terraform-run.sh --operation validate --ci-mode --verbose

  plan:
    name: "Plan"
    needs: validate
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v2
        with:
          terraform_version: ${{ env.TF_VERSION }}

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}

      - name: Run Terraform Plan
        run: ./scripts/terraform/terraform-run.sh --operation plan --ci-mode --verbose
        
      - name: Upload Terraform Plan
        uses: actions/upload-artifact@v3
        with:
          name: tfplan
          path: |
            tfplan
            terraform_state.json

  output:
    name: "Output"
    needs: plan
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v2
        with:
          terraform_version: ${{ env.TF_VERSION }}

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}

      - name: Download Terraform Plan
        uses: actions/download-artifact@v3
        with:
          name: tfplan

      - name: Run Terraform Output
        run: ./scripts/terraform/terraform-run.sh --operation output --ci-mode --verbose

      - name: Install terraform-docs
        run: |
          curl -sSLo ./terraform-docs.tar.gz https://terraform-docs.io/dl/v0.16.0/terraform-docs-v0.16.0-$(uname)-amd64.tar.gz
          tar -xzf terraform-docs.tar.gz
          chmod +x terraform-docs
          sudo mv terraform-docs /usr/local/bin/terraform-docs

      - name: Generate Terraform Documentation
        run: terraform-docs markdown . > TERRAFORM.md

      - name: Upload Terraform Outputs and Documentation
        uses: actions/upload-artifact@v3
        with:
          name: terraform-outputs
          path: |
            terraform_outputs.json
            TERRAFORM.md

  show_plan:
    name: "Show Plan"
    needs: plan
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v2
        with:
          terraform_version: ${{ env.TF_VERSION }}

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}

      - name: Download Terraform Plan
        uses: actions/download-artifact@v3
        with:
          name: tfplan

      - name: Show Terraform Plan
        run: |
          terraform init -reconfigure
          terraform show tfplan > terraform_plan_output.txt

      - name: Upload Plan Output
        uses: actions/upload-artifact@v3
        with:
          name: terraform-plan-output
          path: terraform_plan_output.txt
