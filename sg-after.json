{"SecurityGroups": [{"GroupId": "sg-04b66ff3894aedf98", "IpPermissionsEgress": [{"IpProtocol": "tcp", "FromPort": 80, "ToPort": 80, "UserIdGroupPairs": [], "IpRanges": [{"Description": "Allow HTTP outbound traffic to EKS cluster", "CidrIp": "10.0.0.0/16"}], "Ipv6Ranges": [], "PrefixListIds": []}, {"IpProtocol": "tcp", "FromPort": 443, "ToPort": 443, "UserIdGroupPairs": [], "IpRanges": [{"Description": "Allow HTTPS outbound traffic to EKS cluster", "CidrIp": "10.0.0.0/16"}], "Ipv6Ranges": [], "PrefixListIds": [{"Description": "Allow HTTPS outbound traffic for S3", "PrefixListId": "pl-6ea54007"}, {"Description": "Allow HTTPS outbound traffic for DynamoDB", "PrefixListId": "pl-66a5400f"}]}], "Tags": [{"Key": "Name", "Value": "production-alb-sg"}, {"Key": "Owner", "Value": "DevOps Team"}, {"Key": "Project", "Value": "tenant-management"}, {"Key": "DataClassification", "Value": "Internal"}, {"Key": "ManagedBy", "Value": "Terraform"}, {"Key": "CostCenter", "Value": "Engineering"}, {"Key": "Compliance", "Value": "SOC2"}, {"Key": "Environment", "Value": "production"}, {"Key": "CreationDate", "Value": "2025-07-14T20:38:42Z"}], "VpcId": "vpc-0e937ae9134a95ede", "SecurityGroupArn": "arn:aws:ec2:eu-central-1:545009857703:security-group/sg-04b66ff3894aedf98", "OwnerId": "545009857703", "GroupName": "production-alb-sg", "Description": "Security group for the ALB", "IpPermissions": [{"IpProtocol": "tcp", "FromPort": 80, "ToPort": 80, "UserIdGroupPairs": [], "IpRanges": [{"Description": "Allow HTTP traffic from specified CIDR blocks", "CidrIp": "10.0.0.0/16"}, {"CidrIp": "0.0.0.0/0"}], "Ipv6Ranges": [], "PrefixListIds": []}, {"IpProtocol": "tcp", "FromPort": 443, "ToPort": 443, "UserIdGroupPairs": [], "IpRanges": [{"Description": "Allow HTTPS traffic from specified CIDR blocks", "CidrIp": "10.0.0.0/16"}, {"CidrIp": "0.0.0.0/0"}], "Ipv6Ranges": [], "PrefixListIds": []}]}]}