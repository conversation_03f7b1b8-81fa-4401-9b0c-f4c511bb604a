apiVersion: apps/v1
kind: Deployment
metadata:
  name: demo-backend
  namespace: tenant-demo
  labels:
    app: demo-backend
    tenant: demo
spec:
  replicas: 1
  selector:
    matchLabels:
      app: demo-backend
  template:
    metadata:
      labels:
        app: demo-backend
        tenant: demo
    spec:
      containers:
      - name: backend
        image: nginx:alpine
        ports:
        - containerPort: 80
        env:
        - name: TENANT_ID
          valueFrom:
            configMapKeyRef:
              name: demo-config
              key: TENANT_ID
        - name: TENANT_NAME
          valueFrom:
            configMapKeyRef:
              name: demo-config
              key: TENANT_NAME
        - name: DB_HOST
          valueFrom:
            secretKeyRef:
              name: demo-secret
              key: DB_HOST
        resources:
          requests:
            cpu: 100m
            memory: 128Mi
          limits:
            cpu: 200m
            memory: 256Mi
---
apiVersion: v1
kind: Service
metadata:
  name: demo-backend-service
  namespace: tenant-demo
  labels:
    app: demo-backend
spec:
  selector:
    app: demo-backend
  ports:
  - port: 80
    targetPort: 80
    protocol: TCP
  type: ClusterIP
