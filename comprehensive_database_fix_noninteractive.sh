#!/bin/bash

# Comprehensive Database Fix Script (Non-Interactive)
# This script fixes all database schema issues and runs maintenance commands

set -e

NAMESPACE="tenant-test-s3-fixed"
POD_NAME=$(kubectl get pods -n $NAMESPACE -l app=test-s3-fixed-backend -o jsonpath='{.items[0].metadata.name}')

echo "🔧 Starting comprehensive database fix for namespace: $NAMESPACE"
echo "📦 Using pod: $POD_NAME"

# Function to execute commands in the pod non-interactively
exec_in_pod() {
    local cmd="$1"
    echo "🔄 Executing: $cmd"
    kubectl exec $POD_NAME -n $NAMESPACE -- bash -c "cd /tmp/c901de2bbc014cfa9bd15b4b1d4534d4/shared-app && $cmd"
}

# Function to add missing columns
add_missing_column() {
    local table="$1"
    local column="$2"
    local definition="$3"
    
    echo "📝 Adding column $column to table $table"
    exec_in_pod "mysql -h \$DB_HOST -u \$DB_USER -p\$DB_PASS \$DB_NAME -e \"ALTER TABLE $table ADD COLUMN $column $definition;\" 2>/dev/null || echo 'Column may already exist'"
}

# Function to run maintenance command
run_maintenance() {
    local command="$1"
    local args="$2"
    
    echo "🔧 Running maintenance command: $command $args"
    exec_in_pod "php -d opcache.enable=0 -d opcache.enable_cli=0 bin/architrave.php.bin $command $args"
}

echo "📊 Step 1: Checking current database schema..."

# Add missing columns to assets table
echo "🔧 Step 2: Adding missing columns to assets table..."
add_missing_column "assets" "delphi_enabled" "TINYINT(1) DEFAULT 0"
add_missing_column "assets" "search_consistency_check" "TINYINT(1) DEFAULT 0"
add_missing_column "assets" "textracted" "TINYINT(1) DEFAULT 0"
add_missing_column "assets" "internet_media_type_mapping" "TEXT"
add_missing_column "assets" "state" "VARCHAR(50) DEFAULT 'active'"
add_missing_column "assets" "preview_status_error_counter" "INT DEFAULT 0"

# Add missing columns to documents table
echo "🔧 Step 3: Adding missing columns to documents table..."
add_missing_column "documents" "delphi_enabled" "TINYINT(1) DEFAULT 0"
add_missing_column "documents" "search_consistency_check" "TINYINT(1) DEFAULT 0"
add_missing_column "documents" "textracted" "TINYINT(1) DEFAULT 0"
add_missing_column "documents" "internet_media_type_mapping" "TEXT"
add_missing_column "documents" "state" "VARCHAR(50) DEFAULT 'active'"
add_missing_column "documents" "preview_status_error_counter" "INT DEFAULT 0"

# Add missing columns to other tables that might need them
echo "🔧 Step 4: Adding missing columns to other tables..."
add_missing_column "asset_types" "delphi_enabled" "TINYINT(1) DEFAULT 0"
add_missing_column "asset_types" "search_consistency_check" "TINYINT(1) DEFAULT 0"

echo "🔧 Step 5: Running maintenance commands..."

# Run cache clearing first
echo "🧹 Clearing caches..."
run_maintenance "cache:clear" ""

# Run database schema update
echo "🗄️ Updating database schema..."
run_maintenance "doctrine:schema:update" "--force"

# Run asset maintenance
echo "📦 Running asset maintenance..."
run_maintenance "archassets:maintenance:find-matching-assets" ""

# Run document maintenance
echo "📄 Running document maintenance..."
run_maintenance "archassets:maintenance:find-matching-documents" ""

# Run asset processing
echo "⚙️ Processing assets..."
run_maintenance "archassets:process:assets" ""

# Run document processing
echo "📋 Processing documents..."
run_maintenance "archassets:process:documents" ""

echo "✅ Comprehensive database fix completed!"
echo "📊 Final status check..."

# Final validation
echo "📋 Database tables:"
exec_in_pod "mysql -h \$DB_HOST -u \$DB_USER -p\$DB_PASS \$DB_NAME -e \"SHOW TABLES;\""

echo "📦 Assets table structure:"
exec_in_pod "mysql -h \$DB_HOST -u \$DB_USER -p\$DB_PASS \$DB_NAME -e \"DESCRIBE assets;\""

echo "📄 Documents table structure:"
exec_in_pod "mysql -h \$DB_HOST -u \$DB_USER -p\$DB_PASS \$DB_NAME -e \"DESCRIBE documents;\""

echo "🎉 All database operations completed successfully!" 