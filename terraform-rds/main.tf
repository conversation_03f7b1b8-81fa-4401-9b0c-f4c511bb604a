provider "aws" {
  region = "eu-central-1"
}

module "rds" {
  source = "../modules/rds"

  environment               = "production"
  vpc_id                    = "vpc-0e937ae9134a95ede"
  subnet_ids                = ["subnet-0a1b2c3d4e5f6g7h8", "subnet-0a1b2c3d4e5f6g7h9", "subnet-0a1b2c3d4e5f6g7h0"]
  security_group_id         = "sg-0a1b2c3d4e5f6g7h8"
  master_username           = "admin"
  master_password           = "&BZzY_<AK(=a*UhZ"
  db_name                   = "prod_architrave_db"
  use_aurora_serverless     = true
  import_existing_resources = true

  tenant_kms_key_arns = {}

  tags = {
    Environment = "production"
    Project     = "architrave"
    Terraform   = "true"
  }
}

output "rds_secret_arn" {
  description = "ARN of the RDS secret"
  value       = module.rds.master_secret_arn
}

output "rds_endpoint" {
  description = "Endpoint of the RDS instance"
  value       = module.rds.cluster_endpoint
}
