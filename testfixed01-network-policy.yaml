apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: testfixed01-isolation-policy
  namespace: tenant-testfixed01
  labels:
    app.kubernetes.io/managed-by: advanced-tenant-onboard
    tenant: testfixed01
spec:
  podSelector: {}
  policyTypes:
  - Ingress
  - Egress
  
  # INGRESS RULES - What can access this tenant
  ingress:
  # Allow traffic from Istio Gateway
  - from:
    - namespaceSelector:
        matchLabels:
          name: istio-system
    ports:
    - protocol: TCP
      port: 80
    - protocol: TCP
      port: 8080
    - protocol: TCP
      port: 443
  
  # Allow intra-namespace communication (within tenant)
  - from:
    - namespaceSelector:
        matchLabels:
          name: tenant-testfixed01
  
  # Allow monitoring from system namespaces
  - from:
    - namespaceSelector:
        matchLabels:
          name: kube-system
  - from:
    - namespaceSelector:
        matchLabels:
          name: monitoring
  
  # EGRESS RULES - What this tenant can access
  egress:
  # Allow DNS resolution
  - to: []
    ports:
    - protocol: UDP
      port: 53
    - protocol: TCP
      port: 53
  
  # Allow HTTPS outbound (for external APIs, S3, etc.)
  - to: []
    ports:
    - protocol: TCP
      port: 443
    - protocol: TCP
      port: 80
  
  # Allow database access (Aurora Serverless)
  - to: []
    ports:
    - protocol: TCP
      port: 3306
    - protocol: TCP
      port: 5432
  
  # Allow intra-namespace communication
  - to:
    - namespaceSelector:
        matchLabels:
          name: tenant-testfixed01
  
  # Block all other cross-tenant communication
  # (This is implicit - anything not explicitly allowed is denied)
---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: testfixed01-deny-cross-tenant
  namespace: tenant-testfixed01
  labels:
    app.kubernetes.io/managed-by: advanced-tenant-onboard
    tenant: testfixed01
spec:
  podSelector: {}
  policyTypes:
  - Ingress
  - Egress
  
  # EXPLICITLY DENY cross-tenant communication
  ingress:
  - from:
    - namespaceSelector:
        matchExpressions:
        - key: name
          operator: NotIn
          values:
          - tenant-testfixed01
          - istio-system
          - kube-system
          - monitoring
    ports: []  # Deny all ports
  
---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: testfixed01-istio-gateway-access
  namespace: tenant-testfixed01
  labels:
    app.kubernetes.io/managed-by: advanced-tenant-onboard
    tenant: testfixed01
spec:
  podSelector:
    matchLabels:
      app: testfixed01-frontend
  policyTypes:
  - Ingress
  
  ingress:
  # ONLY allow Istio Gateway to access frontend
  - from:
    - namespaceSelector:
        matchLabels:
          name: istio-system
      podSelector:
        matchLabels:
          istio: ingress
    ports:
    - protocol: TCP
      port: 80
    - protocol: TCP
      port: 443
