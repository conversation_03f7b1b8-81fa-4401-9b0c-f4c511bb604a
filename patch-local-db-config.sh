#!/bin/bash
set -e
CONFIG_FILE="/shared-app/config/autoload/local.php"

# Use sed to replace getenv('MYSQL_*') with getenv('DB_*')
sed -i \
    -e "s/getenv('MYSQL_HOST')/getenv('DB_HOST')/g" \
    -e "s/getenv('MYSQL_USER')/getenv('DB_USER')/g" \
    -e "s/getenv('MYSQL_PASSWORD')/getenv('DB_PASSWORD')/g" \
    -e "s/getenv('MYSQL_DATABASE')/getenv('DB_NAME')/g" \
    "$CONFIG_FILE"

echo "Patched $CONFIG_FILE to use DB_* env vars." 