# **COMPREHENSIVE VALIDATION & TESTING STRATEGY**

## **PHASE 4: VALIDATION & TESTING EXECUTION PLAN**

### **PRE-FLIGHT VALIDATION TESTS**

#### **Test 1: Database Connection Validation**
```bash
# Test database connectivity validation function
kubectl run db-test --image=mysql:8.0 --rm -it --restart=Never -- \
  mysql -h production-aurora-serverless.cluster-cpmagwki2kv8.eu-central-1.rds.amazonaws.com \
  -P 3306 -u admin -p architrave -e "SELECT 1 as connection_test;"
```

**Expected Result**: ✅ Connection successful, returns "1"
**Validation**: Confirms RDS accessibility from EKS cluster

#### **Test 2: SQL File Validation**
```bash
# Verify architrave_1.45.2.sql file integrity
aws s3 cp s3://architravetestdb/architrave_1.45.2.sql /tmp/test-schema.sql
ls -la /tmp/test-schema.sql
grep -c "CREATE TABLE" /tmp/test-schema.sql
grep -c "user_roles" /tmp/test-schema.sql
```

**Expected Result**: ✅ File exists, contains CREATE TABLE statements and user_roles references
**Validation**: Confirms SQL file integrity and required content

#### **Test 3: AWS Secrets Manager Access**
```bash
# Test AWS Secrets Manager credential retrieval
aws secretsmanager get-secret-value --secret-id production/rds/master-new --query SecretString --output text
```

**Expected Result**: ✅ Returns RDS credentials JSON
**Validation**: Confirms AWS credentials and Secrets Manager access

### **COMPONENT-SPECIFIC TESTING**

#### **Test 4: Enhanced User Roles Initialization**
```bash
# Test the enhanced user roles initialization with validation
./advanced_tenant_onboard_enhanced --tenant-id test-validation-01 --tenant-name test-validation-01 \
  --domain architrave-assets.com --skip-dns --skip-web-check --skip-s3-setup \
  --skip-istio --skip-monitoring --debug
```

**Validation Commands:**
```bash
# Check if user roles were created successfully
kubectl logs -l app=test-validation-01-roles-init --tail=50
kubectl get pods -l app=test-validation-01 -o wide
```

**Expected Results:**
- ✅ "CRITICAL SUCCESS: User roles initialization completed" in logs
- ✅ Pod completes successfully (Status: Succeeded)
- ✅ No "CRITICAL FAILURE" messages in logs

#### **Test 5: ALB Ingress Creation and SSL Integration**
```bash
# Test ALB Ingress creation with SSL certificate
./advanced_tenant_onboard_enhanced --tenant-id test-alb-ssl-01 --tenant-name test-alb-ssl-01 \
  --domain architrave-assets.com --ssl-certificate-arn arn:aws:acm:eu-central-1:************:certificate/8d306c2f-62e2-42f4-a941-562ce65cda32 \
  --skip-dns --skip-web-check --debug
```

**Validation Commands:**
```bash
# Check ALB Ingress creation and SSL configuration
kubectl get ingress -n tenant-test-alb-ssl-01 -o yaml
kubectl describe ingress test-alb-ssl-01-alb-ingress -n tenant-test-alb-ssl-01
aws elbv2 describe-load-balancers --names test-alb-ssl-01-alb
```

**Expected Results:**
- ✅ Ingress created with correct SSL certificate ARN annotation
- ✅ ALB provisioned with HTTPS listener on port 443
- ✅ Health checks configured with /health path
- ✅ WAF ACL properly attached

#### **Test 6: S3 IAM Role and IRSA Configuration**
```bash
# Test tenant-specific S3 IAM role creation
./advanced_tenant_onboard_enhanced --tenant-id test-s3-irsa-01 --tenant-name test-s3-irsa-01 \
  --domain architrave-assets.com --skip-dns --skip-web-check --debug
```

**Validation Commands:**
```bash
# Verify IAM role and policy creation
aws iam get-role --role-name tenant-test-s3-irsa-01-s3-role
aws iam list-attached-role-policies --role-name tenant-test-s3-irsa-01-s3-role
kubectl get serviceaccount test-s3-irsa-01-s3-service-account -n tenant-test-s3-irsa-01 -o yaml
```

**Expected Results:**
- ✅ Tenant-specific IAM role created with proper trust policy
- ✅ S3 policy attached with tenant-specific bucket permissions
- ✅ ServiceAccount has correct IRSA annotations
- ✅ OIDC trust relationship properly configured

#### **Test 7: Hetzner DNS Integration with Retry Logic**
```bash
# Test Hetzner DNS configuration (requires HETZNER_DNS_API_KEY)
export HETZNER_DNS_API_KEY="your-api-key-here"
./advanced_tenant_onboard_enhanced --tenant-id test-dns-retry-01 --tenant-name test-dns-retry-01 \
  --domain architrave-assets.com --enable-hetzner-dns --hetzner-zone architrave-assets.com --debug
```

**Validation Commands:**
```bash
# Verify DNS record creation and propagation
nslookup test-dns-retry-01.architrave-assets.com
dig test-dns-retry-01.architrave-assets.com
curl -I http://test-dns-retry-01.architrave-assets.com
```

**Expected Results:**
- ✅ DNS record created successfully with retry mechanism
- ✅ DNS propagation verified within 5 minutes
- ✅ CNAME record points to correct ALB endpoint
- ✅ HTTP/HTTPS access working

### **INTEGRATION TESTING**

#### **Test 8: Full End-to-End Onboarding (Target Command)**
```bash
# Execute the complete onboarding process with all enhancements
./advanced_tenant_onboard_enhanced --tenant-id testautogo18 --tenant-name testautogo18 \
  --domain architrave-assets.com --enable-auto-fix --enable-production-audit \
  --enable-hetzner-dns --ssl-certificate-arn arn:aws:acm:eu-central-1:************:certificate/8d306c2f-62e2-42f4-a941-562ce65cda32 \
  --aws-region eu-central-1 --aws-account-id ************ --hetzner-zone architrave-assets.com
```

**Comprehensive Validation Checklist:**

1. **Database Validation:**
   ```bash
   # Verify database schema and user roles
   kubectl exec -it deployment/testautogo18-backend -n tenant-testautogo18 -- \
     mysql -h production-aurora-serverless.cluster-cpmagwki2kv8.eu-central-1.rds.amazonaws.com \
     -u admin -p architrave -e "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema='architrave';"
   
   kubectl exec -it deployment/testautogo18-backend -n tenant-testautogo18 -- \
     mysql -h production-aurora-serverless.cluster-cpmagwki2kv8.eu-central-1.rds.amazonaws.com \
     -u admin -p architrave -e "SELECT role_id FROM user_roles ORDER BY id;"
   ```

2. **Kubernetes Resources Validation:**
   ```bash
   # Check all Kubernetes resources
   kubectl get all -n tenant-testautogo18
   kubectl get ingress -n tenant-testautogo18 -o yaml
   kubectl get secrets -n tenant-testautogo18
   kubectl get configmaps -n tenant-testautogo18
   kubectl get serviceaccounts -n tenant-testautogo18 -o yaml
   ```

3. **ALB and SSL Validation:**
   ```bash
   # Verify ALB configuration
   aws elbv2 describe-load-balancers --names testautogo18-alb
   aws elbv2 describe-listeners --load-balancer-arn $(aws elbv2 describe-load-balancers --names testautogo18-alb --query 'LoadBalancers[0].LoadBalancerArn' --output text)
   
   # Test SSL certificate
   curl -I https://testautogo18.architrave-assets.com
   openssl s_client -connect testautogo18.architrave-assets.com:443 -servername testautogo18.architrave-assets.com
   ```

4. **Application Health Validation:**
   ```bash
   # Test application endpoints
   curl -f http://testautogo18.architrave-assets.com/health
   curl -f https://testautogo18.architrave-assets.com/health
   curl -f https://testautogo18.architrave-assets.com/api/health
   ```

5. **S3 Access Validation:**
   ```bash
   # Test S3 access from pods
   kubectl exec -it deployment/testautogo18-backend -n tenant-testautogo18 -- \
     aws s3 ls s3://architravetestdb/tenants/testautogo18/
   ```

### **FAILURE SCENARIO TESTING**

#### **Test 9: Database Connection Failure Handling**
```bash
# Test with invalid database credentials
./advanced_tenant_onboard_enhanced --tenant-id test-db-fail-01 --tenant-name test-db-fail-01 \
  --rds-secret-name "invalid-secret-name" --domain architrave-assets.com --debug
```

**Expected Result**: ✅ Graceful failure with clear error message, automatic rollback executed

#### **Test 10: DNS API Failure Handling**
```bash
# Test with invalid Hetzner API key
export HETZNER_DNS_API_KEY="invalid-key"
./advanced_tenant_onboard_enhanced --tenant-id test-dns-fail-01 --tenant-name test-dns-fail-01 \
  --domain architrave-assets.com --enable-hetzner-dns --debug
```

**Expected Result**: ✅ DNS failure handled gracefully, tenant still accessible via ALB

#### **Test 11: Rollback Mechanism Testing**
```bash
# Simulate failure during deployment
./advanced_tenant_onboard_enhanced --tenant-id test-rollback-01 --tenant-name test-rollback-01 \
  --domain architrave-assets.com --backend-image "invalid-image:latest" --debug
```

**Expected Result**: ✅ Automatic rollback executed, namespace and resources cleaned up

### **PERFORMANCE AND RELIABILITY TESTING**

#### **Test 12: Timeout and Retry Validation**
```bash
# Test timeout handling and retry mechanisms
./advanced_tenant_onboard_enhanced --tenant-id test-timeout-01 --tenant-name test-timeout-01 \
  --domain architrave-assets.com --enable-hetzner-dns --debug
```

**Monitor for:**
- ✅ Progress logging every 30-60 seconds
- ✅ Retry attempts with exponential backoff
- ✅ Graceful timeout handling
- ✅ No infinite loops or hanging processes

### **SUCCESS CRITERIA**

**Critical Success Indicators:**
1. ✅ All "CRITICAL SUCCESS" log messages appear
2. ✅ No "CRITICAL FAILURE" messages in logs
3. ✅ HTTP 200 responses from all health endpoints
4. ✅ Database contains expected 73+ tables
5. ✅ User roles table populated with 7 default roles
6. ✅ ALB provisioned with SSL certificate
7. ✅ DNS resolution working within 5 minutes
8. ✅ S3 access working from application pods
9. ✅ All Kubernetes resources in "Ready" state
10. ✅ No pod restart loops or crash backoffs

**Performance Benchmarks:**
- ✅ Total onboarding time: < 15 minutes
- ✅ Database operations: < 3 minutes each
- ✅ ALB provisioning: < 5 minutes
- ✅ DNS propagation: < 5 minutes
- ✅ Application startup: < 2 minutes

---

**Status**: 🚀 **READY FOR COMPREHENSIVE TESTING**
**Recommendation**: Execute tests in sequence, validate each component before proceeding to integration testing.
