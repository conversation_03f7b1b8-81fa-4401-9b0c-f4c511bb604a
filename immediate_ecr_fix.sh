#!/bin/bash
# Immediate ECR Fix Script
# Fixes ECR authentication and pod scheduling issues immediately

echo "🚀 IMMEDIATE ECR FIX SCRIPT"
echo "=========================="
echo "Started at: $(date)"
echo ""

# Fix 1: Refresh ECR authentication
echo "🔧 Fix 1: Refreshing ECR authentication..."
aws ecr get-login-password --region eu-central-1 | docker login --username AWS --password-stdin 545009857703.dkr.ecr.eu-central-1.amazonaws.com
if [ $? -eq 0 ]; then
    echo "✅ ECR authentication successful"
else
    echo "❌ ECR authentication failed"
fi

# Fix 2: Get ECR password for secrets
echo "🔧 Fix 2: Getting ECR password for Kubernetes secrets..."
ECR_PASSWORD=$(aws ecr get-login-password --region eu-central-1)
if [ ! -z "$ECR_PASSWORD" ]; then
    echo "✅ ECR password obtained"
    
    # Fix 3: Update ECR secrets in all tenant namespaces
    echo "🔧 Fix 3: Updating ECR secrets in all tenant namespaces..."
    for ns in $(timeout 10 kubectl get namespaces --no-headers 2>/dev/null | grep tenant- | awk '{print $1}'); do
        echo "  Updating ECR secret in $ns..."
        timeout 15 kubectl create secret docker-registry ecr-secret \
            --docker-server=545009857703.dkr.ecr.eu-central-1.amazonaws.com \
            --docker-username=AWS \
            --docker-password=$ECR_PASSWORD \
            --namespace=$ns \
            --dry-run=client -o yaml | kubectl apply -f - 2>/dev/null
        
        if [ $? -eq 0 ]; then
            echo "    ✅ ECR secret updated in $ns"
        else
            echo "    ⚠️ ECR secret update failed in $ns"
        fi
    done
else
    echo "❌ Could not get ECR password"
fi

# Fix 4: Reduce resource requests for faster scheduling
echo "🔧 Fix 4: Reducing resource requests for faster pod scheduling..."
for ns in $(timeout 10 kubectl get namespaces --no-headers 2>/dev/null | grep tenant- | awk '{print $1}'); do
    echo "  Processing namespace: $ns"
    
    # Get deployments in namespace
    for deployment in $(timeout 10 kubectl get deployments -n $ns --no-headers 2>/dev/null | awk '{print $1}'); do
        echo "    Reducing resources for $deployment..."
        
        # Try different container names
        for container in backend frontend nginx rabbitmq; do
            timeout 10 kubectl patch deployment $deployment -n $ns -p "{\"spec\":{\"template\":{\"spec\":{\"containers\":[{\"name\":\"$container\",\"resources\":{\"requests\":{\"cpu\":\"25m\",\"memory\":\"64Mi\"},\"limits\":{\"cpu\":\"100m\",\"memory\":\"128Mi\"}}}]}}}}" 2>/dev/null
        done
        
        echo "    ✅ Resources updated for $deployment"
    done
done

# Fix 5: Restart failed and pending pods
echo "🔧 Fix 5: Restarting failed and pending pods..."
for ns in $(timeout 10 kubectl get namespaces --no-headers 2>/dev/null | grep tenant- | awk '{print $1}'); do
    echo "  Cleaning up pods in $ns..."
    
    # Delete failed pods
    timeout 10 kubectl delete pods --field-selector=status.phase=Failed -n $ns --ignore-not-found=true 2>/dev/null
    
    # Delete long-pending pods
    timeout 10 kubectl delete pods --field-selector=status.phase=Pending -n $ns --ignore-not-found=true 2>/dev/null
    
    echo "    ✅ Pods cleaned up in $ns"
done

# Fix 6: Create missing deployments with correct ECR images
echo "🔧 Fix 6: Creating missing deployments with correct ECR images..."

# Define ECR images
FRONTEND_IMAGE="545009857703.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:2.0.41"
BACKEND_IMAGE="545009857703.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:2.0.56-test"
NGINX_IMAGE="545009857703.dkr.ecr.eu-central-1.amazonaws.com/nginx_dev:1.0.6-update_ssl"
RABBITMQ_IMAGE="545009857703.dkr.ecr.eu-central-1.amazonaws.com/rabbitmq_dev:1.02"

for ns in $(timeout 10 kubectl get namespaces --no-headers 2>/dev/null | grep tenant- | awk '{print $1}'); do
    TENANT_ID=$(echo $ns | sed 's/tenant-//')
    echo "  Checking deployments for tenant: $TENANT_ID"
    
    # Check if backend deployment exists
    timeout 5 kubectl get deployment -n $ns 2>/dev/null | grep backend >/dev/null 2>&1
    if [ $? -ne 0 ]; then
        echo "    Creating backend deployment..."
        cat <<EOF | timeout 15 kubectl apply -f - 2>/dev/null
apiVersion: apps/v1
kind: Deployment
metadata:
  name: tenant-${TENANT_ID}-backend
  namespace: $ns
spec:
  replicas: 1
  selector:
    matchLabels:
      app: backend
      tenant: $TENANT_ID
  template:
    metadata:
      labels:
        app: backend
        tenant: $TENANT_ID
    spec:
      imagePullSecrets:
      - name: ecr-secret
      containers:
      - name: backend
        image: $BACKEND_IMAGE
        ports:
        - containerPort: 80
        resources:
          requests:
            memory: "64Mi"
            cpu: "25m"
          limits:
            memory: "128Mi"
            cpu: "100m"
        securityContext:
          allowPrivilegeEscalation: false
          capabilities:
            drop:
            - ALL
          runAsNonRoot: true
          runAsUser: 33
          seccompProfile:
            type: RuntimeDefault
---
apiVersion: v1
kind: Service
metadata:
  name: tenant-${TENANT_ID}-backend-service
  namespace: $ns
spec:
  selector:
    app: backend
    tenant: $TENANT_ID
  ports:
  - port: 80
    targetPort: 80
  type: ClusterIP
EOF
        echo "    ✅ Backend deployment created"
    fi
    
    # Check if frontend deployment exists
    timeout 5 kubectl get deployment -n $ns 2>/dev/null | grep frontend >/dev/null 2>&1
    if [ $? -ne 0 ]; then
        echo "    Creating frontend deployment..."
        cat <<EOF | timeout 15 kubectl apply -f - 2>/dev/null
apiVersion: apps/v1
kind: Deployment
metadata:
  name: tenant-${TENANT_ID}-frontend
  namespace: $ns
spec:
  replicas: 1
  selector:
    matchLabels:
      app: frontend
      tenant: $TENANT_ID
  template:
    metadata:
      labels:
        app: frontend
        tenant: $TENANT_ID
    spec:
      imagePullSecrets:
      - name: ecr-secret
      containers:
      - name: frontend
        image: $FRONTEND_IMAGE
        ports:
        - containerPort: 80
        resources:
          requests:
            memory: "64Mi"
            cpu: "25m"
          limits:
            memory: "128Mi"
            cpu: "100m"
        securityContext:
          allowPrivilegeEscalation: false
          capabilities:
            drop:
            - ALL
          runAsNonRoot: true
          runAsUser: 33
          seccompProfile:
            type: RuntimeDefault
---
apiVersion: v1
kind: Service
metadata:
  name: tenant-${TENANT_ID}-frontend-service
  namespace: $ns
spec:
  selector:
    app: frontend
    tenant: $TENANT_ID
  ports:
  - port: 80
    targetPort: 80
  type: ClusterIP
EOF
        echo "    ✅ Frontend deployment created"
    fi
done

# Fix 7: Wait for pods to start and verify
echo "🔧 Fix 7: Waiting for pods to start..."
sleep 30

echo "🔍 Verification: Checking final status..."
TOTAL_PODS=0
RUNNING_PODS=0

for ns in $(timeout 10 kubectl get namespaces --no-headers 2>/dev/null | grep tenant- | awk '{print $1}'); do
    PODS=$(timeout 5 kubectl get pods -n $ns --no-headers 2>/dev/null | wc -l)
    RUNNING=$(timeout 5 kubectl get pods -n $ns --no-headers 2>/dev/null | grep Running | wc -l)
    TOTAL_PODS=$((TOTAL_PODS + PODS))
    RUNNING_PODS=$((RUNNING_PODS + RUNNING))
    
    echo "  $ns: $RUNNING/$PODS pods running"
done

echo ""
echo "📊 FINAL STATUS:"
echo "  Total pods: $TOTAL_PODS"
echo "  Running pods: $RUNNING_PODS"
if [ $TOTAL_PODS -gt 0 ]; then
    SUCCESS_RATE=$(echo "scale=1; $RUNNING_PODS * 100 / $TOTAL_PODS" | bc 2>/dev/null || echo "N/A")
    echo "  Success rate: ${SUCCESS_RATE}%"
    
    if [ $(echo "$SUCCESS_RATE >= 80" | bc 2>/dev/null) -eq 1 ]; then
        echo "✅ IMMEDIATE ECR FIXES SUCCESSFUL!"
        echo "🎉 System is now significantly improved!"
    else
        echo "⚠️ IMMEDIATE ECR FIXES PARTIALLY SUCCESSFUL"
        echo "🔧 Some pods may still be starting up"
    fi
else
    echo "⚠️ No pods found or still starting"
fi

echo ""
echo "✅ Immediate ECR fixes completed at: $(date)"
echo "🔍 Run comprehensive verification to check 100% status"
