.PHONY: help terraform-init terraform-fmt terraform-validate terraform-plan terraform-output terraform-all terraform-clean terraform-docs

# Default target
help:
	@echo "Available targets:"
	@echo "  help                  - Show this help message"
	@echo "  terraform-init        - Initialize Terraform"
	@echo "  terraform-fmt         - Format Terraform code"
	@echo "  terraform-validate    - Validate Terraform code"
	@echo "  terraform-plan        - Create Terraform plan (without applying)"
	@echo "  terraform-output      - Generate Terraform outputs"
	@echo "  terraform-all         - Run all Terraform operations (init, fmt, validate, plan, output)"
	@echo "  terraform-clean       - Clean Terraform temporary files"
	@echo "  terraform-docs        - Generate Terraform documentation"

# Initialize Terraform
terraform-init:
	@echo "Initializing Terraform..."
	./scripts/terraform/terraform-run.sh --operation init --verbose

# Format Terraform code
terraform-fmt:
	@echo "Formatting Terraform code..."
	./scripts/terraform/terraform-run.sh --operation fmt --verbose

# Validate Terraform code
terraform-validate: terraform-init
	@echo "Validating Terraform code..."
	./scripts/terraform/terraform-run.sh --operation validate --verbose

# Create Terraform plan (without applying)
terraform-plan: terraform-validate
	@echo "Creating Terraform plan..."
	./scripts/terraform/terraform-run.sh --operation plan --verbose

# Generate Terraform outputs
terraform-output: terraform-init
	@echo "Generating Terraform outputs..."
	./scripts/terraform/terraform-run.sh --operation output --verbose

# Run all Terraform operations
terraform-all:
	@echo "Running all Terraform operations..."
	./scripts/terraform/terraform-run.sh --operation all --verbose

# Clean Terraform temporary files
terraform-clean:
	@echo "Cleaning Terraform temporary files..."
	rm -f tfplan terraform_state.json terraform_outputs.json
	rm -rf .terraform/

# Generate Terraform documentation
terraform-docs:
	@echo "Generating Terraform documentation..."
	@if command -v terraform-docs >/dev/null 2>&1; then \
		terraform-docs markdown . > TERRAFORM.md; \
		echo "Documentation generated: TERRAFORM.md"; \
	else \
		echo "terraform-docs not installed. Please install it first."; \
		echo "Visit: https://terraform-docs.io/user-guide/installation/"; \
		exit 1; \
	fi
