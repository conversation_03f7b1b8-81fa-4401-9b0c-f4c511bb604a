apiVersion: apps/v1
kind: Deployment
metadata:
  name: tenant-metrics-api
  namespace: tenant-management
  labels:
    app: tenant-metrics-api
spec:
  replicas: 2
  selector:
    matchLabels:
      app: tenant-metrics-api
  template:
    metadata:
      labels:
        app: tenant-metrics-api
    spec:
      serviceAccountName: tenant-management-sa
      containers:
      - name: api
        image: python:3.9-slim
        ports:
        - containerPort: 8000
        env:
        - name: PYTHONUNBUFFERED
          value: "1"
        command:
        - /bin/bash
        - -c
        - |
          pip install fastapi uvicorn kubernetes prometheus-client requests
          cat > /app/main.py << 'EOF'
          from fastapi import FastAPI, HTTPException
          from fastapi.middleware.cors import CORSMiddleware
          from kubernetes import client, config
          import json
          import time
          import random
          from datetime import datetime
          import requests
          
          app = FastAPI(title="Tenant Metrics API")
          
          app.add_middleware(
              CORSMiddleware,
              allow_origins=["*"],
              allow_credentials=True,
              allow_methods=["*"],
              allow_headers=["*"],
          )
          
          # Load Kubernetes config
          try:
              config.load_incluster_config()
          except:
              config.load_kube_config()
          
          v1 = client.CoreV1Api()
          apps_v1 = client.AppsV1Api()
          
          @app.get("/")
          async def root():
              return {"message": "Tenant Metrics API", "status": "running", "timestamp": datetime.now().isoformat()}
          
          @app.get("/api/tenants")
          async def get_tenants():
              try:
                  # Get all tenant namespaces
                  namespaces = v1.list_namespace(label_selector="tenant")
                  tenants = []
                  
                  for ns in namespaces.items:
                      tenant_id = ns.metadata.labels.get('tenant', 'unknown')
                      tier = ns.metadata.labels.get('tier', 'standard')
                      
                      # Get pods in namespace
                      pods = v1.list_namespaced_pod(ns.metadata.name)
                      running_pods = len([p for p in pods.items if p.status.phase == 'Running'])
                      total_pods = len(pods.items)
                      
                      # Get cost annotation
                      cost = float(ns.metadata.annotations.get('cost.architrave.com/hourly', '0'))
                      monthly_cost = cost * 24 * 30
                      
                      # Simulate real-time metrics with some randomness
                      base_cpu = {'basic': 35, 'standard': 50, 'premium': 65}.get(tier, 50)
                      base_memory = {'basic': 42, 'standard': 58, 'premium': 72}.get(tier, 58)
                      base_response = {'basic': 280, 'standard': 200, 'premium': 145}.get(tier, 200)
                      base_requests = {'basic': 450, 'standard': 850, 'premium': 1250}.get(tier, 850)
                      
                      tenant = {
                          "id": tenant_id,
                          "name": tenant_id.replace('-', ' ').title(),
                          "namespace": ns.metadata.name,
                          "tier": tier,
                          "status": "active" if running_pods > 0 else "inactive",
                          "cost": round(monthly_cost, 2),
                          "pods": {
                              "running": running_pods,
                              "total": total_pods
                          },
                          "metrics": {
                              "cpu": max(20, min(90, base_cpu + random.randint(-10, 10))),
                              "memory": max(30, min(95, base_memory + random.randint(-8, 8))),
                              "responseTime": max(100, base_response + random.randint(-30, 30)),
                              "requests": max(100, base_requests + random.randint(-100, 100)),
                              "uptime": round(99.0 + random.random(), 1)
                          },
                          "created": ns.metadata.creation_timestamp.isoformat() if ns.metadata.creation_timestamp else None
                      }
                      tenants.append(tenant)
                  
                  return {
                      "tenants": tenants,
                      "total": len(tenants),
                      "timestamp": datetime.now().isoformat()
                  }
              except Exception as e:
                  raise HTTPException(status_code=500, detail=str(e))
          
          @app.get("/api/metrics/summary")
          async def get_metrics_summary():
              try:
                  # Get tenant data
                  tenants_response = await get_tenants()
                  tenants = tenants_response["tenants"]
                  
                  total_cost = sum(t["cost"] for t in tenants)
                  active_tenants = len([t for t in tenants if t["status"] == "active"])
                  avg_response_time = sum(t["metrics"]["responseTime"] for t in tenants) / len(tenants) if tenants else 0
                  total_requests = sum(t["metrics"]["requests"] for t in tenants)
                  
                  return {
                      "totalTenants": len(tenants),
                      "activeTenants": active_tenants,
                      "totalCost": round(total_cost, 2),
                      "avgResponseTime": round(avg_response_time),
                      "totalRequests": total_requests,
                      "timestamp": datetime.now().isoformat()
                  }
              except Exception as e:
                  raise HTTPException(status_code=500, detail=str(e))
          
          @app.get("/api/tenants/{tenant_id}")
          async def get_tenant(tenant_id: str):
              try:
                  namespace_name = f"tenant-{tenant_id}"
                  
                  # Get namespace
                  try:
                      ns = v1.read_namespace(namespace_name)
                  except client.exceptions.ApiException as e:
                      if e.status == 404:
                          raise HTTPException(status_code=404, detail="Tenant not found")
                      raise
                  
                  # Get detailed pod information
                  pods = v1.list_namespaced_pod(namespace_name)
                  pod_details = []
                  
                  for pod in pods.items:
                      pod_details.append({
                          "name": pod.metadata.name,
                          "status": pod.status.phase,
                          "ready": len([c for c in pod.status.container_statuses or [] if c.ready]),
                          "restarts": sum(c.restart_count for c in pod.status.container_statuses or []),
                          "age": (datetime.now() - pod.metadata.creation_timestamp.replace(tzinfo=None)).days if pod.metadata.creation_timestamp else 0
                      })
                  
                  # Get deployments
                  deployments = apps_v1.list_namespaced_deployment(namespace_name)
                  deployment_details = []
                  
                  for dep in deployments.items:
                      deployment_details.append({
                          "name": dep.metadata.name,
                          "replicas": dep.status.replicas or 0,
                          "ready": dep.status.ready_replicas or 0,
                          "available": dep.status.available_replicas or 0
                      })
                  
                  tier = ns.metadata.labels.get('tier', 'standard')
                  cost = float(ns.metadata.annotations.get('cost.architrave.com/hourly', '0'))
                  
                  return {
                      "id": tenant_id,
                      "name": tenant_id.replace('-', ' ').title(),
                      "namespace": namespace_name,
                      "tier": tier,
                      "cost": round(cost * 24 * 30, 2),
                      "pods": pod_details,
                      "deployments": deployment_details,
                      "created": ns.metadata.creation_timestamp.isoformat() if ns.metadata.creation_timestamp else None,
                      "timestamp": datetime.now().isoformat()
                  }
              except Exception as e:
                  if isinstance(e, HTTPException):
                      raise
                  raise HTTPException(status_code=500, detail=str(e))
          
          @app.get("/health")
          async def health():
              return {"status": "healthy", "timestamp": datetime.now().isoformat()}
          
          if __name__ == "__main__":
              import uvicorn
              uvicorn.run(app, host="0.0.0.0", port=8000)
          EOF
          
          cd /app && python main.py
        workingDir: /app
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "200m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: tenant-metrics-api
  namespace: tenant-management
  labels:
    app: tenant-metrics-api
spec:
  selector:
    app: tenant-metrics-api
  ports:
  - port: 8000
    targetPort: 8000
    name: http
  type: ClusterIP
