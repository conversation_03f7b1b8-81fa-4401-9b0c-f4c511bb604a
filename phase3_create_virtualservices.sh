#!/bin/bash
# Phase 3: Create Istio VirtualService resources for existing tenants
set -e

echo "🚀 PHASE 3: IMPLEMENT ISTIO VIRTUALSERVICE ARCHITECTURE"
echo "======================================================="
echo ""

# Define known tenants from conversation history
TENANTS="test-final-validation test-01 test-02 test-03 cycle-06 demo-tenant test-tenant-001 test-tenant-002 test-tenant-123"

echo "Step 3.1: Ensuring Istio Gateway exists..."
cat > /tmp/tenant-gateway.yaml << 'EOF'
apiVersion: networking.istio.io/v1beta1
kind: Gateway
metadata:
  name: tenant-gateway
  namespace: istio-system
  labels:
    app: istio-ingressgateway
    managed-by: tenant-onboarding
spec:
  selector:
    istio: ingressgateway
  servers:
  - port:
      number: 80
      name: http
      protocol: HTTP
    hosts:
    - "*.architrave-assets.com"
    tls:
      httpsRedirect: true
  - port:
      number: 443
      name: https
      protocol: HTTPS
    hosts:
    - "*.architrave-assets.com"
    tls:
      mode: SIMPLE
      credentialName: architrave-assets-wildcard-cert
EOF

echo "  🚪 Creating/updating tenant-gateway..."
kubectl apply -f /tmp/tenant-gateway.yaml
echo "  ✅ Tenant gateway configured"
echo ""

echo "Step 3.2: Creating VirtualService resources for existing tenants..."
for tenant in $TENANTS; do
    namespace="tenant-$tenant"
    
    # Check if namespace exists
    if kubectl get namespace $namespace >/dev/null 2>&1; then
        echo "  📝 Creating VirtualService for tenant: $tenant"
        
        cat > /tmp/virtualservice-$tenant.yaml << EOF
apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: tenant-$tenant-vs
  namespace: $namespace
  labels:
    tenant: $tenant
    managed-by: tenant-onboarding
spec:
  hosts:
  - "$tenant.architrave-assets.com"
  gateways:
  - "istio-system/tenant-gateway"
  http:
  # API routes (backend)
  - match:
    - uri:
        prefix: "/api"
    route:
    - destination:
        host: $tenant-backend-service
        port:
          number: 8080
    retries:
      attempts: 3
      perTryTimeout: 10s
      retryOn: gateway-error,connect-failure,refused-stream
    timeout: 30s
    headers:
      request:
        set:
          X-Forwarded-Proto: "https"
  # Frontend routes (nginx)
  - route:
    - destination:
        host: $tenant-frontend-service
        port:
          number: 80
    retries:
      attempts: 2
      perTryTimeout: 5s
      retryOn: gateway-error,connect-failure,refused-stream
    timeout: 15s
    headers:
      request:
        set:
          X-Forwarded-Proto: "https"
EOF
        
        kubectl apply -f /tmp/virtualservice-$tenant.yaml
        echo "    ✅ VirtualService created for $tenant"
        
        # Clean up temp file
        rm -f /tmp/virtualservice-$tenant.yaml
    else
        echo "    ⚠️  Namespace $namespace not found - skipping $tenant"
    fi
done
echo ""

echo "Step 3.3: Verifying VirtualService creation..."
echo "  📊 VirtualService resources created:"
kubectl get virtualservice --all-namespaces | grep "tenant-.*-vs" || echo "  ❌ No VirtualService resources found"
echo ""

echo "Step 3.4: Getting Istio Gateway external endpoint..."
EXTERNAL_IP=$(kubectl get svc istio-ingressgateway -n istio-system -o jsonpath='{.status.loadBalancer.ingress[0].hostname}' 2>/dev/null || echo "")
if [ -z "$EXTERNAL_IP" ]; then
    EXTERNAL_IP=$(kubectl get svc istio-ingressgateway -n istio-system -o jsonpath='{.status.loadBalancer.ingress[0].ip}' 2>/dev/null || echo "")
fi

if [ -n "$EXTERNAL_IP" ]; then
    echo "  🌐 Istio Gateway external endpoint: $EXTERNAL_IP"
    echo ""
    echo "  📝 DNS Configuration Required:"
    echo "  Create CNAME records pointing to: $EXTERNAL_IP"
    for tenant in $TENANTS; do
        if kubectl get namespace tenant-$tenant >/dev/null 2>&1; then
            echo "    $tenant.architrave-assets.com -> $EXTERNAL_IP"
        fi
    done
else
    echo "  ❌ No external endpoint found for Istio Gateway"
fi
echo ""

# Clean up temp files
rm -f /tmp/tenant-gateway.yaml

echo "🏁 PHASE 3 VIRTUALSERVICE CREATION COMPLETE"
echo "==========================================="
