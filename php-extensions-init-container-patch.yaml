spec:
  template:
    spec:
      initContainers:
      - name: install-php-extensions
        image: 545009857703.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:2.0.57-test
        command: ["/bin/bash"]
        args:
        - -c
        - |
          echo "Installing PHP extensions..."
          docker-php-ext-install zip
          docker-php-ext-enable imagick
          echo "Extensions installed successfully"
          # Copy extensions to shared volume
          cp /usr/local/lib/php/extensions/no-debug-non-zts-20210902/*.so /shared-extensions/
          cp /usr/local/etc/php/conf.d/*.ini /shared-extensions/
        securityContext:
          runAsUser: 0
          runAsGroup: 0
          allowPrivilegeEscalation: true
          readOnlyRootFilesystem: false
        volumeMounts:
        - name: shared-extensions
          mountPath: /shared-extensions
        resources:
          requests:
            memory: "64Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "200m"
      containers:
      - name: backend
        command: ["/bin/bash"]
        args:
        - -c
        - |
          echo "Loading PHP extensions from shared volume..."
          cp /shared-extensions/*.so /usr/local/lib/php/extensions/no-debug-non-zts-20210902/ 2>/dev/null || true
          cp /shared-extensions/*.ini /usr/local/etc/php/conf.d/ 2>/dev/null || true
          echo "Starting application..."
          exec docker-php-entrypoint php-fpm
        volumeMounts:
        - name: shared-extensions
          mountPath: /shared-extensions
        - name: cli-config
          mountPath: /storage/ArchAssets/config/autoload/local.php
          subPath: local.php
      volumes:
      - name: shared-extensions
        emptyDir: {}
      - name: cli-config
        configMap:
          name: cli-config
