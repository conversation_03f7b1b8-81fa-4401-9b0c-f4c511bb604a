#!/bin/bash

# Test script for fixed HTTPS onboarding
# This script tests the onboarding with DNS check skipped

set -e

TENANT_ID="completetest$(date +%s)"
TENANT_NAME="Complete HTTPS Test"

echo "🚀 Testing Fixed HTTPS Onboarding System"
echo "========================================"
echo "Tenant ID: $TENANT_ID"
echo "Tenant Name: $TENANT_NAME"
echo ""

echo "📋 Step 1: Running onboarding with DNS check skipped..."
./advanced_tenant_onboard \
  --tenant-id "$TENANT_ID" \
  --tenant-name "$TENANT_NAME" \
  --skip-dns \
  --skip-web-check \
  --enable-production-audit

ONBOARD_RESULT=$?

if [ $ONBOARD_RESULT -eq 0 ]; then
    echo "✅ Onboarding completed successfully!"
else
    echo "⚠️ Onboarding completed with warnings (exit code: $ONBOARD_RESULT)"
fi

echo ""
echo "📋 Step 2: Testing HTTPS functionality..."

# Get load balancer hostname
LB_HOSTNAME=$(kubectl get svc istio-ingress -n istio-system -o jsonpath='{.status.loadBalancer.ingress[0].hostname}')
echo "Load Balancer: $LB_HOSTNAME"

# Test HTTPS frontend
echo "🔒 Testing HTTPS frontend..."
HTTPS_RESULT=$(curl -k -H "Host: ${TENANT_ID}.architrave-assets.com" \
  "https://${LB_HOSTNAME}/" --max-time 10 -s -o /dev/null -w "%{http_code}")

if [ "$HTTPS_RESULT" = "200" ]; then
    echo "✅ HTTPS frontend: Working (HTTP $HTTPS_RESULT)"
else
    echo "❌ HTTPS frontend: Failed (HTTP $HTTPS_RESULT)"
fi

# Test HTTPS API
echo "🔒 Testing HTTPS API..."
API_RESULT=$(curl -k -H "Host: ${TENANT_ID}.architrave-assets.com" \
  "https://${LB_HOSTNAME}/api/health" --max-time 10 -s)

if [ "$API_RESULT" = "healthy" ]; then
    echo "✅ HTTPS API: Working (Response: $API_RESULT)"
else
    echo "❌ HTTPS API: Failed (Response: $API_RESULT)"
fi

# Test HTTP frontend
echo "🌐 Testing HTTP frontend..."
HTTP_RESULT=$(curl -H "Host: ${TENANT_ID}.architrave-assets.com" \
  "http://${LB_HOSTNAME}/" --max-time 5 -s -o /dev/null -w "%{http_code}")

if [ "$HTTP_RESULT" = "200" ]; then
    echo "✅ HTTP frontend: Working (HTTP $HTTP_RESULT)"
else
    echo "❌ HTTP frontend: Failed (HTTP $HTTP_RESULT)"
fi

echo ""
echo "📋 Step 3: Verifying infrastructure..."

# Check namespace
if kubectl get namespace "tenant-${TENANT_ID}" >/dev/null 2>&1; then
    echo "✅ Namespace: Created"
else
    echo "❌ Namespace: Missing"
fi

# Check VirtualService
if kubectl get virtualservice "tenant-${TENANT_ID}-vs" -n "tenant-${TENANT_ID}" >/dev/null 2>&1; then
    echo "✅ VirtualService: Created"
else
    echo "❌ VirtualService: Missing"
fi

# Check for conflicting services
WEBAPP_COUNT=$(kubectl get svc -n "tenant-${TENANT_ID}" 2>/dev/null | grep -c webapp || true)
if [ "$WEBAPP_COUNT" -eq 0 ]; then
    echo "✅ No conflicting webapp services"
else
    echo "⚠️ Found $WEBAPP_COUNT conflicting webapp services"
fi

# Check pods
RUNNING_PODS=$(kubectl get pods -n "tenant-${TENANT_ID}" --field-selector=status.phase=Running 2>/dev/null | wc -l)
RUNNING_PODS=$((RUNNING_PODS - 1)) # Subtract header line
echo "✅ Running pods: $RUNNING_PODS"

echo ""
echo "🎉 HTTPS ONBOARDING TEST COMPLETE!"
echo "=================================="
echo "Tenant: ${TENANT_ID}.architrave-assets.com"
echo "HTTPS URL: https://${TENANT_ID}.architrave-assets.com"
echo "API URL: https://${TENANT_ID}.architrave-assets.com/api/health"
echo ""
echo "Ready for DNS configuration:"
echo "Record Type: CNAME"
echo "Name: ${TENANT_ID}.architrave-assets.com"
echo "Target: $LB_HOSTNAME"
