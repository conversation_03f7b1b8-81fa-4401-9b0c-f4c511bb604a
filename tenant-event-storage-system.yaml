apiVersion: v1
kind: ConfigMap
metadata:
  name: tenant-event-db-schema
  namespace: tenant-management
  labels:
    app: tenant-event-storage
    component: database
data:
  schema.sql: |
    -- Tenant Event Storage Database Schema
    -- This schema supports 100+ tenants with comprehensive event tracking
    
    CREATE DATABASE IF NOT EXISTS tenant_management;
    USE tenant_management;
    
    -- Tenants table - Master tenant registry
    CREATE TABLE IF NOT EXISTS tenants (
        id VARCHAR(255) PRIMARY KEY,
        name VARCHAR(500) NOT NULL,
        display_name VARCHAR(500),
        namespace VARCHAR(255) NOT NULL UNIQUE,
        tier ENUM('basic', 'standard', 'premium') NOT NULL DEFAULT 'basic',
        status ENUM('active', 'inactive', 'suspended', 'pending', 'terminated') NOT NULL DEFAULT 'pending',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        created_by <PERSON><PERSON><PERSON><PERSON>(255),
        contact_email VARCHAR(255),
        contact_name VARCHAR(255),
        subdomain VARCHAR(255),
        environment VARCHAR(100) DEFAULT 'production',
        INDEX idx_status (status),
        INDEX idx_tier (tier),
        INDEX idx_created_at (created_at),
        INDEX idx_namespace (namespace)
    );
    
    -- Tenant Events table - Comprehensive event tracking
    CREATE TABLE IF NOT EXISTS tenant_events (
        id BIGINT AUTO_INCREMENT PRIMARY KEY,
        tenant_id VARCHAR(255) NOT NULL,
        event_type ENUM('onboard', 'offboard', 'suspend', 'resume', 'scale', 'update', 'backup', 'restore', 'migrate', 'health_check', 'cost_alert', 'resource_alert') NOT NULL,
        event_status ENUM('started', 'in_progress', 'completed', 'failed', 'cancelled') NOT NULL DEFAULT 'started',
        event_data JSON,
        metadata JSON,
        initiated_by VARCHAR(255),
        initiated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        completed_at TIMESTAMP NULL,
        duration_seconds INT NULL,
        error_message TEXT NULL,
        INDEX idx_tenant_id (tenant_id),
        INDEX idx_event_type (event_type),
        INDEX idx_event_status (event_status),
        INDEX idx_initiated_at (initiated_at),
        INDEX idx_tenant_type (tenant_id, event_type),
        FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE
    );
    
    -- Tenant Resources table - Current resource allocation and usage
    CREATE TABLE IF NOT EXISTS tenant_resources (
        id BIGINT AUTO_INCREMENT PRIMARY KEY,
        tenant_id VARCHAR(255) NOT NULL,
        resource_type ENUM('cpu', 'memory', 'storage', 'pods', 'services', 'ingress', 'secrets', 'configmaps') NOT NULL,
        allocated_amount DECIMAL(10,2) NOT NULL,
        used_amount DECIMAL(10,2) NOT NULL DEFAULT 0,
        unit VARCHAR(50) NOT NULL,
        last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        UNIQUE KEY unique_tenant_resource (tenant_id, resource_type),
        INDEX idx_tenant_id (tenant_id),
        INDEX idx_resource_type (resource_type),
        FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE
    );
    
    -- Tenant Costs table - Cost tracking and billing
    CREATE TABLE IF NOT EXISTS tenant_costs (
        id BIGINT AUTO_INCREMENT PRIMARY KEY,
        tenant_id VARCHAR(255) NOT NULL,
        cost_period ENUM('hourly', 'daily', 'monthly', 'yearly') NOT NULL,
        cost_amount DECIMAL(10,2) NOT NULL,
        cost_currency VARCHAR(3) DEFAULT 'USD',
        billing_period_start DATE NOT NULL,
        billing_period_end DATE NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_tenant_id (tenant_id),
        INDEX idx_billing_period (billing_period_start, billing_period_end),
        INDEX idx_cost_period (cost_period),
        FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE
    );
    
    -- Tenant Performance Metrics table - Performance tracking
    CREATE TABLE IF NOT EXISTS tenant_performance (
        id BIGINT AUTO_INCREMENT PRIMARY KEY,
        tenant_id VARCHAR(255) NOT NULL,
        metric_name VARCHAR(100) NOT NULL,
        metric_value DECIMAL(10,4) NOT NULL,
        metric_unit VARCHAR(50),
        recorded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_tenant_id (tenant_id),
        INDEX idx_metric_name (metric_name),
        INDEX idx_recorded_at (recorded_at),
        INDEX idx_tenant_metric (tenant_id, metric_name),
        FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE
    );
    
    -- Tenant Configurations table - Configuration history
    CREATE TABLE IF NOT EXISTS tenant_configurations (
        id BIGINT AUTO_INCREMENT PRIMARY KEY,
        tenant_id VARCHAR(255) NOT NULL,
        config_type VARCHAR(100) NOT NULL,
        config_data JSON NOT NULL,
        version INT NOT NULL DEFAULT 1,
        applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        applied_by VARCHAR(255),
        is_active BOOLEAN DEFAULT TRUE,
        INDEX idx_tenant_id (tenant_id),
        INDEX idx_config_type (config_type),
        INDEX idx_version (version),
        INDEX idx_is_active (is_active),
        FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE
    );
    
    -- Tenant Audit Log table - Comprehensive audit trail
    CREATE TABLE IF NOT EXISTS tenant_audit_log (
        id BIGINT AUTO_INCREMENT PRIMARY KEY,
        tenant_id VARCHAR(255),
        action VARCHAR(255) NOT NULL,
        resource_type VARCHAR(100),
        resource_id VARCHAR(255),
        old_values JSON,
        new_values JSON,
        user_id VARCHAR(255),
        user_ip VARCHAR(45),
        user_agent TEXT,
        timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_tenant_id (tenant_id),
        INDEX idx_action (action),
        INDEX idx_resource_type (resource_type),
        INDEX idx_timestamp (timestamp),
        INDEX idx_user_id (user_id),
        FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE SET NULL
    );
    
    -- Views for common queries
    CREATE OR REPLACE VIEW tenant_summary AS
    SELECT 
        t.id,
        t.name,
        t.display_name,
        t.namespace,
        t.tier,
        t.status,
        t.created_at,
        t.updated_at,
        COUNT(DISTINCT te.id) as total_events,
        MAX(te.initiated_at) as last_event_at,
        SUM(CASE WHEN te.event_type = 'onboard' AND te.event_status = 'completed' THEN 1 ELSE 0 END) as onboard_count,
        SUM(CASE WHEN te.event_type = 'offboard' AND te.event_status = 'completed' THEN 1 ELSE 0 END) as offboard_count
    FROM tenants t
    LEFT JOIN tenant_events te ON t.id = te.tenant_id
    GROUP BY t.id, t.name, t.display_name, t.namespace, t.tier, t.status, t.created_at, t.updated_at;
    
    CREATE OR REPLACE VIEW tenant_current_costs AS
    SELECT 
        tenant_id,
        SUM(CASE WHEN cost_period = 'hourly' THEN cost_amount ELSE 0 END) as hourly_cost,
        SUM(CASE WHEN cost_period = 'daily' THEN cost_amount ELSE 0 END) as daily_cost,
        SUM(CASE WHEN cost_period = 'monthly' THEN cost_amount ELSE 0 END) as monthly_cost,
        SUM(CASE WHEN cost_period = 'yearly' THEN cost_amount ELSE 0 END) as yearly_cost
    FROM tenant_costs 
    WHERE billing_period_start <= CURDATE() AND billing_period_end >= CURDATE()
    GROUP BY tenant_id;
    
    -- Stored procedures for common operations
    DELIMITER //
    
    CREATE OR REPLACE PROCEDURE CreateTenantEvent(
        IN p_tenant_id VARCHAR(255),
        IN p_event_type VARCHAR(50),
        IN p_event_data JSON,
        IN p_initiated_by VARCHAR(255)
    )
    BEGIN
        INSERT INTO tenant_events (tenant_id, event_type, event_data, initiated_by)
        VALUES (p_tenant_id, p_event_type, p_event_data, p_initiated_by);
    END //
    
    CREATE OR REPLACE PROCEDURE CompleteEvent(
        IN p_event_id BIGINT,
        IN p_status VARCHAR(50),
        IN p_error_message TEXT
    )
    BEGIN
        UPDATE tenant_events 
        SET 
            event_status = p_status,
            completed_at = CURRENT_TIMESTAMP,
            duration_seconds = TIMESTAMPDIFF(SECOND, initiated_at, CURRENT_TIMESTAMP),
            error_message = p_error_message
        WHERE id = p_event_id;
    END //
    
    CREATE OR REPLACE PROCEDURE UpdateTenantResources(
        IN p_tenant_id VARCHAR(255),
        IN p_resource_type VARCHAR(50),
        IN p_allocated DECIMAL(10,2),
        IN p_used DECIMAL(10,2),
        IN p_unit VARCHAR(50)
    )
    BEGIN
        INSERT INTO tenant_resources (tenant_id, resource_type, allocated_amount, used_amount, unit)
        VALUES (p_tenant_id, p_resource_type, p_allocated, p_used, p_unit)
        ON DUPLICATE KEY UPDATE
            allocated_amount = p_allocated,
            used_amount = p_used,
            unit = p_unit,
            last_updated = CURRENT_TIMESTAMP;
    END //
    
    DELIMITER ;
    
    -- Insert sample data for testing
    INSERT IGNORE INTO tenants (id, name, display_name, namespace, tier, status, created_by, contact_email) VALUES
    ('tenant-001', 'Enterprise Corp 1', 'Enterprise Corporation', 'tenant-enterprise-corp-1', 'premium', 'active', '<EMAIL>', '<EMAIL>'),
    ('tenant-002', 'Tech Startup 2', 'Tech Startup Hub', 'tenant-tech-startup-2', 'basic', 'active', '<EMAIL>', '<EMAIL>'),
    ('tenant-003', 'Global Solutions 3', 'Global Solutions Inc', 'tenant-global-solutions-3', 'standard', 'active', '<EMAIL>', '<EMAIL>');
    
    -- Insert sample events
    INSERT IGNORE INTO tenant_events (tenant_id, event_type, event_status, event_data, initiated_by) VALUES
    ('tenant-001', 'onboard', 'completed', '{"components": ["database", "frontend", "backend", "rabbitmq"]}', '<EMAIL>'),
    ('tenant-002', 'onboard', 'completed', '{"components": ["database", "frontend", "backend"]}', '<EMAIL>'),
    ('tenant-003', 'onboard', 'completed', '{"components": ["database", "frontend", "backend", "rabbitmq"]}', '<EMAIL>');
---
apiVersion: v1
kind: Secret
metadata:
  name: tenant-event-db-credentials
  namespace: tenant-management
  labels:
    app: tenant-event-storage
type: Opaque
data:
  # Default credentials (base64 encoded)
  # username: tenant_admin
  # password: secure_password_123
  username: dGVuYW50X2FkbWlu
  password: c2VjdXJlX3Bhc3N3b3JkXzEyMw==
  database: dGVuYW50X21hbmFnZW1lbnQ=
  host: bG9jYWxob3N0
  port: MzMwNg==
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: tenant-event-db
  namespace: tenant-management
  labels:
    app: tenant-event-storage
    component: database
spec:
  replicas: 1
  selector:
    matchLabels:
      app: tenant-event-storage
      component: database
  template:
    metadata:
      labels:
        app: tenant-event-storage
        component: database
    spec:
      containers:
      - name: mysql
        image: mysql:8.0
        env:
        - name: MYSQL_ROOT_PASSWORD
          valueFrom:
            secretKeyRef:
              name: tenant-event-db-credentials
              key: password
        - name: MYSQL_DATABASE
          valueFrom:
            secretKeyRef:
              name: tenant-event-db-credentials
              key: database
        - name: MYSQL_USER
          valueFrom:
            secretKeyRef:
              name: tenant-event-db-credentials
              key: username
        - name: MYSQL_PASSWORD
          valueFrom:
            secretKeyRef:
              name: tenant-event-db-credentials
              key: password
        ports:
        - containerPort: 3306
          name: mysql
        volumeMounts:
        - name: mysql-data
          mountPath: /var/lib/mysql
        - name: init-schema
          mountPath: /docker-entrypoint-initdb.d
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          exec:
            command:
            - mysqladmin
            - ping
            - -h
            - localhost
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          exec:
            command:
            - mysql
            - -h
            - localhost
            - -u
            - root
            - -p$MYSQL_ROOT_PASSWORD
            - -e
            - "SELECT 1"
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: mysql-data
        persistentVolumeClaim:
          claimName: tenant-event-db-pvc
      - name: init-schema
        configMap:
          name: tenant-event-db-schema
