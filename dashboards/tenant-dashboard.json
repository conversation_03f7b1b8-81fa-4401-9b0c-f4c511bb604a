{"widgets": [{"type": "metric", "x": 0, "y": 0, "width": 12, "height": 6, "properties": {"metrics": [["AWS/EKS", "pod_cpu_utilization", "ClusterName", "${EKS_CLUSTER_NAME}", "Namespace", "${NAMESPACE}"]], "view": "timeSeries", "stacked": false, "region": "eu-central-1", "title": "Pod CPU Utilization", "period": 300, "stat": "Average"}}, {"type": "metric", "x": 12, "y": 0, "width": 12, "height": 6, "properties": {"metrics": [["AWS/EKS", "pod_memory_utilization", "ClusterName", "${EKS_CLUSTER_NAME}", "Namespace", "${NAMESPACE}"]], "view": "timeSeries", "stacked": false, "region": "eu-central-1", "title": "Pod Memory Utilization", "period": 300, "stat": "Average"}}, {"type": "metric", "x": 0, "y": 6, "width": 12, "height": 6, "properties": {"metrics": [["AWS/S3", "BucketSizeBytes", "BucketName", "${S3_BUCKET}", "StorageType", "StandardStorage"]], "view": "timeSeries", "stacked": false, "region": "eu-central-1", "title": "S3 Bucket Size", "period": 86400, "stat": "Average"}}, {"type": "metric", "x": 12, "y": 6, "width": 12, "height": 6, "properties": {"metrics": [["AWS/S3", "NumberOfObjects", "BucketName", "${S3_BUCKET}", "StorageType", "AllStorageTypes"]], "view": "timeSeries", "stacked": false, "region": "eu-central-1", "title": "S3 Number of Objects", "period": 86400, "stat": "Average"}}, {"type": "metric", "x": 0, "y": 12, "width": 12, "height": 6, "properties": {"metrics": [["AWS/RDS", "CPUUtilization", "DBInstanceIdentifier", "${RDS_INSTANCE_ID}"]], "view": "timeSeries", "stacked": false, "region": "eu-central-1", "title": "RDS CPU Utilization", "period": 300, "stat": "Average"}}, {"type": "metric", "x": 12, "y": 12, "width": 12, "height": 6, "properties": {"metrics": [["AWS/RDS", "DatabaseConnections", "DBInstanceIdentifier", "${RDS_INSTANCE_ID}"]], "view": "timeSeries", "stacked": false, "region": "eu-central-1", "title": "RDS Database Connections", "period": 300, "stat": "Average"}}]}