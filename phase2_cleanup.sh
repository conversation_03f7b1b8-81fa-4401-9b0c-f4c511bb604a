#!/bin/bash
# Phase 2: Cleanup conflicting ALB components
set -e

echo "🧹 PHASE 2: CLEANUP CONFLICTING ALB COMPONENTS"
echo "=============================================="
echo ""

echo "Step 2.1: Identifying existing tenant namespaces..."
TENANT_NAMESPACES=$(kubectl get namespaces | grep "tenant-" | awk '{print $1}' 2>/dev/null || echo "")
if [ -z "$TENANT_NAMESPACES" ]; then
    echo "❌ No tenant namespaces found"
else
    echo "✅ Found tenant namespaces:"
    for ns in $TENANT_NAMESPACES; do
        echo "  - $ns"
    done
fi
echo ""

echo "Step 2.2: Removing Kubernetes Ingress resources from tenant namespaces..."
for namespace in $TENANT_NAMESPACES; do
    echo "  Checking namespace: $namespace"
    INGRESSES=$(kubectl get ingress -n $namespace --no-headers 2>/dev/null | awk '{print $1}' || echo "")
    if [ -n "$INGRESSES" ]; then
        for ingress in $INGRESSES; do
            echo "    🗑️  Deleting ingress: $ingress"
            kubectl delete ingress $ingress -n $namespace || echo "    ❌ Failed to delete $ingress"
        done
    else
        echo "    ✅ No ingress resources found"
    fi
done
echo ""

echo "Step 2.3: Checking AWS Load Balancer Controller status..."
if kubectl get deployment aws-load-balancer-controller -n kube-system >/dev/null 2>&1; then
    echo "  ⚠️  AWS Load Balancer Controller found"
    echo "  📝 RECOMMENDATION: Consider removing if no other applications use ALB"
    echo "  📝 Manual removal command: helm uninstall aws-load-balancer-controller -n kube-system"
else
    echo "  ✅ AWS Load Balancer Controller not found"
fi
echo ""

echo "Step 2.4: Verifying Istio installation..."
if kubectl get namespace istio-system >/dev/null 2>&1; then
    echo "  ✅ istio-system namespace exists"
    
    # Check Istio pods
    ISTIO_PODS=$(kubectl get pods -n istio-system --no-headers 2>/dev/null | wc -l || echo "0")
    echo "  📊 Istio pods running: $ISTIO_PODS"
    
    # Check ingress gateway service
    if kubectl get svc istio-ingressgateway -n istio-system >/dev/null 2>&1; then
        echo "  ✅ istio-ingressgateway service exists"
        
        # Get external endpoint
        EXTERNAL_IP=$(kubectl get svc istio-ingressgateway -n istio-system -o jsonpath='{.status.loadBalancer.ingress[0].hostname}' 2>/dev/null || echo "")
        if [ -z "$EXTERNAL_IP" ]; then
            EXTERNAL_IP=$(kubectl get svc istio-ingressgateway -n istio-system -o jsonpath='{.status.loadBalancer.ingress[0].ip}' 2>/dev/null || echo "")
        fi
        
        if [ -n "$EXTERNAL_IP" ]; then
            echo "  🌐 Istio Gateway external endpoint: $EXTERNAL_IP"
        else
            echo "  ⚠️  No external endpoint found - LoadBalancer may be provisioning"
        fi
    else
        echo "  ❌ istio-ingressgateway service not found"
    fi
else
    echo "  ❌ istio-system namespace not found"
    echo "  🚨 CRITICAL: Istio must be installed for new architecture"
fi
echo ""

echo "🏁 PHASE 2 CLEANUP COMPLETE"
echo "=========================="
echo ""
echo "Summary:"
echo "- Tenant namespaces: $(echo "$TENANT_NAMESPACES" | wc -w)"
echo "- Ingress resources removed from tenant namespaces"
echo "- AWS Load Balancer Controller status documented"
echo "- Istio installation verified"
echo ""
