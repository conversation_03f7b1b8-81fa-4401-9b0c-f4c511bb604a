apiVersion: apps/v1
kind: Deployment
metadata:
  name: cluster-autoscaler-aws-cluster-autoscaler
  namespace: kube-system
  labels:
    app.kubernetes.io/instance: cluster-autoscaler
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: aws-cluster-autoscaler
    helm.sh/chart: cluster-autoscaler-9.47.0
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/instance: cluster-autoscaler
      app.kubernetes.io/name: aws-cluster-autoscaler
  template:
    metadata:
      labels:
        app.kubernetes.io/instance: cluster-autoscaler
        app.kubernetes.io/name: aws-cluster-autoscaler
    spec:
      serviceAccountName: cluster-autoscaler-aws-cluster-autoscaler
      priorityClassName: system-cluster-critical
      containers:
      - name: aws-cluster-autoscaler
        image: registry.k8s.io/autoscaling/cluster-autoscaler:v1.33.0
        command:
        - ./cluster-autoscaler
        - --cloud-provider=aws
        - --namespace=kube-system
        - --node-group-auto-discovery=asg:tag=k8s.io/cluster-autoscaler/enabled,k8s.io/cluster-autoscaler/production-wks
        - --balance-similar-node-groups=true
        - --logtostderr=true
        - --stderrthreshold=info
        - --v=4
        # COST OPTIMIZATION PARAMETERS
        - --scale-down-utilization-threshold=0.3
        - --scale-down-delay-after-add=3m
        - --scale-down-unneeded-time=2m
        - --scale-down-delay-after-delete=10s
        - --scale-down-delay-after-failure=3m
        - --skip-nodes-with-local-storage=false
        - --skip-nodes-with-system-pods=false
        - --new-pod-scale-up-delay=0s
        - --expander=least-waste
        # RESOURCE LIMITS FOR COST CONTROL
        - --cores-total=10:80
        - --memory-total=40000:320000
        - --max-nodes-total=20
        env:
        - name: POD_NAMESPACE
          valueFrom:
            fieldRef:
              fieldPath: metadata.namespace
        - name: SERVICE_ACCOUNT
          valueFrom:
            fieldRef:
              fieldPath: spec.serviceAccountName
        - name: AWS_REGION
          value: eu-central-1
        ports:
        - containerPort: 8085
          protocol: TCP
        livenessProbe:
          httpGet:
            path: /health-check
            port: 8085
          periodSeconds: 10
        resources:
          requests:
            cpu: 100m
            memory: 300Mi
          limits:
            cpu: 100m
            memory: 300Mi
