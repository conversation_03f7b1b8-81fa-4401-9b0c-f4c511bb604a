# Terraform Security Management

This document consolidates information about security practices, best practices, and security scanning tools for our Terraform infrastructure.

## Security Practices Overview

Our security practices are designed to ensure that our infrastructure is secure, compliant, and follows industry best practices. We implement security at multiple levels:

1. **Infrastructure as Code (IaC) Security**: Securing the Terraform code itself
2. **Resource Security**: Ensuring AWS resources are configured securely
3. **Access Control**: Implementing proper authentication and authorization
4. **Data Protection**: Protecting sensitive data and ensuring encryption
5. **Monitoring and Auditing**: Tracking changes and detecting security issues
6. **Compliance**: Ensuring adherence to regulatory requirements

## AWS Foundational Security Best Practices Implementation

### Critical Security Controls

#### AWS Config (Config.1)
- AWS Config is enabled with a service-linked role for resource recording
- Configuration recorder is enabled to track resource changes
- Delivery channel is configured to store configuration history

#### Security Groups (EC2.19)
- Security groups are configured to restrict access to high-risk ports
- No unrestricted access (0.0.0.0/0) is allowed for SSH or other high-risk ports
- Bastion host access is restricted to specific CIDR blocks

### High Security Controls

#### ECR Image Scanning (ECR.1)
- All ECR repositories have image scanning enabled
- Scan on push is configured for immediate vulnerability detection
- Findings are reported to SNS for notification

#### CloudTrail (CloudTrail.1)
- Multi-region CloudTrail is enabled for comprehensive audit logging
- Log file validation is enabled to ensure integrity
- CloudTrail logs are encrypted with KMS
- CloudWatch integration for real-time monitoring of suspicious activities

#### GuardDuty (GuardDuty.6, GuardDuty.11)
- GuardDuty is enabled for threat detection
- Lambda Network Logs is enabled to detect suspicious Lambda function activity
- Runtime Monitoring is enabled to detect runtime threats

#### Amazon Inspector (Inspector.1, Inspector.2, Inspector.3, Inspector.4)
- Inspector is enabled for EC2 instances to detect vulnerabilities
- Inspector is enabled for ECR repositories to scan container images
- Inspector is enabled for Lambda code scanning
- Inspector is enabled for Lambda standard scanning

## IaC Security Practices

### Code Security
- **Version Control**: All infrastructure code is stored in version control
- **Code Reviews**: All changes undergo peer review before merging
- **Static Analysis**: Code is scanned for security issues using tools like TFSec, Checkov, and TFLint
- **Policy as Code**: Custom policies are enforced using OPA/Conftest

### Secret Management
- **No Hardcoded Secrets**: Secrets are never hardcoded in Terraform code
- **AWS Secrets Manager**: Sensitive data is stored in AWS Secrets Manager
- **KMS Encryption**: Encryption keys are managed using AWS KMS
- **IAM Roles**: Services use IAM roles instead of access keys when possible

## Resource Security

### Network Security
- **VPC Isolation**: Resources are deployed in isolated VPCs
- **Security Groups**: Strict security group rules are applied
- **Network ACLs**: Additional network-level filtering is implemented
- **Private Subnets**: Sensitive resources are placed in private subnets
- **Bastion Hosts**: Access to private resources is controlled through bastion hosts

### Compute Security
- **Encrypted EBS Volumes**: All EBS volumes are encrypted
- **IMDSv2**: EC2 instances use IMDSv2 to prevent SSRF attacks
- **Security Groups**: Instances have minimal inbound access
- **Patching**: Instances are regularly patched using AWS Systems Manager

### Storage Security
- **S3 Bucket Policies**: Strict bucket policies are applied
- **S3 Encryption**: All S3 buckets have encryption enabled
- **Public Access Blocks**: S3 buckets block public access
- **Versioning**: S3 buckets have versioning enabled
- **Logging**: S3 access logs are enabled

### Database Security
- **Encryption at Rest**: All databases are encrypted at rest
- **Encryption in Transit**: SSL/TLS is enforced for database connections
- **Private Subnets**: Databases are placed in private subnets
- **Security Groups**: Database access is restricted to specific security groups
- **IAM Authentication**: RDS instances use IAM authentication when possible

## Access Control

### Authentication
- **IAM Roles**: Services use IAM roles with least privilege
- **MFA**: Multi-factor authentication is enforced for human users
- **Temporary Credentials**: Short-lived credentials are used when possible
- **No Shared Credentials**: Each user has their own credentials

### Authorization
- **Least Privilege**: IAM policies follow the principle of least privilege
- **Permission Boundaries**: IAM permission boundaries are used to limit permissions
- **Service Control Policies (SCPs)**: Organization-wide policies are enforced using SCPs
- **Resource-Based Policies**: Additional controls are applied using resource-based policies

## Data Protection

### Encryption
- **KMS**: AWS KMS is used for key management
- **Encryption at Rest**: All sensitive data is encrypted at rest
- **Encryption in Transit**: TLS is enforced for all communications
- **Customer-Managed Keys**: Customer-managed keys are used for sensitive data

### Data Classification
- **Data Tagging**: Resources are tagged based on data classification
- **Isolation**: Different data classifications are isolated from each other
- **Access Controls**: Access controls are based on data classification

## Monitoring and Auditing

### Logging
- **CloudTrail**: AWS CloudTrail is enabled for all regions
- **VPC Flow Logs**: VPC Flow Logs are enabled for network monitoring
- **S3 Access Logs**: S3 access logs are enabled for all buckets
- **CloudWatch Logs**: Application logs are sent to CloudWatch Logs

### Monitoring
- **CloudWatch Alarms**: Alarms are configured for security events
- **GuardDuty**: AWS GuardDuty is enabled for threat detection
- **Security Hub**: AWS Security Hub is used for security posture management
- **Config**: AWS Config is used for resource compliance monitoring

### Incident Response
- **Automated Remediation**: Some security issues are automatically remediated
- **Alerting**: Security incidents trigger alerts to the security team
- **Playbooks**: Incident response playbooks are documented and tested
- **Forensics**: Tools and procedures are in place for forensic analysis

## Compliance

### Regulatory Compliance
- **CIS Benchmarks**: Infrastructure follows CIS AWS Foundations Benchmark
- **GDPR**: Data protection measures comply with GDPR requirements
- **HIPAA**: Healthcare data is protected according to HIPAA requirements
- **PCI DSS**: Payment card data is protected according to PCI DSS requirements

### Compliance Monitoring
- **Automated Checks**: Compliance is continuously monitored using automated checks
- **Audit Reports**: Regular audit reports are generated
- **Remediation**: Non-compliant resources are remediated

## Security Testing

### Automated Testing
- **Security Scanning**: Infrastructure code is scanned for security issues
- **Compliance Checking**: Infrastructure is checked for compliance violations
- **Penetration Testing**: Regular penetration testing is performed

### Manual Testing
- **Code Reviews**: Security-focused code reviews are performed
- **Architecture Reviews**: Infrastructure architecture is reviewed for security issues
- **Threat Modeling**: Threat modeling is performed for critical systems

## Security Scanning Tools

We've fixed the issues with the security scanning tools in the GitLab CI pipeline. The following tools were failing:

1. **Checkov**: Failing with unrecognized arguments `sh`
2. **Infracost**: Failing with error related to `INFRACOST_NO_COLOR` environment variable
3. **OPA/Conftest**: Failing with "unknown command 'sh'"
4. **TFLint**: Failing with "Command line arguments support was dropped in v0.47"
5. **TFSec**: Failing with "unknown shorthand flag: 'c' in -c"

### Changes Made

We've updated the `.gitlab/security-scan-template.yml` file with the following improvements:

1. **Added Comments**: Added clear comments to explain what each command does
2. **Fixed Command Syntax**: Updated commands to use the correct syntax for each tool
3. **Added Error Handling**: Improved error handling for all tools
4. **Created Default Policies**: Added code to create default policies for OPA/Conftest
5. **Fixed Environment Variables**: Fixed the `INFRACOST_NO_COLOR` environment variable issue by setting it to "true" (string) instead of true (boolean)
6. **Updated Dependencies**: Fixed dependency issues between jobs in the pipeline
7. **Simplified Scripts**: Removed shell script wrappers and directly called tool commands

### How to Test

To test these changes:

1. Commit and push the changes to your GitLab repository
2. Monitor the pipeline to ensure that all security scanning jobs pass
3. Check the job logs for any errors or warnings

To manually run the security scanning tools locally:

```bash
# TFSec
docker run --rm -v $(pwd):/src -w /src aquasec/tfsec:latest tfsec . --format=default

# Checkov
docker run --rm -v $(pwd):/src -w /src bridgecrew/checkov:latest checkov -d . --quiet --framework terraform --output cli --soft-fail

# TFLint
docker run --rm -v $(pwd):/src -w /src ghcr.io/terraform-linters/tflint:latest tflint --filter=.

# OPA/Conftest (requires tfplan.json)
terraform plan -out=tfplan
terraform show -json tfplan > tfplan.json
docker run --rm -v $(pwd):/src -w /src openpolicyagent/conftest:latest conftest test tfplan.json -p policies/terraform.rego

# Infracost
docker run --rm -v $(pwd):/src -w /src -e INFRACOST_API_KEY="00000000000000000000000000000000" -e INFRACOST_NO_COLOR="true" infracost/infracost:latest infracost breakdown --path .
```

## Security Best Practices

1. **Follow the Principle of Least Privilege**: Grant only the permissions necessary
2. **Encrypt Sensitive Data**: Always encrypt sensitive data at rest and in transit
3. **Implement Defense in Depth**: Apply multiple layers of security controls
4. **Automate Security Checks**: Use automated tools to check for security issues
5. **Keep Infrastructure Updated**: Regularly update and patch infrastructure components
6. **Monitor and Alert**: Implement comprehensive monitoring and alerting
7. **Document Security Controls**: Document all security controls and procedures
8. **Test Security Controls**: Regularly test security controls to ensure effectiveness
9. **Train Team Members**: Ensure all team members are trained on security practices
10. **Plan for Incidents**: Have incident response plans in place

## Security Module Configuration

### AWS Config Module
```hcl
module "aws_config" {
  source                    = "./modules/aws_config"
  environment               = var.environment
  tags                      = var.tags
  skip_config_creation      = false # Always create AWS Config
}
```

### CloudTrail Module
```hcl
module "cloudtrail" {
  source            = "./modules/cloudtrail"
  environment       = var.environment
  alert_emails      = var.alert_emails
  log_retention_days = 90
  tags              = var.tags
}
```

### GuardDuty Module
```hcl
module "guardduty" {
  source                      = "./modules/guardduty"
  environment                 = var.environment
  alert_emails                = var.alert_emails
  tags                        = var.tags
}
```

### Inspector Module
```hcl
module "inspector" {
  source       = "./modules/inspector"
  environment  = var.environment
  alert_emails = var.alert_emails
  kms_key_arn  = module.kms.primary_key_arn
  tags         = var.tags
}
```

## Maintenance and Updates

- Security modules are regularly updated to address new threats
- Security findings are reviewed and remediated promptly
- Security configurations are version-controlled and deployed through CI/CD
