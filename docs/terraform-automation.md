# 🚀 Terraform Automation Guide

This guide explains how to use the Terraform automation scripts to manage your infrastructure without running `terraform apply`.

## 📋 Overview

The Terraform automation scripts provide a consistent way to run Terraform operations (init, fmt, validate, plan, output) without applying changes. This is useful for:

- CI/CD pipelines where you want to validate and plan changes without applying them
- Local development where you want to check your changes before applying them
- Ensuring consistency between local development and CI/CD environments

## 🛠️ Available Tools

### 1. Terraform Run Script

The main script for Terraform automation is `scripts/terraform/terraform-run.sh`. This script can run various Terraform operations with consistent settings.

```bash
# Run all operations (init, fmt, validate, plan, output)
./scripts/terraform/terraform-run.sh

# Run specific operations
./scripts/terraform/terraform-run.sh --operation init
./scripts/terraform/terraform-run.sh --operation validate
./scripts/terraform/terraform-run.sh --operation plan

# Run in CI mode
./scripts/terraform/terraform-run.sh --ci-mode

# Run with verbose output
./scripts/terraform/terraform-run.sh --verbose
```

### 2. GitLab CI Configuration

The `.**********************-apply.yml` file provides a GitLab CI configuration that runs Terraform operations without applying changes.

```bash
# Include in your .gitlab-ci.yml
include:
  - local: .**********************-apply.yml
```

### 3. GitHub Actions Workflow

The `.github/workflows/terraform-no-apply.yml` file provides a GitHub Actions workflow that runs Terraform operations without applying changes.

### 4. Makefile Targets

The `Makefile.terraform` file provides Makefile targets for running Terraform operations.

```bash
# Include in your Makefile
include Makefile.terraform

# Run Makefile targets
make terraform-init
make terraform-validate
make terraform-plan
make terraform-all
```

## 🚀 Usage Examples

### Local Development

```bash
# Run all operations
./scripts/terraform/terraform-run.sh

# Run specific operations
./scripts/terraform/terraform-run.sh --operation plan

# Use Makefile targets
make terraform-plan
```

### CI/CD Pipeline

#### GitLab CI

```yaml
# .gitlab-ci.yml
include:
  - local: .**********************-apply.yml
```

#### GitHub Actions

The workflow is automatically triggered on push to main/develop branches and on pull requests.

## 📝 Command Line Options

The `terraform-run.sh` script supports the following options:

```
Options:
  -o, --operation <operation>     Operation to perform (init, fmt, validate, plan, output, all)
  -p, --plan-file <file>          Path to the plan file (default: tfplan)
  -s, --state-file <file>         Path to the state file (default: terraform_state.json)
  -u, --output-file <file>        Path to the output file (default: terraform_outputs.json)
  --skip-fmt                      Skip terraform fmt
  --skip-validate                 Skip terraform validate
  --skip-plan                     Skip terraform plan
  --skip-output                   Skip terraform output
  --ci-mode                       Run in CI mode (use CI-specific settings)
  --no-skip-k8s                   Don't skip Kubernetes connections
  --no-skip-kubernetes-resources  Don't skip Kubernetes resources
  --no-check-if-cluster-exists    Don't check if cluster exists
  --verbose                       Enable verbose output
  -h, --help                      Display this help message
```

## 🔄 Workflow

The typical workflow for using these tools is:

1. Make changes to your Terraform code
2. Run `./scripts/terraform/terraform-run.sh` or `make terraform-all` to validate and plan your changes
3. Review the plan output
4. If the plan looks good, you can apply the changes manually using `terraform apply tfplan`

## 🔒 Security Considerations

- The scripts do not run `terraform apply`, so they cannot make changes to your infrastructure
- Sensitive outputs are not displayed in the logs
- AWS credentials are not stored in the scripts

## 🚨 Troubleshooting

### Common Issues

#### Terraform initialization fails

```bash
# Run with verbose output to see the error
./scripts/terraform/terraform-run.sh --operation init --verbose
```

#### Terraform plan fails

```bash
# Check if the error is related to Kubernetes connections
./scripts/terraform/terraform-run.sh --operation plan --verbose --no-skip-k8s
```

#### CI/CD pipeline fails

```bash
# Check if the error is related to provider configuration
# Make sure provider.tf.ci exists and is correctly configured
```

## 📚 Additional Resources

- [Terraform Documentation](https://www.terraform.io/docs)
- [GitLab CI Documentation](https://docs.gitlab.com/ee/ci/)
- [GitHub Actions Documentation](https://docs.github.com/en/actions)
