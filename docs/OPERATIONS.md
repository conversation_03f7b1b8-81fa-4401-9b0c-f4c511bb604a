# Operations Guide

## Daily Operations

### Backup Operations

#### 1. Monitor Backup Status
```bash
# Check backup status
kubectl get backup -n velero

# View backup details
velero backup describe <backup-name>

# Check backup logs
velero backup logs <backup-name>
```

#### 2. Verify Backup Integrity
```bash
# Run backup verification
velero backup verify <backup-name>

# Check verification results
kubectl get backupverification -n velero
```

#### 3. Storage Management
```bash
# Check storage usage
aws s3 ls s3://architrave-backups --summarize

# Clean up old backups
velero backup delete <backup-name>
```

### Performance Testing Operations

#### 1. Run Performance Tests
```bash
# Start a test
kubectl create -f testing/performance-test.yaml

# Monitor test progress
kubectl logs -n testing -l app=k6 -f
```

#### 2. Analyze Results
```bash
# Get test results
kubectl logs -n testing -l app=k6

# Export results
kubectl cp testing/<k6-pod>:/results ./test-results
```

#### 3. Optimize Performance
```bash
# Review performance metrics
kubectl get prometheusrules -n monitoring

# Adjust test parameters
kubectl edit configmap -n testing k6-test-script
```

### Monitoring Operations

#### 1. Dashboard Management
```bash
# Access Grafana
kubectl port-forward -n monitoring svc/grafana 3000:3000

# Update dashboards
kubectl apply -f monitoring/backup-dashboard.yaml
kubectl apply -f monitoring/performance-dashboard.yaml
```

#### 2. Alert Management
```bash
# Check alert rules
kubectl get prometheusrules -n monitoring

# View active alerts
kubectl get prometheusalerts -n monitoring
```

#### 3. Metrics Management
```bash
# Check metrics collection
kubectl get servicemonitor -n monitoring

# View metrics
kubectl port-forward -n monitoring svc/prometheus-server 9090:9090
```

## Weekly Operations

### 1. Backup Maintenance
```bash
# Review backup schedule
kubectl get schedule -n velero

# Update backup configuration
kubectl edit schedule -n velero daily-backup

# Clean up old backups
velero backup delete --older-than 30d
```

### 2. Performance Testing Maintenance
```bash
# Update test scenarios
kubectl edit configmap -n testing k6-test-script

# Clean up test results
kubectl delete pod -n testing -l app=k6

# Review performance baselines
kubectl get prometheusrules -n monitoring
```

### 3. Monitoring Maintenance
```bash
# Update dashboards
kubectl apply -f monitoring/backup-dashboard.yaml
kubectl apply -f monitoring/performance-dashboard.yaml

# Review alert rules
kubectl edit prometheusrules -n monitoring backup-monitoring-rules
kubectl edit prometheusrules -n monitoring performance-monitoring-rules

# Clean up old metrics
kubectl exec -n monitoring deploy/prometheus-server -- promtool tsdb clean
```

## Monthly Operations

### 1. System Review
```bash
# Review backup success rate
velero backup get --output json | jq '.items[] | select(.status.phase == "Failed")'

# Review performance trends
kubectl logs -n testing -l app=k6 --since 30d

# Review alert history
kubectl get prometheusalerts -n monitoring --all-namespaces
```

### 2. Security Review
```bash
# Review access logs
kubectl logs -n velero -l app.kubernetes.io/name=velero --since 30d

# Check security alerts
kubectl get prometheusalerts -n monitoring -l severity=critical

# Review audit logs
kubectl logs -n monitoring -l app=prometheus --since 30d
```

### 3. Performance Review
```bash
# Analyze performance metrics
kubectl port-forward -n monitoring svc/grafana 3000:3000

# Review resource usage
kubectl top pods -n monitoring
kubectl top pods -n velero
kubectl top pods -n testing
```

## Emergency Procedures

### 1. Backup Failure
```bash
# Check backup status
velero backup describe <failed-backup>

# View backup logs
velero backup logs <failed-backup>

# Restart backup
velero backup create <backup-name>
```

### 2. Performance Issues
```bash
# Check system resources
kubectl top nodes
kubectl top pods --all-namespaces

# Review performance metrics
kubectl port-forward -n monitoring svc/grafana 3000:3000

# Adjust resource limits
kubectl edit deployment -n testing k6
```

### 3. Monitoring Issues
```bash
# Check Prometheus status
kubectl get pods -n monitoring -l app=prometheus

# Check Grafana status
kubectl get pods -n monitoring -l app=grafana

# Restart monitoring components
kubectl rollout restart deployment -n monitoring prometheus-server
kubectl rollout restart deployment -n monitoring grafana
```

## Maintenance Windows

### 1. Backup Maintenance
- Time: Weekly, Sunday 02:00-04:00 UTC
- Duration: 2 hours
- Activities:
  - Backup verification
  - Storage cleanup
  - Configuration updates
  - Performance optimization

### 2. Performance Testing Maintenance
- Time: Weekly, Saturday 02:00-04:00 UTC
- Duration: 2 hours
- Activities:
  - Test scenario updates
  - Environment maintenance
  - Data cleanup
  - Tool updates

### 3. Monitoring Maintenance
- Time: Monthly, First Sunday 02:00-04:00 UTC
- Duration: 2 hours
- Activities:
  - Dashboard updates
  - Alert rule tuning
  - Metrics cleanup
  - System optimization

## Reporting

### 1. Daily Reports
```bash
# Generate backup report
velero backup get --output json | jq '.items[] | {name: .metadata.name, status: .status.phase, created: .metadata.creationTimestamp}'

# Generate performance report
kubectl logs -n testing -l app=k6 --since 24h

# Generate monitoring report
kubectl get prometheusalerts -n monitoring --all-namespaces
```

### 2. Weekly Reports
```bash
# Generate backup summary
velero backup get --output json | jq '.items[] | select(.metadata.creationTimestamp > "2024-01-01") | {name: .metadata.name, status: .status.phase, created: .metadata.creationTimestamp}'

# Generate performance summary
kubectl logs -n testing -l app=k6 --since 7d

# Generate monitoring summary
kubectl get prometheusalerts -n monitoring --all-namespaces
```

### 3. Monthly Reports
```bash
# Generate backup analysis
velero backup get --output json | jq '.items[] | select(.metadata.creationTimestamp > "2024-01-01") | {name: .metadata.name, status: .status.phase, created: .metadata.creationTimestamp, size: .status.progress.totalBytes}'

# Generate performance analysis
kubectl logs -n testing -l app=k6 --since 30d

# Generate monitoring analysis
kubectl get prometheusalerts -n monitoring --all-namespaces
```

## Best Practices

### 1. Backup Best Practices
- Regular verification
- Incremental backups
- Secure storage
- Retention management

### 2. Performance Testing Best Practices
- Realistic scenarios
- Resource monitoring
- Data analysis
- Continuous improvement

### 3. Monitoring Best Practices
- Proactive monitoring
- Alert tuning
- Dashboard optimization
- Metrics management 