# Networking and Connectivity Guide

## Overview

This document provides a comprehensive overview of the networking architecture and connectivity between different components in our multi-tenant infrastructure. It explains how Kubernetes pods connect to databases, S3 storage, and other AWS services, as well as the security measures implemented to protect these connections.

## Network Architecture

### VPC Configuration

Our infrastructure uses a custom VPC with the following configuration:

- **CIDR Block**: `10.0.0.0/16`
- **Subnets**:
  - **Public Subnets**: `10.0.1.0/23` (eu-central-1a), `10.0.3.0/23` (eu-central-1b)
  - **Private Subnets**: `10.0.5.0/23` (eu-central-1a), `10.0.7.0/23` (eu-central-1b)
- **NAT Gateways**: One per public subnet for high availability
- **Internet Gateway**: Attached to the VPC for public subnet egress

### Security Groups

We use the following security groups to control traffic:

1. **EKS Cluster Security Group**:
   - Allows HTTPS (443) ingress from the VPC CIDR (`10.0.0.0/16`)
   - Allows all egress within the VPC
   - Allows HTTPS (443) egress to AWS services via prefix lists

2. **RDS Security Group**:
   - Allows MySQL (3306) ingress from within the VPC
   - Allows all egress within the VPC

3. **VPC Endpoints Security Group**:
   - Allows HTTPS (443) ingress from the VPC CIDR
   - Allows all egress within the VPC
   - Allows HTTPS (443) egress to S3 via prefix list

4. **ALB Security Group**:
   - Allows HTTPS (443) ingress from specified IP ranges
   - Allows all egress

### VPC Endpoints

We use VPC endpoints to securely access AWS services without going through the public internet:

1. **Gateway Endpoints**:
   - S3 endpoint
   - DynamoDB endpoint

2. **Interface Endpoints**:
   - SSM endpoint
   - EC2 Messages endpoint
   - SSM Messages endpoint
   - CloudWatch Logs endpoint

## Pod-to-Database Connectivity

### Network Path

Pods connect to the database through the following path:

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │     │                 │     │                 │
│ Kubernetes Pod  │────▶│ Network Policy  │────▶│ Security Group  │────▶│ RDS/Aurora DB   │
│                 │     │                 │     │                 │     │                 │
└─────────────────┘     └─────────────────┘     └─────────────────┘     └─────────────────┘
```

When RDS Proxy is enabled, the path becomes:

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │     │                 │     │                 │     │                 │
│ Kubernetes Pod  │────▶│ Network Policy  │────▶│ Security Group  │────▶│  RDS Proxy     │────▶│ RDS/Aurora DB   │
│                 │     │                 │     │                 │     │                 │     │                 │
└─────────────────┘     └─────────────────┘     └─────────────────┘     └─────────────────┘     └─────────────────┘
```

### Network Policies

Each tenant namespace has a network policy that allows:

1. **Ingress**:
   - Traffic from within the same namespace
   - Traffic from the monitoring namespace
   - Traffic from the istio-system namespace (if Istio is enabled)
   - Traffic from the logging namespace

2. **Egress**:
   - DNS resolution (UDP/TCP port 53)
   - Traffic to the same namespace
   - Traffic to the monitoring namespace
   - Traffic to the istio-system namespace (if Istio is enabled)
   - Traffic to AWS services via VPC endpoints (HTTPS port 443)
   - Traffic to RDS (TCP port 3306)

Example network policy:

```yaml
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: tenant-example-isolation
  namespace: tenant-example
spec:
  podSelector: {}
  policyTypes:
  - Ingress
  - Egress
  ingress:
  # Allow traffic from within the same namespace
  - from:
    - podSelector: {}
  # Allow traffic from the monitoring namespace
  - from:
    - namespaceSelector:
        matchLabels:
          name: monitoring
  egress:
  # Allow DNS resolution
  - to:
    - namespaceSelector:
        matchLabels:
          name: kube-system
    ports:
    - protocol: UDP
      port: 53
    - protocol: TCP
      port: 53
  # Allow traffic to RDS
  - to:
    - ipBlock:
        cidr: 10.0.0.0/16
    ports:
    - protocol: TCP
      port: 3306
```

### Authentication

Pods authenticate to the database using IAM database authentication:

1. **IAM Role**: Each tenant has an IAM role with permissions to generate database authentication tokens
2. **Service Account**: Kubernetes service account is associated with the IAM role using IRSA (IAM Roles for Service Accounts)
3. **Authentication Token**: Application generates a short-lived authentication token using the AWS SDK
4. **Database Connection**: Application uses the token to connect to the database

## Pod-to-S3 Connectivity

### Network Path

Pods connect to S3 through the following path:

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │     │                 │     │                 │
│ Kubernetes Pod  │────▶│ Network Policy  │────▶│ VPC Endpoint    │────▶│ S3 Bucket       │
│                 │     │                 │     │                 │     │                 │
└─────────────────┘     └─────────────────┘     └─────────────────┘     └─────────────────┘
```

### Authentication

Pods authenticate to S3 using IAM roles:

1. **IAM Role**: Each tenant has an IAM role with permissions to access its S3 prefix
2. **Service Account**: Kubernetes service account is associated with the IAM role using IRSA
3. **AWS SDK**: Application uses the AWS SDK to access S3, which automatically uses the IAM role credentials

### Prefix-Based Isolation

Each tenant has access only to its own prefix in the S3 bucket:

```
production-assets/
├── tenant-tenant1/
│   ├── uploads/
│   ├── exports/
│   └── backups/
├── tenant-tenant2/
│   ├── uploads/
│   ├── exports/
│   └── backups/
└── ...
```

IAM policies restrict access to the tenant's prefix:

```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "s3:GetObject",
        "s3:PutObject",
        "s3:DeleteObject",
        "s3:ListBucket"
      ],
      "Resource": [
        "arn:aws:s3:::production-assets/tenant-tenant1/*",
        "arn:aws:s3:::production-assets"
      ],
      "Condition": {
        "StringLike": {
          "s3:prefix": "tenant-tenant1/*"
        }
      }
    }
  ]
}
```

## Service Mesh Integration

When Istio service mesh is enabled, it provides additional networking capabilities:

1. **mTLS**: Mutual TLS encryption between services
2. **Traffic Management**: Advanced routing, load balancing, and traffic splitting
3. **Observability**: Detailed metrics, logs, and traces for service-to-service communication

The network path with Istio becomes:

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │     │                 │     │                 │
│ Kubernetes Pod  │────▶│ Istio Sidecar   │────▶│ Network Policy  │────▶│ External Service│
│                 │     │                 │     │                 │     │                 │
└─────────────────┘     └─────────────────┘     └─────────────────┘     └─────────────────┘
```

## External Connectivity

### Ingress Traffic

External traffic enters the cluster through the following path:

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │     │                 │     │                 │
│ Internet        │────▶│ ALB             │────▶│ Istio Ingress   │────▶│ Kubernetes Pod  │
│                 │     │                 │     │ Gateway         │     │                 │
└─────────────────┘     └─────────────────┘     └─────────────────┘     └─────────────────┘
```

### DNS Configuration

We support two DNS configurations:

1. **AWS Route53**: Default configuration using AWS-managed DNS
2. **Hetzner DNS**: External DNS configuration with integration to AWS resources

For Hetzner DNS integration, we use one of two methods:

- **NS Record Delegation**: Delegate specific subdomains to AWS Route53
- **A/CNAME Records**: Point directly to AWS resources from Hetzner DNS

## Security Considerations

### Encryption in Transit

All network traffic is encrypted:

1. **TLS**: HTTPS for all external traffic
2. **mTLS**: Mutual TLS for service-to-service communication (with Istio)
3. **SSL/TLS**: Encrypted connections to RDS and other AWS services

### Network Isolation

We implement multiple layers of network isolation:

1. **VPC**: Isolated network environment
2. **Subnets**: Separation of public and private resources
3. **Security Groups**: Granular control of traffic between resources
4. **Network Policies**: Kubernetes-level traffic control
5. **Service Mesh**: Additional layer of traffic management and security

### Firewall Integration

For integration with external firewalls (e.g., Untangle Firewall):

1. **IP Allowlisting**: Configure security groups to allow traffic from firewall IP
2. **VPN Connectivity**: Site-to-site VPN for secure communication
3. **Routing**: Configure routing tables to route traffic through the firewall

## Troubleshooting

### Common Issues

1. **Network Policy Issues**:
   - Check if network policies allow traffic to the required destinations
   - Verify that namespace labels match the selectors in network policies

2. **Security Group Issues**:
   - Verify that security groups allow traffic on the required ports
   - Check if CIDR blocks are correctly configured

3. **DNS Resolution Issues**:
   - Verify that DNS resolution is allowed in network policies
   - Check if CoreDNS is functioning correctly

### Diagnostic Commands

```bash
# Check network policies in a namespace
kubectl get networkpolicies -n tenant-example

# Describe a network policy
kubectl describe networkpolicy tenant-example-isolation -n tenant-example

# Test connectivity from a pod
kubectl exec -it -n tenant-example pod-name -- curl -v telnet://rds-endpoint:3306

# Check AWS security groups
aws ec2 describe-security-groups --group-ids sg-xxxxxxxxx

# Test VPC endpoint connectivity
aws ec2 describe-vpc-endpoints --filters Name=vpc-id,Values=vpc-xxxxxxxxx
```

## Best Practices

1. **Least Privilege**: Grant only the necessary network access
2. **Defense in Depth**: Implement multiple layers of network security
3. **Encryption**: Encrypt all network traffic
4. **Monitoring**: Monitor network traffic for suspicious activity
5. **Regular Audits**: Regularly audit network policies and security groups
6. **Documentation**: Keep network documentation up-to-date
7. **Automation**: Automate network configuration to reduce human error
