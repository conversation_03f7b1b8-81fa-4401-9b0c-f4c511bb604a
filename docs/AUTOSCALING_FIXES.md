# Comprehensive Autoscaling Fixes Documentation

## Overview

This document outlines the comprehensive fixes implemented to ensure **KEDA (Kubernetes Event-Driven Autoscaling)**, **Karpenter**, and **Cluster Autoscaler** work automatically in your Kubernetes environment.

## Problem Analysis

### Why Autoscaling Wasn't Working Automatically

1. **KEDA Issues:**
   - KEDA not installed or not running properly
   - Conflicting HPAs preventing KEDA ScaledObjects from working
   - Missing metrics server for KEDA to collect metrics
   - Incorrect ScaledObject configurations

2. **Karpenter Issues:**
   - Karpenter not installed or not running
   - Missing provisioner configurations
   - IAM permissions not properly configured
   - Node group constraints not set up

3. **Cluster Autoscaler Issues:**
   - Cluster Autoscaler not running or misconfigured
   - ASG (Auto Scaling Group) limits preventing scaling
   - Node pressure preventing new pod scheduling

## Implemented Solutions

### 1. Enhanced Go Onboarding Script (`advanced_tenant_onboard.go`)

#### New Functions Added:

##### `ensureAutoscalingInfrastructure()`
- Checks and fixes KED<PERSON>, <PERSON><PERSON><PERSON>, and Cluster Autoscaler
- Automatically installs missing components
- Restarts unhealthy components

##### `checkAndFixKEDA()`
- Verifies KEDA namespace and operator pods
- Installs KEDA via Helm if missing
- Restarts KEDA operator if unhealthy

##### `checkAndFixKarpenter()`
- Verifies Karpenter namespace and controller
- Installs Karpenter via Helm if missing
- Creates default provisioner if none exists

##### `checkAndFixClusterAutoscaler()`
- Verifies Cluster Autoscaler pods are running
- Restarts Cluster Autoscaler if unhealthy

##### `cleanupConflictingHPAs()`
- Removes HPAs that conflict with KEDA ScaledObjects
- Ensures KEDA can manage scaling exclusively

##### `createKEDAScaledObjects()`
- Creates comprehensive KEDA ScaledObjects with multiple triggers:
  - CPU-based scaling (70% utilization)
  - Memory-based scaling (80% utilization)
  - Prometheus-based HTTP request scaling
  - RabbitMQ queue-based scaling
- Replaces traditional HPAs with more flexible KEDA scaling

##### `createVPAs()`
- Creates Vertical Pod Autoscalers for resource optimization
- Automatically adjusts CPU and memory requests/limits

##### `createPodDisruptionBudget()`
- Ensures high availability during scaling events
- Prevents all pods from being terminated simultaneously

##### `runAutoscalingHealthCheck()`
- Comprehensive health check for all autoscaling components
- Monitors node pressure, pending pods, and metrics availability
- Provides detailed status reporting

### 2. Autoscaling Monitor Script (`scripts/autoscaling/autoscaling-monitor.sh`)

#### Features:
- **Monitoring Mode:** Check status of all autoscaling components
- **Auto-Fix Mode:** Automatically fix common issues
- **Install Mode:** Install missing components
- **Interactive Mode:** Ask for confirmation before making changes

#### Usage:
```bash
# Monitor autoscaling components
./scripts/autoscaling/autoscaling-monitor.sh

# Auto-fix issues
./scripts/autoscaling/autoscaling-monitor.sh --fix

# Install missing components
./scripts/autoscaling/autoscaling-monitor.sh --install
```

## KEDA Configuration

### ScaledObject Configuration
```yaml
apiVersion: keda.sh/v1alpha1
kind: ScaledObject
metadata:
  name: tenant-backend-scaler
  namespace: tenant-namespace
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: tenant-backend
  minReplicaCount: 2
  maxReplicaCount: 20
  pollingInterval: 15
  cooldownPeriod: 300
  advanced:
    horizontalPodAutoscalerConfig:
      behavior:
        scaleDown:
          stabilizationWindowSeconds: 300
          policies:
          - type: Percent
            value: 50
            periodSeconds: 60
        scaleUp:
          stabilizationWindowSeconds: 0
          policies:
          - type: Percent
            value: 100
            periodSeconds: 30
          - type: Pods
            value: 2
            periodSeconds: 30
          selectPolicy: Max
  triggers:
  # CPU-based scaling
  - type: cpu
    metricType: Utilization
    metadata:
      value: "70"
  # Memory-based scaling
  - type: memory
    metricType: Utilization
    metadata:
      value: "80"
  # HTTP request-based scaling
  - type: prometheus
    metadata:
      serverAddress: http://prometheus-server.monitoring.svc.cluster.local:9090
      metricName: http_requests_per_second
      query: sum(rate(istio_requests_total{destination_service_name="tenant-backend"}[2m]))
      threshold: "50"
  # Queue-based scaling (RabbitMQ)
  - type: rabbitmq
    metadata:
      protocol: http
      host: tenant-rabbitmq.tenant-namespace.svc.cluster.local
      port: "15672"
      queueName: tenant-queue
      mode: QueueLength
      value: "5"
      username: guest
      password: guest
```

## Karpenter Configuration

### Provisioner Configuration
```yaml
apiVersion: karpenter.sh/v1alpha5
kind: Provisioner
metadata:
  name: default
  namespace: karpenter
spec:
  requirements:
    - key: karpenter.sh/capacity-type
      operator: In
      values: ["on-demand", "spot"]
    - key: kubernetes.io/arch
      operator: In
      values: ["amd64"]
    - key: node.kubernetes.io/instance-type
      operator: In
      values: ["t3.medium", "t3.large", "m5.large", "m5.xlarge"]
  limits:
    resources:
      cpu: 100
      memory: 100Gi
  ttlSecondsAfterEmpty: 60
  ttlSecondsUntilExpired: 2592000
```

## VPA Configuration

### Backend VPA
```yaml
apiVersion: autoscaling.k8s.io/v1
kind: VerticalPodAutoscaler
metadata:
  name: tenant-backend-vpa
  namespace: tenant-namespace
spec:
  targetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: tenant-backend
  updatePolicy:
    updateMode: "Auto"
  resourcePolicy:
    containerPolicies:
    - containerName: "*"
      minAllowed:
        cpu: 100m
        memory: 128Mi
      maxAllowed:
        cpu: 2
        memory: 4Gi
      controlledResources: ["cpu", "memory"]
```

## PodDisruptionBudget Configuration

```yaml
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: tenant-backend-pdb
  namespace: tenant-namespace
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: tenant-backend
```

## Health Check Integration

### Automatic Health Checks
The Go onboarding script now includes comprehensive health checks:

1. **KEDA ScaledObjects Verification**
2. **KEDA Operator Status**
3. **Karpenter Controller Status**
4. **Cluster Autoscaler Status**
5. **Node Capacity and Pressure**
6. **Pending Pods Detection**
7. **Metrics Server Availability**

### Health Check Output Example
```
🔍 Running comprehensive autoscaling health check for tenant auto-tenant-**********
🔍 Checking KEDA ScaledObjects...
✅ Found 2 KEDA ScaledObjects
   - auto-tenant-**********-backend-scaler
   - auto-tenant-**********-frontend-scaler
🔍 Checking KEDA operator status...
✅ KEDA operator is healthy
🔍 Checking Karpenter status...
✅ Karpenter controller is healthy
🔍 Checking Cluster Autoscaler status...
✅ Cluster Autoscaler is healthy
🔍 Checking node capacity...
✅ Found 3 nodes
✅ No nodes under pressure
✅ No pending pods found
🔍 Checking autoscaling metrics...
✅ Node metrics available
✅ Autoscaling health check completed for tenant auto-tenant-**********
```

## Troubleshooting Guide

### Common Issues and Solutions

#### 1. KEDA Not Scaling
**Symptoms:** Pods not scaling despite high load
**Solutions:**
- Check for conflicting HPAs: `kubectl get hpa --all-namespaces`
- Verify KEDA operator is running: `kubectl get pods -n keda`
- Check ScaledObject status: `kubectl get scaledobject --all-namespaces`

#### 2. Karpenter Not Provisioning Nodes
**Symptoms:** Pending pods due to insufficient resources
**Solutions:**
- Verify Karpenter controller: `kubectl get pods -n karpenter`
- Check provisioner: `kubectl get provisioner -n karpenter`
- Verify IAM permissions for Karpenter

#### 3. Cluster Autoscaler Not Working
**Symptoms:** Nodes not scaling up/down
**Solutions:**
- Check Cluster Autoscaler logs: `kubectl logs -n kube-system -l app=cluster-autoscaler`
- Verify ASG limits and policies
- Check node pressure conditions

#### 4. Metrics Server Issues
**Symptoms:** `kubectl top` commands failing
**Solutions:**
- Install metrics server: `kubectl apply -f https://github.com/kubernetes-sigs/metrics-server/releases/latest/download/components.yaml`
- Verify metrics server is running: `kubectl get pods -n kube-system -l k8s-app=metrics-server`

### Manual Fix Commands

```bash
# Fix KEDA
kubectl delete pods -n keda -l app.kubernetes.io/name=keda-operator
helm upgrade --install keda kedacore/keda --namespace keda --create-namespace

# Fix Karpenter
kubectl delete pods -n karpenter -l app.kubernetes.io/name=karpenter
helm upgrade --install karpenter karpenter/karpenter --namespace karpenter --create-namespace

# Fix Cluster Autoscaler
kubectl delete pods -n kube-system -l app=cluster-autoscaler

# Clean up conflicting HPAs
kubectl delete hpa --all-namespaces --field-selector=metadata.name=backend-hpa
```

## Monitoring and Alerts

### Key Metrics to Monitor

1. **KEDA Metrics:**
   - ScaledObject count
   - Scaling events per minute
   - Trigger activation status

2. **Karpenter Metrics:**
   - Node provisioning rate
   - Node termination rate
   - Provisioner utilization

3. **Cluster Autoscaler Metrics:**
   - Scale-up events
   - Scale-down events
   - Node group utilization

### Recommended Alerts

```yaml
# KEDA Operator Down
- alert: KEDAOperatorDown
  expr: kube_deployment_status_replicas_available{deployment="keda-operator"} == 0
  for: 5m

# Karpenter Controller Down
- alert: KarpenterControllerDown
  expr: kube_deployment_status_replicas_available{deployment="karpenter"} == 0
  for: 5m

# High Pending Pods
- alert: HighPendingPods
  expr: kube_pod_status_phase{phase="Pending"} > 10
  for: 10m

# Node Pressure
- alert: NodeDiskPressure
  expr: kube_node_status_condition{condition="DiskPressure",status="true"} == 1
  for: 5m
```

## Best Practices

### 1. Resource Planning
- Set appropriate min/max replica counts
- Use resource requests and limits
- Monitor resource utilization patterns

### 2. Scaling Policies
- Use conservative scale-down policies
- Implement aggressive scale-up policies
- Set appropriate cooldown periods

### 3. Monitoring
- Monitor scaling events and patterns
- Set up alerts for scaling failures
- Track cost implications of scaling

### 4. Testing
- Test scaling behavior under load
- Verify scaling limits and policies
- Test failure scenarios

## Conclusion

These comprehensive fixes ensure that:

1. **KEDA** automatically scales pods based on CPU, memory, HTTP requests, and queue length
2. **Karpenter** automatically provisions and terminates nodes based on demand
3. **Cluster Autoscaler** works alongside Karpenter for additional scaling capabilities
4. **Health checks** continuously monitor and fix issues automatically
5. **Comprehensive monitoring** provides visibility into scaling behavior

The system now provides **true automatic scaling** without manual intervention, ensuring optimal resource utilization and application performance. 