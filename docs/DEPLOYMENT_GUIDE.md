# Deployment Guide for New Components

## Table of Contents
1. [Prerequisites](#prerequisites)
2. [Component Overview](#component-overview)
3. [Deployment Steps](#deployment-steps)
4. [Verification](#verification)
5. [Monitoring Setup](#monitoring-setup)
6. [Troubleshooting](#troubleshooting)

## Prerequisites

### Required Tools
- kubectl (v1.21 or later)
- velero CLI (v1.9.0 or later)
- AWS CLI (for S3 access)
- jq (for JSON processing)

### Required Access
- Kubernetes cluster admin access
- AWS S3 bucket access
- Grafana admin access
- Prometheus access

### Required Resources
- S3 bucket: `architrave-backups`
- AWS credentials with S3 access
- Sufficient cluster resources for monitoring components

## Component Overview

### 1. Backup and Disaster Recovery
- Daily automated backups using Velero
- 30-day retention policy
- AWS S3 storage integration
- Backup verification
- Monitoring and alerts

### 2. Performance Testing
- Load testing with k6
- Scalability testing
- Performance monitoring
- Alert thresholds
- Automated test execution

### 3. Monitoring Components
- Grafana dashboards
- Prometheus rules
- ServiceMonitors
- Alert configurations

## Deployment Steps

### 1. Prepare Environment

```bash
# Create necessary directories
mkdir -p monitoring backup testing

# Set up AWS credentials for Velero
cat > credentials-velero <<EOF
[default]
aws_access_key_id=<your-access-key>
aws_secret_access_key=<your-secret-key>
EOF
```

### 2. Deploy Backup Components

```bash
# Make deployment script executable
chmod +x deploy-additional-components.sh

# Run backup deployment
./deploy-additional-components.sh
```

### 3. Deploy Monitoring Components

```bash
# Make monitoring deployment script executable
chmod +x deploy-monitoring.sh

# Run monitoring deployment
./deploy-monitoring.sh
```

### 4. Configure Backup Schedule

```bash
# Verify backup schedule
kubectl get schedule -n velero

# Check backup configuration
kubectl get backupstoragelocation -n velero
```

### 5. Set Up Performance Testing

```bash
# Verify k6 deployment
kubectl get pods -n testing -l app=k6

# Check performance test configuration
kubectl get configmap -n testing k6-test-script
```

## Verification

### 1. Verify Backup Setup

```bash
# Check Velero pods
kubectl get pods -n velero

# Verify backup storage location
kubectl get backupstoragelocation -n velero

# Check backup schedule
kubectl get schedule -n velero
```

### 2. Verify Monitoring Setup

```bash
# Check Grafana dashboards
kubectl get configmap -n monitoring -l grafana_dashboard=true

# Verify ServiceMonitors
kubectl get servicemonitor -n monitoring

# Check Prometheus rules
kubectl get prometheusrules -n monitoring
```

### 3. Verify Performance Testing

```bash
# Check k6 deployment
kubectl get pods -n testing -l app=k6

# Verify test configuration
kubectl get configmap -n testing k6-test-script
```

## Monitoring Setup

### 1. Access Grafana Dashboards

- Backup Monitoring: `http://grafana.monitoring:3000/d/backup-monitoring`
- Performance Monitoring: `http://grafana.monitoring:3000/d/performance-monitoring`

### 2. Configure Alerts

The following alerts are pre-configured:

#### Backup Alerts
- BackupFailed: Triggers when backup fails
- BackupVerificationFailed: Triggers when verification fails
- HighBackupDuration: Triggers when backup takes > 1 hour
- HighBackupStorageUsage: Triggers when storage > 1TB

#### Performance Alerts
- HighLatency: Triggers when response time > 500ms
- HighErrorRate: Triggers when error rate > 10%
- HighRequestRate: Triggers when requests > 1000 req/s

### 3. Monitor Metrics

Key metrics to monitor:

#### Backup Metrics
- Backup success/failure rate
- Backup duration
- Storage usage
- Verification status

#### Performance Metrics
- Response time
- Request rate
- Error rate
- Resource utilization

## Troubleshooting

### Common Issues

1. **Backup Issues**
   ```bash
   # Check Velero logs
   kubectl logs -n velero -l app.kubernetes.io/name=velero
   
   # Verify backup status
   velero backup describe <backup-name>
   ```

2. **Monitoring Issues**
   ```bash
   # Check Prometheus status
   kubectl get pods -n monitoring -l app=prometheus
   
   # Check Grafana status
   kubectl get pods -n monitoring -l app=grafana
   ```

3. **Performance Testing Issues**
   ```bash
   # Check k6 logs
   kubectl logs -n testing -l app=k6
   
   # Verify test results
   kubectl logs -n testing -l app=k6 -c k6
   ```

### Recovery Procedures

1. **Backup Recovery**
   ```bash
   # List available backups
   velero backup get
   
   # Restore from backup
   velero restore create --from-backup <backup-name>
   ```

2. **Monitoring Recovery**
   ```bash
   # Restart Prometheus
   kubectl rollout restart deployment -n monitoring prometheus-server
   
   # Restart Grafana
   kubectl rollout restart deployment -n monitoring grafana
   ```

3. **Performance Testing Recovery**
   ```bash
   # Restart k6
   kubectl rollout restart deployment -n testing k6
   ```

## Maintenance

### Regular Tasks

1. **Backup Maintenance**
   - Monitor backup success rates
   - Review storage usage
   - Verify backup retention
   - Test restore procedures

2. **Monitoring Maintenance**
   - Review alert thresholds
   - Update dashboard configurations
   - Clean up old metrics
   - Verify alert notifications

3. **Performance Testing Maintenance**
   - Update test scenarios
   - Review performance baselines
   - Adjust load patterns
   - Update test configurations

### Update Procedures

1. **Update Velero**
   ```bash
   # Update Velero
   velero install --provider aws --plugins velero/velero-plugin-for-aws:v1.5.0 --bucket architrave-backups --backup-location-config region=eu-west-2 --secret-file ./credentials-velero --namespace velero
   ```

2. **Update Monitoring**
   ```bash
   # Update dashboards
   kubectl apply -f monitoring/backup-dashboard.yaml
   kubectl apply -f monitoring/performance-dashboard.yaml
   ```

3. **Update Performance Testing**
   ```bash
   # Update k6 configuration
   kubectl apply -f testing/performance-test.yaml
   ```

## Support

For support issues:
1. Check the troubleshooting section
2. Review component logs
3. Contact the support <NAME_EMAIL>
4. Reference the component documentation 