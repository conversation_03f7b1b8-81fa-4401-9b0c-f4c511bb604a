# Disaster Recovery Plan

This document outlines the disaster recovery procedures for the Sample Application.

## Backup Strategy

The Sample Application uses <PERSON>elero to back up the Kubernetes resources and persistent volumes. Backups are scheduled to run daily at 1:00 AM.

### Backup Components

- **Kubernetes Resources**: All Kubernetes resources in the `sample-app` namespace are backed up.
- **Persistent Volumes**: All persistent volumes used by the application are backed up.
- **Backup Storage**: Backups are stored in an S3 bucket.

## Recovery Procedures

### Full Cluster Recovery

In case of a complete cluster failure, follow these steps to recover:

1. Create a new EKS cluster:

```bash
terraform apply -target=module.eks
```

2. Install Velero on the new cluster:

```bash
kubectl apply -f kubernetes/backup.yaml
```

3. List available backups:

```bash
velero backup get
```

4. Restore the latest backup:

```bash
velero restore create --from-backup <backup-name>
```

5. Verify the restoration:

```bash
kubectl get pods -n sample-app
```

### Single Namespace Recovery

In case of a namespace failure, follow these steps to recover:

1. List available backups:

```bash
velero backup get
```

2. Restore the namespace from the latest backup:

```bash
velero restore create --from-backup <backup-name> --include-namespaces sample-app
```

3. Verify the restoration:

```bash
kubectl get pods -n sample-app
```

### Single Resource Recovery

In case of a single resource failure, follow these steps to recover:

1. List available backups:

```bash
velero backup get
```

2. Restore the specific resource from the latest backup:

```bash
velero restore create --from-backup <backup-name> --include-resources deployments,services --include-namespaces sample-app
```

3. Verify the restoration:

```bash
kubectl get deployments,services -n sample-app
```

## Testing Recovery Procedures

It is important to regularly test the recovery procedures to ensure they work as expected. Follow these steps to test the recovery procedures:

1. Create a test backup:

```bash
velero backup create sample-app-test-backup --include-namespaces sample-app
```

2. Simulate a failure by deleting the namespace:

```bash
kubectl delete namespace sample-app
```

3. Restore from the test backup:

```bash
velero restore create --from-backup sample-app-test-backup
```

4. Verify the restoration:

```bash
kubectl get pods -n sample-app
```

## Disaster Recovery Team

The following team members are responsible for disaster recovery:

- **Primary Contact**: [Name], [Email], [Phone]
- **Secondary Contact**: [Name], [Email], [Phone]
- **Tertiary Contact**: [Name], [Email], [Phone]

## Recovery Time Objectives (RTO)

- **Full Cluster Recovery**: 2 hours
- **Single Namespace Recovery**: 30 minutes
- **Single Resource Recovery**: 15 minutes

## Recovery Point Objectives (RPO)

- **Daily Backups**: 24 hours
- **Manual Backups**: On-demand
