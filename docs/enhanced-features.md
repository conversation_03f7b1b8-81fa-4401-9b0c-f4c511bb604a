# Enhanced Infrastructure Features

This document provides an overview of the enhanced features implemented in the infrastructure.

## Table of Contents

1. [Autoscaling](#autoscaling)
2. [Istio Service Mesh](#istio-service-mesh)
3. [Tenant Backup and Restore](#tenant-backup-and-restore)
4. [Expanded Dashboard Templates](#expanded-dashboard-templates)
5. [Testing](#testing)

## Autoscaling

The infrastructure includes comprehensive autoscaling capabilities to optimize resource usage and ensure high availability, with a focus on minimal resource consumption for tenant workloads.

### Horizontal Pod Autoscaler (HPA)

HPA automatically scales the number of pods in a deployment, stateful set, or replica set based on observed CPU utilization or other select metrics.

**Key Features:**
- CPU and memory-based scaling
- Custom metrics support
- Advanced scaling behavior configuration
- Stabilization windows to prevent thrashing
- Rapid scale-up during traffic spikes

**Configuration:**
- Located in `kubernetes/autoscaling/horizontal-pod-autoscaler.yaml`
- Tenant-specific configuration in `kubernetes/autoscaling/tenant-autoscaling.yaml`
- Default tenant configuration:
  - Min replicas: 1
  - Max replicas: 5
  - CPU target utilization: 70%
  - Memory target utilization: 75%
  - Scale-up stabilization window: 30 seconds
  - Scale-down stabilization window: 300 seconds (5 minutes)

### Vertical Pod Autoscaler (VPA)

VPA automatically adjusts the CPU and memory requests and limits for containers in pods, allowing them to use resources more efficiently.

**Key Features:**
- Automatic resource request/limit adjustment
- Different update modes (Auto, Initial, Off)
- Container-specific policies
- Resource min/max constraints
- Optimized for minimal resource usage

**Configuration:**
- Located in `kubernetes/autoscaling/vertical-pod-autoscaler.yaml`
- Tenant-specific configuration in `kubernetes/autoscaling/tenant-autoscaling.yaml`
- Default tenant configuration:
  - Update mode: Auto
  - Min CPU: 30m
  - Min Memory: 48Mi
  - Max CPU: 300m
  - Max Memory: 384Mi

### Pod Disruption Budget (PDB)

PDB limits the number of pods that can be down simultaneously during voluntary disruptions, ensuring high availability.

**Key Features:**
- Minimum availability guarantees
- Maximum unavailability constraints
- Works with node drains, upgrades, and scaling operations

**Configuration:**
- Located in `kubernetes/autoscaling/pod-disruption-budget.yaml`
- Tenant-specific configuration in `kubernetes/autoscaling/tenant-autoscaling.yaml`
- Default tenant configuration:
  - Minimum available: 1 pod

### Karpenter Node Provisioning

Karpenter is an advanced node provisioner that automatically provisions new nodes in response to unschedulable pods.

**Key Features:**
- Just-in-time node provisioning
- Bin-packing for optimal resource utilization
- Spot instance support for cost optimization
- Node consolidation for efficient resource usage

**Configuration:**
- Located in `kubernetes/autoscaling/karpenter-tenant-provisioner.yaml`
- Optimized for tenant workloads with minimal resource requirements

### KEDA Event-Driven Autoscaling

KEDA (Kubernetes Event-Driven Autoscaling) extends Kubernetes with event-driven autoscaling capabilities.

**Key Features:**
- Scaling based on event sources (SQS, Prometheus, etc.)
- Multiple triggers for scaling decisions
- Fine-grained scaling control

**Configuration:**
- Located in `kubernetes/autoscaling/keda-tenant-scaledobject.yaml`
- Supports various triggers including CPU, memory, HTTP requests, and queue length

### Minimal Resource Configuration

Tenant pods are configured with minimal resource requests to optimize resource utilization:

```yaml
resources:
  requests:
    cpu: 50m
    memory: 64Mi
  limits:
    cpu: 200m
    memory: 256Mi
```

This configuration allows for efficient resource usage while still providing room for scaling.

## Istio Service Mesh

Istio is an open-source service mesh that provides a uniform way to connect, secure, control, and observe microservices.

### Key Features

- **Traffic Management**: Intelligent routing and load balancing
- **Security**: Mutual TLS, authentication, and authorization
- **Observability**: Distributed tracing, monitoring, and logging
- **Platform Support**: Runs on Kubernetes with minimal resource requirements

### Components

The minimal Istio deployment includes:

- **Istiod**: The Istio control plane (includes Pilot, Citadel, and Galley)
- **Ingress Gateway**: For external traffic entering the mesh
- **Sidecar Injector**: For automatic sidecar injection into pods

### Configuration

- Located in `kubernetes/istio/minimal-istio-config.yaml`
- Optimized for small clusters with minimal resource requirements

### Deployment

Use the provided script to deploy Istio:

```bash
./scripts/deploy-istio.sh
```

## Tenant Backup and Restore

The tenant backup and restore system provides comprehensive capabilities for backing up and restoring tenant resources.

### Key Features

- **Automated Backups**: Scheduled backups of tenant resources using Kubernetes CronJobs
- **Comprehensive Backup**: Backs up Kubernetes resources, S3 data, and database schemas/data
- **Flexible Restore**: Ability to restore tenants from any previous backup
- **Verification**: Verification of backup and restore operations

### Components

- **Backup CronJobs**: Scheduled backups for tenants
- **Backup Scripts**: Scripts for backing up tenant resources
- **Restore Scripts**: Scripts for restoring tenant resources
- **Verification**: Scripts for verifying backup and restore operations

### Configuration

- Located in `kubernetes/tenant-backup-cronjob/`
- Includes backup and restore scripts, CronJobs, and service accounts

### Usage

To restore a tenant from a backup:

```bash
./scripts/tenant-restore.sh --tenant-id abc --environment Production [--backup-id ********-120000]
```

## Expanded Dashboard Templates

The infrastructure includes expanded dashboard templates for monitoring various aspects of the infrastructure.

### Available Dashboards

- **Kubernetes Dashboard**: Comprehensive view of your Kubernetes cluster
- **RDS Dashboard**: Monitors your RDS database instances
- **Tenant Monitoring Dashboard**: Provides tenant-specific monitoring

### Key Features

- **Variable Support**: Dashboards use template variables for filtering
- **Multi-Datasource**: Metrics from Prometheus, CloudWatch, and MySQL
- **Alerting**: Pre-configured alert thresholds
- **Responsive Layout**: Optimized for different screen sizes

### Configuration

- Dashboard templates located in `dashboards/`
- Grafana configuration in `modules/grafana/templates/grafana-values-enhanced.yaml`

### Tenant-Specific Dashboards

For tenant-specific monitoring:

1. Use the `tenant-monitoring-dashboard.json` as a template
2. Set the `tenant_id` variable to the specific tenant ID
3. Save the dashboard with a tenant-specific name

## Testing

The infrastructure includes comprehensive testing capabilities to ensure reliability and security.

### Testing Framework

The testing framework follows a multi-layered approach:

1. **Static Analysis**: Checks code formatting, syntax, and best practices
2. **Security Scanning**: Identifies security vulnerabilities and compliance issues
3. **Policy Validation**: Ensures adherence to organizational policies
4. **Unit Testing**: Tests individual modules and resources
5. **Integration Testing**: Tests interactions between different components
6. **Cost Estimation**: Analyzes the cost implications of infrastructure changes
7. **Drift Detection**: Identifies differences between the actual infrastructure and the Terraform state

### Testing Tools

- **Terraform Format**: Ensures consistent code formatting
- **Terraform Validate**: Checks syntax and internal consistency
- **TFLint**: Lints Terraform code for potential errors and best practices
- **TFSec**: Identifies security vulnerabilities in Terraform code
- **Checkov**: Scans for security and compliance issues
- **OPA/Conftest**: Validates infrastructure against custom policies
- **Terratest**: Go framework for testing Terraform code
- **Infracost**: Estimates the cost of infrastructure changes

### CI/CD Integration

The testing framework is integrated into the CI/CD pipeline with the following stages:

1. **Validate**: Runs static analysis and basic validation
2. **Plan**: Creates a Terraform plan
3. **Security**: Runs security scans and policy checks
4. **Apply**: Applies the Terraform plan (manual approval required)

### Running Tests Locally

You can run the tests locally using the Makefile:

```bash
# Run all tests
make test

# Run specific tests
make fmt
make validate
make security
make terratest
```
