# Security Policy Customization and Failure Conditions

This document explains the customized security policies and failure conditions implemented in the GitLab CI pipeline.

## Overview

We've updated the security scanning tools in the GitLab CI pipeline to:

1. Use customized policies for each security scanning tool
2. Add failure conditions to fail the pipeline when critical issues are found

## Customized Policies

### 1. TFSec Policies (`tfsec.yaml`)

The TFSec configuration includes:

- **Excluded Checks**: 
  - `aws-rds-deletion-protection-enabled` - Allows RDS instances without deletion protection
  - `aws-s3-enable-bucket-logging` - Allows S3 buckets without logging

- **Custom Checks**:
  - `CUSTOM-AWS-001`: Ensures all resources have proper tags
  - `CUSTOM-AWS-002`: Ensures KMS encryption is used for sensitive data

### 2. Checkov Policies (`.checkov.yaml`)

The Checkov configuration includes:

- **Skipped Checks**:
  - `CKV_AWS_157` - Allows RDS instances without deletion protection
  - `CKV_AWS_18` - Allows S3 buckets without access logging

- **Custom Policies**:
  - `CKV_CUSTOM_AWS_1`: Ensures S3 buckets have versioning enabled
  - `CKV_CUSTOM_AWS_2`: Ensures no security group allows ingress from 0.0.0.0/0 to port 22

### 3. TFLint Rules (`.tflint.hcl`)

The TFLint configuration includes:

- **AWS Plugin**: Enabled with version 0.28.0
- **Terraform Plugin**: Enabled with recommended preset
- **Enabled Rules**:
  - `terraform_comment_syntax`
  - `terraform_deprecated_index`
  - `terraform_deprecated_interpolation`
  - `terraform_documented_outputs`
  - `terraform_documented_variables`
  - `terraform_module_pinned_source`
  - `terraform_naming_convention` (snake_case)
  - `terraform_required_providers`
  - `terraform_required_version`
  - `terraform_standard_module_structure`
  - `terraform_typed_variables`
  - `terraform_unused_required_providers`
  - `terraform_workspace_remote`
- **Disabled Rules**:
  - `terraform_unused_declarations` (Disabled as we have many variables for different environments)
- **Ignored Modules**:
  - `terraform-aws-modules/vpc/aws`
  - `terraform-aws-modules/security-group/aws`

### 4. OPA/Conftest Policies (`policies/terraform.rego`)

The OPA/Conftest policies include:

- **S3 Bucket Policies**:
  - Deny S3 buckets without encryption
  - Deny S3 buckets without versioning
  - Deny S3 buckets without public access block

- **RDS Policies**:
  - Deny RDS instances without encryption

- **EKS Policies**:
  - Deny EKS clusters without encryption

- **Security Group Policies**:
  - Deny security groups with 0.0.0.0/0 ingress to port 22

- **Tagging Policies**:
  - Ensure all resources have tags

## Failure Conditions

The following failure conditions have been implemented:

### 1. TFSec

- **Critical Issues**: The pipeline will fail if any critical issues are found
- **High Issues**: The pipeline will display warnings but continue

### 2. Checkov

- **Critical Issues**: The pipeline will fail if any critical issues are found
- **High Issues**: The pipeline will display warnings but continue

### 3. TFLint

- **Errors**: The pipeline will fail if any errors are found
- **Warnings**: The pipeline will display warnings but continue
- **Notices**: The pipeline will display notices but continue

### 4. OPA/Conftest

- **Policy Violations**: The pipeline will fail if any policy violations are found

## How to Customize Further

### Adding More TFSec Custom Checks

Add more custom checks to the `tfsec.yaml` file:

```yaml
custom_checks:
  - code: CUSTOM-AWS-003
    description: "Your custom check description"
    impact: "Impact description"
    resolution: "Resolution description"
    requiredTypes:
      - resource
    requiredLabels:
      - aws_resource_type
    severity: HIGH
    matchSpec:
      name: property_name
      action: isPresent
    errorMessage: "Error message"
```

### Adding More Checkov Custom Policies

Add more custom policies to the `.checkov.yaml` file:

```yaml
custom_policies:
  aws_resource_type:
    - id: "CKV_CUSTOM_AWS_3"
      name: "Your custom policy name"
      severity: "CRITICAL"
      definition:
        cond_type: "attribute"
        resource_types:
          - "aws_resource_type"
        attribute: "property.name"
        operator: "equals"
        value: "expected_value"
```

### Adding More TFLint Rules

Enable or disable rules in the `.tflint.hcl` file:

```hcl
rule "rule_name" {
  enabled = true
}
```

### Adding More OPA/Conftest Policies

Add more policies to the `policies/terraform.rego` file:

```rego
# New policy
deny[msg] {
    input.resource.aws_resource_type[name]
    not has_required_property(name)
    msg = sprintf("Resource '%v' does not have required property", [name])
}

has_required_property(resource_name) {
    input.resource.aws_resource_type[resource_name].property_name == "expected_value"
}
```

## Adjusting Failure Conditions

To adjust the failure conditions:

1. **Make Critical Issues Non-Blocking**: Comment out the `exit 1` lines in the script sections
2. **Make High Issues Blocking**: Uncomment the `# exit 1` lines after high severity issue checks
3. **Add Medium Issue Checks**: Add similar checks for medium severity issues with appropriate failure conditions
