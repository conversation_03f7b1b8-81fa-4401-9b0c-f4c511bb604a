# Missing Components Deployment Guide

This guide provides detailed instructions for deploying any missing infrastructure components identified during verification.

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Service Mesh Deployment](#service-mesh-deployment)
   - [Istio Installation](#istio-installation)
   - [Istio Gateways Configuration](#istio-gateways-configuration)
   - [mTLS Configuration](#mtls-configuration)
   - [Kiali Dashboard Deployment](#kiali-dashboard-deployment)
3. [Advanced Monitoring Deployment](#advanced-monitoring-deployment)
   - [Prometheus Installation](#prometheus-installation)
   - [Grafana Deployment](#grafana-deployment)
   - [Loki Installation](#loki-installation)
   - [Jaeger Deployment](#jaeger-deployment)
   - [CloudWatch Integration](#cloudwatch-integration)
4. [Advanced Autoscaling Deployment](#advanced-autoscaling-deployment)
   - [KEDA Installation](#keda-installation)
   - [Vertical Pod Autoscaler (VPA)](#vertical-pod-autoscaler-vpa)
   - [Horizontal Pod Autoscaler (HPA)](#horizontal-pod-autoscaler-hpa)
   - [Pod Disruption Budgets (PDBs)](#pod-disruption-budgets-pdbs)
   - [Goldilocks Installation](#goldilocks-installation)
   - [Karpenter Configuration](#karpenter-configuration)
5. [Tenant Functionality Implementation](#tenant-functionality-implementation)
   - [Tenant Resource Creation](#tenant-resource-creation)
   - [Tenant Onboarding Script Testing](#tenant-onboarding-script-testing)
   - [Tenant Isolation Verification](#tenant-isolation-verification)
   - [Tenant Resource Quotas](#tenant-resource-quotas)
   - [Tenant Networking Policies](#tenant-networking-policies)
6. [CI/CD Pipeline Configuration](#cicd-pipeline-configuration)
7. [Additional Security Features](#additional-security-features)
8. [Backup and Disaster Recovery](#backup-and-disaster-recovery)
9. [Verification](#verification)

## Prerequisites

- Terraform installed
- AWS CLI installed and configured
- kubectl installed
- Access to the bastion host

## Service Mesh Deployment

The Service Mesh components are currently missing from the infrastructure. This section provides detailed instructions for deploying Istio service mesh and its components.

### Istio Installation

1. **Check the Service Mesh module configuration**

   Ensure the service_mesh module is properly configured in your Terraform files:

   ```hcl
   module "service_mesh" {
     source = "./modules/service_mesh"

     namespace        = "istio-system"
     create_namespace = true

     # Istio configuration
     istio_version = "1.16.1"

     # Resource configuration
     pilot_resources = {
       requests = {
         cpu    = "100m"
         memory = "128Mi"
       }
       limits = {
         cpu    = "500m"
         memory = "512Mi"
       }
     }

     # Security configuration
     enable_mtls = true
     mtls_mode   = "PERMISSIVE"

     # Gateway configuration
     create_gateway = true
     gateway_name   = "default-gateway"
     gateway_hosts  = ["*"]

     # Grafana dashboards
     create_grafana_dashboard = true
     grafana_namespace        = "monitoring"

     depends_on = [
       module.eks
     ]
   }
   ```

2. **Apply the Service Mesh module**

   ```bash
   terraform apply -target=module.service_mesh
   ```

3. **Verify the Istio installation**

   Connect to the bastion host and run:

   ```bash
   # Configure kubectl
   aws eks update-kubeconfig --name prod-architrave-eks --region eu-central-1

   # Check Istio namespace
   kubectl get namespace istio-system

   # Check Istio pods
   kubectl get pods -n istio-system
   ```

### Istio Gateways Configuration

1. **Configure Istio Gateway**

   Create a gateway configuration file:

   ```bash
   cat <<EOF > gateway.yaml
   apiVersion: networking.istio.io/v1alpha3
   kind: Gateway
   metadata:
     name: tenant-gateway
     namespace: istio-system
   spec:
     selector:
       istio: ingressgateway
     servers:
     - port:
         number: 80
         name: http
         protocol: HTTP
       hosts:
       - "*.example.com"
     - port:
         number: 443
         name: https
         protocol: HTTPS
       hosts:
       - "*.example.com"
       tls:
         mode: SIMPLE
         credentialName: example-com-cert
   EOF
   ```

2. **Apply the Gateway configuration**

   ```bash
   kubectl apply -f gateway.yaml
   ```

3. **Verify the Gateway configuration**

   ```bash
   kubectl get gateways -n istio-system
   kubectl describe gateway tenant-gateway -n istio-system
   ```

### mTLS Configuration

1. **Enable mTLS for the entire mesh**

   Create a PeerAuthentication resource:

   ```bash
   cat <<EOF > mtls.yaml
   apiVersion: security.istio.io/v1beta1
   kind: PeerAuthentication
   metadata:
     name: default
     namespace: istio-system
   spec:
     mtls:
       mode: STRICT
   EOF
   ```

2. **Apply the mTLS configuration**

   ```bash
   kubectl apply -f mtls.yaml
   ```

3. **Verify mTLS configuration**

   ```bash
   kubectl get peerauthentication -n istio-system
   istioctl x describe pod <any-pod-name> -n <any-namespace>
   ```

### Kiali Dashboard Deployment

1. **Install Kiali using Helm**

   ```bash
   helm repo add kiali https://kiali.org/helm-charts
   helm repo update
   helm install kiali kiali/kiali-server \
     --namespace istio-system \
     --set auth.strategy="anonymous" \
     --set deployment.ingress.enabled=true \
     --set deployment.ingress.hosts[0].host=kiali.example.com \
     --set deployment.ingress.hosts[0].paths[0].path="/"
   ```

2. **Verify Kiali installation**

   ```bash
   kubectl get pods -n istio-system | grep kiali
   kubectl get svc -n istio-system | grep kiali
   kubectl get ingress -n istio-system | grep kiali
   ```

3. **Access Kiali dashboard**

   Access the Kiali dashboard at `https://kiali.example.com` or port-forward to access locally:

   ```bash
   kubectl port-forward svc/kiali 20001:20001 -n istio-system
   ```

   Then access the dashboard at `http://localhost:20001`

## Advanced Monitoring Deployment

Advanced monitoring components are missing from the infrastructure. This section provides detailed instructions for deploying Prometheus, Grafana, Loki, Jaeger, and CloudWatch integration.

### Prometheus Installation

1. **Create a monitoring namespace**

   ```bash
   kubectl create namespace monitoring
   ```

2. **Install Prometheus using Helm**

   ```bash
   helm repo add prometheus-community https://prometheus-community.github.io/helm-charts
   helm repo update
   helm install prometheus prometheus-community/prometheus \
     --namespace monitoring \
     --set server.persistentVolume.size=20Gi \
     --set alertmanager.persistentVolume.size=5Gi \
     --set server.retention=15d
   ```

3. **Verify Prometheus installation**

   ```bash
   kubectl get pods -n monitoring | grep prometheus
   kubectl get svc -n monitoring | grep prometheus
   ```

4. **Access Prometheus dashboard**

   ```bash
   kubectl port-forward svc/prometheus-server 9090:80 -n monitoring
   ```

   Then access the dashboard at `http://localhost:9090`

### Grafana Deployment

1. **Check the Grafana module configuration**

   Ensure the Grafana module is properly configured:

   ```hcl
   module "grafana" {
     source = "./modules/grafana"

     eks_cluster_name    = module.eks.cluster_name
     domain_name         = var.domain_name
     acm_certificate_arn = var.acm_certificate_arn
     environment         = var.environment
     tags                = local.common_tags

     depends_on = [
       module.eks
     ]
   }
   ```

2. **Install Grafana using Helm**

   ```bash
   helm repo add grafana https://grafana.github.io/helm-charts
   helm repo update
   helm install grafana grafana/grafana \
     --namespace monitoring \
     --set persistence.enabled=true \
     --set persistence.size=10Gi \
     --set adminPassword=admin \
     --set service.type=ClusterIP \
     --set ingress.enabled=true \
     --set ingress.hosts[0]=grafana.example.com
   ```

3. **Configure Prometheus as a data source**

   ```bash
   kubectl apply -f - <<EOF
   apiVersion: v1
   kind: ConfigMap
   metadata:
     name: grafana-datasources
     namespace: monitoring
   data:
     prometheus.yaml: |-
       apiVersion: 1
       datasources:
       - name: Prometheus
         type: prometheus
         url: http://prometheus-server.monitoring.svc.cluster.local
         access: proxy
         isDefault: true
   EOF
   ```

4. **Verify Grafana installation**

   ```bash
   kubectl get pods -n monitoring | grep grafana
   kubectl get svc -n monitoring | grep grafana
   kubectl get ingress -n monitoring | grep grafana
   ```

5. **Access Grafana dashboard**

   Access the Grafana dashboard at `https://grafana.example.com` or port-forward to access locally:

   ```bash
   kubectl port-forward svc/grafana 3000:80 -n monitoring
   ```

   Then access the dashboard at `http://localhost:3000` (default credentials: admin/admin)

### Loki Installation

1. **Install Loki using Helm**

   ```bash
   helm repo add grafana https://grafana.github.io/helm-charts
   helm repo update
   helm install loki grafana/loki-stack \
     --namespace monitoring \
     --set grafana.enabled=false \
     --set prometheus.enabled=false \
     --set loki.persistence.enabled=true \
     --set loki.persistence.size=10Gi
   ```

2. **Configure Loki as a Grafana data source**

   ```bash
   kubectl apply -f - <<EOF
   apiVersion: v1
   kind: ConfigMap
   metadata:
     name: grafana-loki-datasource
     namespace: monitoring
     labels:
       grafana_datasource: "1"
   data:
     loki-datasource.yaml: |-
       apiVersion: 1
       datasources:
       - name: Loki
         type: loki
         url: http://loki.monitoring.svc.cluster.local:3100
         access: proxy
   EOF
   ```

3. **Verify Loki installation**

   ```bash
   kubectl get pods -n monitoring | grep loki
   kubectl get svc -n monitoring | grep loki
   ```

### Jaeger Deployment

1. **Create an observability namespace**

   ```bash
   kubectl create namespace observability
   ```

2. **Install Jaeger Operator using Helm**

   ```bash
   helm repo add jaegertracing https://jaegertracing.github.io/helm-charts
   helm repo update
   helm install jaeger-operator jaegertracing/jaeger-operator \
     --namespace observability \
     --set rbac.clusterRole=true
   ```

3. **Deploy Jaeger instance**

   ```bash
   kubectl apply -f - <<EOF
   apiVersion: jaegertracing.io/v1
   kind: Jaeger
   metadata:
     name: jaeger
     namespace: observability
   spec:
     strategy: production
     storage:
       type: elasticsearch
       options:
         es:
           server-urls: http://elasticsearch-master:9200
     ingress:
       enabled: true
       hosts:
         - jaeger.example.com
   EOF
   ```

4. **Verify Jaeger installation**

   ```bash
   kubectl get pods -n observability | grep jaeger
   kubectl get svc -n observability | grep jaeger
   ```

5. **Access Jaeger UI**

   Access the Jaeger UI at `https://jaeger.example.com` or port-forward to access locally:

   ```bash
   kubectl port-forward svc/jaeger-query 16686:16686 -n observability
   ```

   Then access the UI at `http://localhost:16686`

### CloudWatch Integration

1. **Create IAM policy for CloudWatch access**

   ```bash
   aws iam create-policy \
     --policy-name EKSCloudWatchPolicy \
     --policy-document '{
       "Version": "2012-10-17",
       "Statement": [
         {
           "Effect": "Allow",
           "Action": [
             "cloudwatch:PutMetricData",
             "logs:PutLogEvents",
             "logs:DescribeLogStreams",
             "logs:DescribeLogGroups",
             "logs:CreateLogStream",
             "logs:CreateLogGroup"
           ],
           "Resource": "*"
         }
       ]
     }'
   ```

2. **Create IAM role for CloudWatch**

   ```bash
   eksctl create iamserviceaccount \
     --name cloudwatch-agent \
     --namespace amazon-cloudwatch \
     --cluster prod-architrave-eks \
     --attach-policy-arn arn:aws:iam::YOUR_ACCOUNT_ID:policy/EKSCloudWatchPolicy \
     --approve \
     --region eu-central-1
   ```

3. **Install CloudWatch agent**

   ```bash
   kubectl create namespace amazon-cloudwatch

   kubectl apply -f https://raw.githubusercontent.com/aws-samples/amazon-cloudwatch-container-insights/latest/k8s-deployment-manifest-templates/deployment-mode/daemonset/container-insights-monitoring/cloudwatch-namespace.yaml

   kubectl apply -f https://raw.githubusercontent.com/aws-samples/amazon-cloudwatch-container-insights/latest/k8s-deployment-manifest-templates/deployment-mode/daemonset/container-insights-monitoring/cwagent/cwagent-serviceaccount.yaml

   kubectl apply -f https://raw.githubusercontent.com/aws-samples/amazon-cloudwatch-container-insights/latest/k8s-deployment-manifest-templates/deployment-mode/daemonset/container-insights-monitoring/cwagent/cwagent-configmap.yaml

   kubectl apply -f https://raw.githubusercontent.com/aws-samples/amazon-cloudwatch-container-insights/latest/k8s-deployment-manifest-templates/deployment-mode/daemonset/container-insights-monitoring/cwagent/cwagent-daemonset.yaml
   ```

4. **Install Prometheus adapter for CloudWatch**

   ```bash
   kubectl apply -f https://raw.githubusercontent.com/aws-samples/amazon-cloudwatch-container-insights/latest/k8s-deployment-manifest-templates/deployment-mode/service/cwagent-prometheus/prometheus-eks.yaml
   ```

5. **Verify CloudWatch integration**

   ```bash
   kubectl get pods -n amazon-cloudwatch
   ```

   Check the CloudWatch console for Container Insights metrics and logs.

## Advanced Autoscaling Deployment

Advanced autoscaling components are missing from the infrastructure. This section provides detailed instructions for deploying KEDA, VPA, HPA, PDBs, Goldilocks, and Karpenter.

### KEDA Installation

1. **Create an autoscaling namespace**

   ```bash
   kubectl create namespace autoscaling
   ```

2. **Install KEDA using Helm**

   ```bash
   helm repo add kedacore https://kedacore.github.io/charts
   helm repo update
   helm install keda kedacore/keda \
     --namespace autoscaling \
     --version 2.10.1
   ```

3. **Verify KEDA installation**

   ```bash
   kubectl get pods -n autoscaling
   kubectl get crd | grep keda
   ```

4. **Create a sample KEDA ScaledObject**

   ```bash
   kubectl apply -f - <<EOF
   apiVersion: keda.sh/v1alpha1
   kind: ScaledObject
   metadata:
     name: sample-scaledobject
     namespace: default
   spec:
     scaleTargetRef:
       name: sample-deployment
     minReplicaCount: 1
     maxReplicaCount: 10
     triggers:
     - type: cpu
       metadata:
         type: Utilization
         value: "70"
   EOF
   ```

### Vertical Pod Autoscaler (VPA)

1. **Install VPA using Helm**

   ```bash
   helm repo add fairwinds-stable https://charts.fairwinds.com/stable
   helm repo update
   helm install vpa fairwinds-stable/vpa \
     --namespace autoscaling \
     --set recommender.enabled=true \
     --set updater.enabled=true \
     --set admissionController.enabled=true
   ```

2. **Verify VPA installation**

   ```bash
   kubectl get pods -n autoscaling | grep vpa
   kubectl get crd | grep autoscaling.k8s.io
   ```

3. **Create a sample VPA resource**

   ```bash
   kubectl apply -f - <<EOF
   apiVersion: autoscaling.k8s.io/v1
   kind: VerticalPodAutoscaler
   metadata:
     name: sample-vpa
     namespace: default
   spec:
     targetRef:
       apiVersion: "apps/v1"
       kind: Deployment
       name: sample-deployment
     updatePolicy:
       updateMode: "Auto"
   EOF
   ```

### Horizontal Pod Autoscaler (HPA)

1. **Check the autoscaling module configuration**

   Ensure the autoscaling module is properly configured:

   ```hcl
   module "autoscaling" {
     source = "./modules/autoscaling"

     eks_cluster_name = module.eks.cluster_name
     environment      = var.environment

     # Configure autoscaling for deployments
     deployments = {
       "api" = {
         namespace     = "default"
         min_replicas  = 2
         max_replicas  = 5
         cpu_threshold = 70
       },
       "app" = {
         namespace     = "default"
         min_replicas  = 2
         max_replicas  = 5
         cpu_threshold = 70
       }
     }

     # Configure cluster autoscaler
     enable_cluster_autoscaler = true

     depends_on = [
       module.eks
     ]
   }
   ```

2. **Install Metrics Server**

   ```bash
   kubectl apply -f https://github.com/kubernetes-sigs/metrics-server/releases/latest/download/components.yaml
   ```

3. **Create a sample HPA resource**

   ```bash
   kubectl apply -f - <<EOF
   apiVersion: autoscaling/v2
   kind: HorizontalPodAutoscaler
   metadata:
     name: sample-hpa
     namespace: default
   spec:
     scaleTargetRef:
       apiVersion: apps/v1
       kind: Deployment
       name: sample-deployment
     minReplicas: 1
     maxReplicas: 10
     metrics:
     - type: Resource
       resource:
         name: cpu
         target:
           type: Utilization
           averageUtilization: 70
   EOF
   ```

4. **Verify HPA installation**

   ```bash
   kubectl get hpa --all-namespaces
   ```

### Pod Disruption Budgets (PDBs)

1. **Create sample PDBs for critical services**

   ```bash
   kubectl apply -f - <<EOF
   apiVersion: policy/v1
   kind: PodDisruptionBudget
   metadata:
     name: sample-pdb
     namespace: default
   spec:
     minAvailable: 1
     selector:
       matchLabels:
         app: sample-app
   EOF
   ```

2. **Verify PDB creation**

   ```bash
   kubectl get pdb --all-namespaces
   ```

### Goldilocks Installation

1. **Install Goldilocks using Helm**

   ```bash
   helm repo add fairwinds-stable https://charts.fairwinds.com/stable
   helm repo update
   helm install goldilocks fairwinds-stable/goldilocks \
     --namespace autoscaling \
     --set dashboard.enabled=true \
     --set dashboard.service.type=ClusterIP \
     --set dashboard.ingress.enabled=true \
     --set dashboard.ingress.hosts[0]=goldilocks.example.com
   ```

2. **Enable Goldilocks for a namespace**

   ```bash
   kubectl label namespace default goldilocks.fairwinds.com/enabled=true
   ```

3. **Verify Goldilocks installation**

   ```bash
   kubectl get pods -n autoscaling | grep goldilocks
   kubectl get vpa --all-namespaces
   ```

4. **Access Goldilocks dashboard**

   Access the Goldilocks dashboard at `https://goldilocks.example.com` or port-forward to access locally:

   ```bash
   kubectl port-forward svc/goldilocks-dashboard 8080:80 -n autoscaling
   ```

   Then access the dashboard at `http://localhost:8080`

### Karpenter Configuration

1. **Create IAM resources for Karpenter**

   ```bash
   export CLUSTER_NAME=prod-architrave-eks
   export AWS_ACCOUNT_ID=$(aws sts get-caller-identity --query Account --output text)
   export AWS_REGION=eu-central-1

   aws cloudformation deploy \
     --stack-name Karpenter-${CLUSTER_NAME} \
     --template-file karpenter-cfn.yaml \
     --capabilities CAPABILITY_NAMED_IAM \
     --parameter-overrides ClusterName=${CLUSTER_NAME}
   ```

2. **Install Karpenter using Helm**

   ```bash
   helm repo add karpenter https://charts.karpenter.sh
   helm repo update
   helm install karpenter karpenter/karpenter \
     --namespace karpenter \
     --create-namespace \
     --set serviceAccount.annotations."eks\.amazonaws\.com/role-arn"=arn:aws:iam::${AWS_ACCOUNT_ID}:role/KarpenterNodeRole-${CLUSTER_NAME} \
     --set clusterName=${CLUSTER_NAME} \
     --set clusterEndpoint=$(aws eks describe-cluster --name ${CLUSTER_NAME} --query "cluster.endpoint" --output text) \
     --set aws.defaultInstanceProfile=KarpenterNodeInstanceProfile-${CLUSTER_NAME}
   ```

3. **Create a Karpenter Provisioner**

   ```bash
   kubectl apply -f - <<EOF
   apiVersion: karpenter.sh/v1alpha5
   kind: Provisioner
   metadata:
     name: default
   spec:
     requirements:
       - key: karpenter.sh/capacity-type
         operator: In
         values: ["spot", "on-demand"]
     limits:
       resources:
         cpu: 100
         memory: 100Gi
     provider:
       subnetSelector:
         karpenter.sh/discovery: ${CLUSTER_NAME}
       securityGroupSelector:
         karpenter.sh/discovery: ${CLUSTER_NAME}
       tags:
         karpenter.sh/discovery: ${CLUSTER_NAME}
     ttlSecondsAfterEmpty: 30
   EOF
   ```

4. **Verify Karpenter installation**

   ```bash
   kubectl get pods -n karpenter
   kubectl get provisioners
   ```

## Tenant Functionality Implementation

Tenant functionality components are missing from the infrastructure. This section provides detailed instructions for implementing tenant resources, testing onboarding scripts, verifying isolation, configuring resource quotas, and implementing networking policies.

### Tenant Resource Creation

1. **Create tenant namespaces**

   ```bash
   kubectl create namespace tenant1
   kubectl create namespace tenant2
   ```

2. **Apply resource quotas to tenant namespaces**

   ```bash
   kubectl apply -f - <<EOF
   apiVersion: v1
   kind: ResourceQuota
   metadata:
     name: tenant-quota
     namespace: tenant1
   spec:
     hard:
       requests.cpu: "2"
       requests.memory: 4Gi
       limits.cpu: "4"
       limits.memory: 8Gi
       pods: "20"
       services: "10"
       persistentvolumeclaims: "5"
   EOF
   ```

3. **Create tenant service accounts**

   ```bash
   kubectl apply -f - <<EOF
   apiVersion: v1
   kind: ServiceAccount
   metadata:
     name: tenant-admin
     namespace: tenant1
   ---
   apiVersion: rbac.authorization.k8s.io/v1
   kind: RoleBinding
   metadata:
     name: tenant-admin-binding
     namespace: tenant1
   subjects:
   - kind: ServiceAccount
     name: tenant-admin
     namespace: tenant1
   roleRef:
     kind: ClusterRole
     name: admin
     apiGroup: rbac.authorization.k8s.io
   EOF
   ```

### Tenant Onboarding Script Testing

1. **Review the tenant onboarding script**

   ```bash
   cat ./scripts/tenant-onboarding.sh
   ```

2. **Test the tenant onboarding script with a sample tenant**

   ```bash
   ./scripts/tenant-onboarding.sh \
     --tenant-name test-tenant \
     --subdomain test \
     --environment production \
     --dms true \
     --logo https://example.com/logo.png \
     --delphi true \
     --external-api true \
     --document-class-set standard \
     --language en \
     --reference-data true \
     --heap-tracking false
   ```

3. **Verify the tenant resources were created**

   ```bash
   kubectl get all -n test-tenant
   ```

### Tenant Isolation Verification

1. **Apply network policies for tenant isolation**

   ```bash
   kubectl apply -f - <<EOF
   apiVersion: networking.k8s.io/v1
   kind: NetworkPolicy
   metadata:
     name: deny-all
     namespace: tenant1
   spec:
     podSelector: {}
     policyTypes:
     - Ingress
     - Egress
   ---
   apiVersion: networking.k8s.io/v1
   kind: NetworkPolicy
   metadata:
     name: allow-same-namespace
     namespace: tenant1
   spec:
     podSelector: {}
     policyTypes:
     - Ingress
     ingress:
     - from:
       - podSelector: {}
   ---
   apiVersion: networking.k8s.io/v1
   kind: NetworkPolicy
   metadata:
     name: allow-egress-dns
     namespace: tenant1
   spec:
     podSelector: {}
     policyTypes:
     - Egress
     egress:
     - to:
       - namespaceSelector:
           matchLabels:
             kubernetes.io/metadata.name: kube-system
         podSelector:
           matchLabels:
             k8s-app: kube-dns
       ports:
       - protocol: UDP
         port: 53
       - protocol: TCP
         port: 53
   EOF
   ```

2. **Test tenant isolation**

   ```bash
   # Create test pods in different namespaces
   kubectl run test-pod-1 -n tenant1 --image=nginx
   kubectl run test-pod-2 -n tenant2 --image=nginx

   # Try to access pod in tenant2 from tenant1
   kubectl exec -it test-pod-1 -n tenant1 -- curl -s --connect-timeout 5 test-pod-2.tenant2.svc.cluster.local
   # This should fail due to network policies
   ```

### Tenant Resource Quotas

1. **Apply LimitRange to tenant namespaces**

   ```bash
   kubectl apply -f - <<EOF
   apiVersion: v1
   kind: LimitRange
   metadata:
     name: tenant-limits
     namespace: tenant1
   spec:
     limits:
     - default:
         memory: 512Mi
         cpu: 500m
       defaultRequest:
         memory: 256Mi
         cpu: 250m
       type: Container
   EOF
   ```

2. **Verify LimitRange is applied**

   ```bash
   kubectl get limitrange -n tenant1
   ```

### Tenant Networking Policies

1. **Apply Istio AuthorizationPolicy for tenant isolation**

   ```bash
   kubectl apply -f - <<EOF
   apiVersion: security.istio.io/v1beta1
   kind: AuthorizationPolicy
   metadata:
     name: tenant-isolation
     namespace: tenant1
   spec:
     selector:
       matchLabels:
         app: tenant-app
     rules:
     - from:
       - source:
           namespaces: ["tenant1"]
   EOF
   ```

2. **Apply Istio ServiceEntry for external access**

   ```bash
   kubectl apply -f - <<EOF
   apiVersion: networking.istio.io/v1alpha3
   kind: ServiceEntry
   metadata:
     name: external-apis
     namespace: tenant1
   spec:
     hosts:
     - "api.example.com"
     ports:
     - number: 443
       name: https
       protocol: HTTPS
     resolution: DNS
     location: MESH_EXTERNAL
   EOF
   ```

## CI/CD Pipeline Configuration

The GitLab CI pipeline is not fully configured. This section provides instructions for configuring the pipeline.

1. **Review the current GitLab CI configuration**

   ```bash
   cat .gitlab-ci.yml
   ```

2. **Update the GitLab CI configuration**

   Create a new file with the following content:

   ```bash
   cat > .gitlab-ci.yml <<EOF
   stages:
     - validate
     - plan
     - apply
     - verify

   variables:
     TF_VAR_skip_k8s_connection: "true"
     TF_VAR_skip_kubernetes_resources: "true"
     TF_VAR_check_if_cluster_exists: "true"

   terraform:validate:
     stage: validate
     image: hashicorp/terraform:1.11.2
     script:
       - terraform init -backend=false
       - terraform validate

   terraform:plan:
     stage: plan
     image: hashicorp/terraform:1.11.2
     script:
       - terraform init
       - terraform plan -out=tfplan
     artifacts:
       paths:
         - tfplan

   terraform:apply:
     stage: apply
     image: hashicorp/terraform:1.11.2
     script:
       - terraform init
       - terraform apply -auto-approve tfplan
     dependencies:
       - terraform:plan
     when: manual

   verify:resources:
     stage: verify
     image: alpine:latest
     script:
       - apk add --no-cache aws-cli jq
       - ./scripts/verify-all-resources.sh
     dependencies:
       - terraform:apply
     when: manual
   EOF
   ```

3. **Commit and push the changes**

   ```bash
   git add .gitlab-ci.yml
   git commit -m "Update GitLab CI configuration"
   git push
   ```

## Additional Security Features

Several security features are missing from the infrastructure. This section provides instructions for implementing these features.

1. **Enable AWS Config**

   ```bash
   aws configservice put-configuration-recorder \
     --configuration-recorder name=default,roleARN=arn:aws:iam::$(aws sts get-caller-identity --query Account --output text):role/aws-service-role/config.amazonaws.com/AWSServiceRoleForConfig \
     --recording-group allSupported=true,includeGlobalResources=true

   aws configservice put-delivery-channel \
     --delivery-channel name=default,s3BucketName=config-bucket-$(aws sts get-caller-identity --query Account --output text),configSnapshotDeliveryProperties={deliveryFrequency=One_Hour}

   aws configservice start-configuration-recorder --configuration-recorder-name default
   ```

2. **Configure CloudTrail for comprehensive audit logging**

   ```bash
   aws cloudtrail create-trail \
     --name comprehensive-trail \
     --s3-bucket-name cloudtrail-bucket-$(aws sts get-caller-identity --query Account --output text) \
     --is-multi-region-trail \
     --enable-log-file-validation

   aws cloudtrail start-logging --name comprehensive-trail
   ```

3. **Implement network policies for pod-to-pod traffic control**

   ```bash
   kubectl apply -f - <<EOF
   apiVersion: networking.k8s.io/v1
   kind: NetworkPolicy
   metadata:
     name: default-deny-all
     namespace: default
   spec:
     podSelector: {}
     policyTypes:
     - Ingress
     - Egress
   EOF
   ```

4. **Implement secret management solution**

   ```bash
   # Install AWS Secrets Manager CSI driver
   helm repo add secrets-store-csi-driver https://kubernetes-sigs.github.io/secrets-store-csi-driver/charts
   helm install csi-secrets-store secrets-store-csi-driver/secrets-store-csi-driver \
     --namespace kube-system \
     --set syncSecret.enabled=true

   # Install AWS provider
   kubectl apply -f https://raw.githubusercontent.com/aws/secrets-store-csi-driver-provider-aws/main/deployment/aws-provider-installer.yaml
   ```

## Backup and Disaster Recovery

Backup and disaster recovery features are missing from the infrastructure. This section provides instructions for implementing these features.

1. **Configure automated backups**

   ```bash
   # Install Velero for Kubernetes backup
   helm repo add vmware-tanzu https://vmware-tanzu.github.io/helm-charts
   helm install velero vmware-tanzu/velero \
     --namespace velero \
     --create-namespace \
     --set-file credentials.secretContents.cloud=./credentials-velero \
     --set configuration.provider=aws \
     --set configuration.backupStorageLocation.bucket=velero-backup-$(aws sts get-caller-identity --query Account --output text) \
     --set configuration.backupStorageLocation.config.region=eu-central-1 \
     --set snapshotsEnabled=true \
     --set deployRestic=true
   ```

2. **Configure cross-region replication**

   ```bash
   # Enable cross-region replication for S3 buckets
   aws s3api put-bucket-replication \
     --bucket primary-bucket-$(aws sts get-caller-identity --query Account --output text) \
     --replication-configuration file://replication-config.json
   ```

3. **Document disaster recovery procedures**

   Create a disaster recovery plan document with detailed procedures for different failure scenarios.

## Verification

After deploying all missing components, run the verification script again to ensure everything is properly deployed:

```bash
./scripts/verify-all-resources.sh
```

Connect to the bastion host and verify Kubernetes resources:

```bash
./scripts/connect-to-bastion.sh
```

Follow the instructions provided by the script to connect to the bastion host and run kubectl commands to verify all Kubernetes resources.

### Important Note

When implementing these components, ensure that there is no deletion protection enabled since we will do multiple create and delete operations during testing. Also, make sure that when we onboard a new tenant, it covers all the advanced features including networking, HPA, autoscaling, and other features automatically.
