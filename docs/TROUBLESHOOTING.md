# Troubleshooting Guide

## Common Issues and Solutions

### Backup Issues

#### 1. Backup Creation Fails
**Symptoms:**
- Backup status shows "Failed"
- Error messages in Velero logs
- No backup files in S3 bucket

**Troubleshooting Steps:**
```bash
# 1. Check Velero pod status
kubectl get pods -n velero

# 2. Check Velero logs
kubectl logs -n velero -l app.kubernetes.io/name=velero

# 3. Verify AWS credentials
kubectl get secret -n velero aws-credentials -o yaml

# 4. Check S3 bucket access
aws s3 ls s3://architrave-backups

# 5. Verify backup configuration
kubectl get backup -n velero -o yaml
```

**Solutions:**
1. **AWS Credentials Issue:**
   ```bash
   # Update AWS credentials
   kubectl create secret generic aws-credentials \
     --from-file=cloud=credentials-velero \
     -n velero
   ```

2. **S3 Bucket Access Issue:**
   ```bash
   # Verify bucket policy
   aws s3api get-bucket-policy --bucket architrave-backups
   
   # Update bucket policy if needed
   aws s3api put-bucket-policy --bucket architrave-backups --policy file://bucket-policy.json
   ```

3. **Resource Issues:**
   ```bash
   # Check resource limits
   kubectl describe pod -n velero -l app.kubernetes.io/name=velero
   
   # Update resource limits if needed
   kubectl edit deployment -n velero velero
   ```

#### 2. Backup Restoration Fails
**Symptoms:**
- Restore status shows "Failed"
- Missing resources after restore
- Inconsistent data

**Troubleshooting Steps:**
```bash
# 1. Check restore status
velero restore describe <restore-name>

# 2. Check restore logs
velero restore logs <restore-name>

# 3. Verify backup integrity
velero backup verify <backup-name>

# 4. Check resource conflicts
kubectl get all --all-namespaces
```

**Solutions:**
1. **Resource Conflict:**
   ```bash
   # Delete conflicting resources
   kubectl delete <resource-type> <resource-name> -n <namespace>
   
   # Retry restore
   velero restore create --from-backup <backup-name>
   ```

2. **Storage Class Issue:**
   ```bash
   # Check storage classes
   kubectl get storageclass
   
   # Update storage class if needed
   kubectl patch pv <pv-name> -p '{"spec":{"storageClassName":"<new-storage-class>"}}'
   ```

3. **Namespace Issue:**
   ```bash
   # Create namespace if missing
   kubectl create namespace <namespace>
   
   # Retry restore
   velero restore create --from-backup <backup-name>
   ```

### Performance Testing Issues

#### 1. Test Execution Fails
**Symptoms:**
- Test pod in Error state
- No test results
- High resource usage

**Troubleshooting Steps:**
```bash
# 1. Check test pod status
kubectl get pods -n testing -l app=k6

# 2. Check test logs
kubectl logs -n testing -l app=k6

# 3. Check resource usage
kubectl top pods -n testing

# 4. Verify test configuration
kubectl get configmap -n testing k6-test-script -o yaml
```

**Solutions:**
1. **Resource Limits:**
   ```bash
   # Update resource limits
   kubectl edit deployment -n testing k6
   
   # Scale down test
   kubectl scale deployment -n testing k6 --replicas=1
   ```

2. **Test Script Issues:**
   ```bash
   # Update test script
   kubectl edit configmap -n testing k6-test-script
   
   # Restart test
   kubectl rollout restart deployment -n testing k6
   ```

3. **Network Issues:**
   ```bash
   # Check network policies
   kubectl get networkpolicy -n testing
   
   # Update network policy if needed
   kubectl apply -f testing/network-policy.yaml
   ```

#### 2. Test Results Unavailable
**Symptoms:**
- No test results in logs
- Missing metrics
- Incomplete test execution

**Troubleshooting Steps:**
```bash
# 1. Check test pod status
kubectl get pods -n testing -l app=k6

# 2. Check test logs
kubectl logs -n testing -l app=k6

# 3. Verify metrics collection
kubectl get servicemonitor -n monitoring

# 4. Check Prometheus status
kubectl get pods -n monitoring -l app=prometheus
```

**Solutions:**
1. **Metrics Collection:**
   ```bash
   # Update ServiceMonitor
   kubectl apply -f monitoring/performance-metrics.yaml
   
   # Restart Prometheus
   kubectl rollout restart deployment -n monitoring prometheus-server
   ```

2. **Test Configuration:**
   ```bash
   # Update test configuration
   kubectl edit configmap -n testing k6-test-script
   
   # Restart test
   kubectl rollout restart deployment -n testing k6
   ```

3. **Storage Issues:**
   ```bash
   # Check PVC status
   kubectl get pvc -n testing
   
   # Update PVC if needed
   kubectl edit pvc -n testing k6-results
   ```

### Monitoring Issues

#### 1. Prometheus Issues
**Symptoms:**
- Prometheus pod not running
- Missing metrics
- High resource usage

**Troubleshooting Steps:**
```bash
# 1. Check Prometheus status
kubectl get pods -n monitoring -l app=prometheus

# 2. Check Prometheus logs
kubectl logs -n monitoring -l app=prometheus

# 3. Check resource usage
kubectl top pods -n monitoring

# 4. Verify configuration
kubectl get prometheus -n monitoring -o yaml
```

**Solutions:**
1. **Resource Issues:**
   ```bash
   # Update resource limits
   kubectl edit deployment -n monitoring prometheus-server
   
   # Scale down if needed
   kubectl scale deployment -n monitoring prometheus-server --replicas=1
   ```

2. **Storage Issues:**
   ```bash
   # Check PVC status
   kubectl get pvc -n monitoring
   
   # Update PVC if needed
   kubectl edit pvc -n monitoring prometheus-storage
   ```

3. **Configuration Issues:**
   ```bash
   # Update configuration
   kubectl apply -f monitoring/prometheus-config.yaml
   
   # Restart Prometheus
   kubectl rollout restart deployment -n monitoring prometheus-server
   ```

#### 2. Grafana Issues
**Symptoms:**
- Grafana pod not running
- Dashboard not loading
- Missing data sources

**Troubleshooting Steps:**
```bash
# 1. Check Grafana status
kubectl get pods -n monitoring -l app=grafana

# 2. Check Grafana logs
kubectl logs -n monitoring -l app=grafana

# 3. Verify configuration
kubectl get configmap -n monitoring grafana-datasources -o yaml
```

**Solutions:**
1. **Data Source Issues:**
   ```bash
   # Update data sources
   kubectl apply -f monitoring/grafana-datasources.yaml
   
   # Restart Grafana
   kubectl rollout restart deployment -n monitoring grafana
   ```

2. **Dashboard Issues:**
   ```bash
   # Update dashboards
   kubectl apply -f monitoring/grafana-dashboards.yaml
   
   # Restart Grafana
   kubectl rollout restart deployment -n monitoring grafana
   ```

3. **Authentication Issues:**
   ```bash
   # Update admin credentials
   kubectl create secret generic grafana-admin \
     --from-literal=admin-user=<username> \
     --from-literal=admin-password=<password> \
     -n monitoring
   
   # Restart Grafana
   kubectl rollout restart deployment -n monitoring grafana
   ```

## Advanced Troubleshooting

### 1. Network Issues
**Symptoms:**
- Pods can't communicate
- Services not accessible
- DNS resolution fails

**Troubleshooting Steps:**
```bash
# 1. Check network policies
kubectl get networkpolicy --all-namespaces

# 2. Check service endpoints
kubectl get endpoints --all-namespaces

# 3. Check DNS resolution
kubectl run -it --rm debug --image=busybox -- nslookup kubernetes.default

# 4. Check pod network
kubectl run -it --rm debug --image=busybox -- ping <service-ip>
```

**Solutions:**
1. **Network Policy:**
   ```bash
   # Update network policy
   kubectl apply -f security/network-policies.yaml
   ```

2. **Service Issues:**
   ```bash
   # Update service
   kubectl apply -f <service-manifest>
   ```

3. **DNS Issues:**
   ```bash
   # Check CoreDNS
   kubectl get pods -n kube-system -l k8s-app=kube-dns
   
   # Restart CoreDNS
   kubectl rollout restart deployment -n kube-system coredns
   ```

### 2. Storage Issues
**Symptoms:**
- PVC not bound
- Pod can't mount volume
- Storage class not found

**Troubleshooting Steps:**
```bash
# 1. Check PVC status
kubectl get pvc --all-namespaces

# 2. Check storage classes
kubectl get storageclass

# 3. Check volume attachments
kubectl get volumeattachment

# 4. Check AWS EBS volumes
aws ec2 describe-volumes
```

**Solutions:**
1. **Storage Class:**
   ```bash
   # Create storage class
   kubectl apply -f storage/storage-class.yaml
   ```

2. **PVC Issues:**
   ```bash
   # Delete and recreate PVC
   kubectl delete pvc <pvc-name> -n <namespace>
   kubectl apply -f <pvc-manifest>
   ```

3. **Volume Issues:**
   ```bash
   # Check volume status
   aws ec2 describe-volumes --volume-ids <volume-id>
   
   # Detach and reattach volume
   aws ec2 detach-volume --volume-id <volume-id>
   aws ec2 attach-volume --volume-id <volume-id> --instance-id <instance-id> --device /dev/sdf
   ```

### 3. Security Issues
**Symptoms:**
- Pod security violations
- Network policy violations
- Authentication failures

**Troubleshooting Steps:**
```bash
# 1. Check pod security
kubectl get pods --all-namespaces -o yaml | grep securityContext

# 2. Check network policies
kubectl get networkpolicy --all-namespaces

# 3. Check RBAC
kubectl get clusterrole,clusterrolebinding,role,rolebinding --all-namespaces

# 4. Check audit logs
kubectl logs -n kube-system -l component=kube-apiserver
```

**Solutions:**
1. **Pod Security:**
   ```bash
   # Update pod security context
   kubectl edit deployment <deployment-name> -n <namespace>
   ```

2. **Network Policy:**
   ```bash
   # Update network policy
   kubectl apply -f security/network-policies.yaml
   ```

3. **RBAC Issues:**
   ```bash
   # Update RBAC
   kubectl apply -f security/rbac.yaml
   ```

## Recovery Procedures

### 1. Cluster Recovery
**Steps:**
```bash
# 1. Check cluster status
kubectl get nodes
kubectl get pods --all-namespaces

# 2. Check system pods
kubectl get pods -n kube-system

# 3. Check cluster events
kubectl get events --all-namespaces

# 4. Check cluster logs
kubectl logs -n kube-system -l component=kube-apiserver
```

**Recovery Actions:**
1. **Node Issues:**
   ```bash
   # Drain node
   kubectl drain <node-name> --ignore-daemonsets
   
   # Cordon node
   kubectl cordon <node-name>
   ```

2. **System Pod Issues:**
   ```bash
   # Restart system pods
   kubectl rollout restart deployment -n kube-system <deployment-name>
   ```

3. **Cluster Issues:**
   ```bash
   # Reset cluster
   kubeadm reset
   kubeadm init
   ```

### 2. Data Recovery
**Steps:**
```bash
# 1. Check backup status
velero backup get

# 2. Check restore status
velero restore get

# 3. Check PVC status
kubectl get pvc --all-namespaces

# 4. Check AWS EBS volumes
aws ec2 describe-volumes
```

**Recovery Actions:**
1. **Backup Recovery:**
   ```bash
   # Create restore
   velero restore create --from-backup <backup-name>
   ```

2. **PVC Recovery:**
   ```bash
   # Recreate PVC
   kubectl apply -f <pvc-manifest>
   ```

3. **Volume Recovery:**
   ```bash
   # Restore EBS volume
   aws ec2 restore-volume --volume-id <volume-id>
   ```

### 3. Application Recovery
**Steps:**
```bash
# 1. Check application status
kubectl get pods -n <namespace>

# 2. Check application logs
kubectl logs -n <namespace> -l app=<app-label>

# 3. Check application events
kubectl get events -n <namespace>

# 4. Check application configuration
kubectl get configmap,secret -n <namespace>
```

**Recovery Actions:**
1. **Pod Issues:**
   ```bash
   # Restart deployment
   kubectl rollout restart deployment -n <namespace> <deployment-name>
   ```

2. **Configuration Issues:**
   ```bash
   # Update configuration
   kubectl apply -f <config-manifest>
   ```

3. **Resource Issues:**
   ```bash
   # Update resource limits
   kubectl edit deployment -n <namespace> <deployment-name>
   ```

## Prevention and Best Practices

### 1. Backup Best Practices
- Regular backup verification
- Multiple backup locations
- Automated backup testing
- Regular restore testing

### 2. Performance Testing Best Practices
- Regular test execution
- Resource monitoring
- Test data management
- Performance baselines

### 3. Monitoring Best Practices
- Proactive monitoring
- Alert tuning
- Dashboard optimization
- Metrics management

### 4. Security Best Practices
- Regular security scans
- Access control reviews
- Network policy audits
- Security updates

### 5. Maintenance Best Practices
- Regular maintenance windows
- Change management
- Documentation updates
- Training and knowledge sharing 