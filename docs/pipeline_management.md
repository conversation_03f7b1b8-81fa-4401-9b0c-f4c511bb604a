# GitLab CI Pipeline Management

This document consolidates information about GitLab CI pipeline management, fixes, and best practices.

## Pipeline Issues and Fixes

### Security Scanning Tools Fixes

1. **Checkov**: Fixed issues with unrecognized arguments `sh`
2. **Infracost**: Fixed error related to `INFRACOST_NO_COLOR` environment variable
3. **OPA/Conftest**: Fixed "unknown command 'sh'" error
4. **TFLint**: Fixed "Command line arguments support was dropped in v0.47" error
5. **TFSec**: Fixed "unknown shorthand flag: 'c' in -c" error

### Kubernetes Connectivity Fixes

1. **Inconsistent Dependency Lock File**:
   - Added detection for dependency lock file inconsistency in error logs
   - Added automatic reconfiguration with `terraform init -reconfigure`
   - Ensured proper inclusion of `.terraform.lock.hcl` in artifacts and cache

2. **Stale Plan Error**:
   - Enhanced error handling to detect stale plan errors
   - Added automatic creation of a new plan when existing plan is stale
   - Applied new plan automatically to continue the pipeline

3. **Kubernetes Connectivity Issues**:
   - Updated `apply-kubernetes` job to use `-reconfigure` during `terraform init`
   - Added error handling for failed Terraform apply operations
   - Added automatic creation of a new plan with explicit `skip_k8s_connection=false` parameter
   - Improved bastion host connection logic for Kubernetes operations

### AWS Resource Fixes

1. **Security Hub Standards Subscription Issues**:
   - Added conditional creation of Security Hub standards subscriptions
   - Updated ARNs to use the correct format

2. **CloudTrail S3 Bucket Policy Issue**:
   - Added public access block for the audit logs bucket
   - Updated S3 bucket policy to include correct resource ARNs
   - Added dependency on public access block for proper creation order

3. **Resource Already Exists Errors**:
   - Added steps to remove existing resources from Terraform state
   - Updated script to handle existing resources gracefully

## Automated Pipeline Fixes

The `auto_fix_pipeline_issues.sh` script automatically fixes common issues in the GitLab CI/CD pipeline:

1. **Kubernetes Connection Issues**:
   - Ensures `skip_k8s_connection` is set to `true` in terraform.tfvars
   - Prevents Terraform from trying to connect to Kubernetes cluster during infrastructure deployment

2. **Existing Resources Issues**:
   - Removes existing resources from Terraform state
   - Includes CloudWatch log groups, IAM roles, and Lambda functions

3. **Security Hub Standards Subscription Issues**:
   - Removes conflicting Security Hub standards subscriptions from Terraform state
   - Prevents errors when trying to create standards that already exist in AWS

4. **AWS Config Delivery Channel Issues**:
   - Stops AWS Config Configuration Recorders
   - Removes AWS Config resources from Terraform state

5. **EC2 Key Pair Duplicate Issues**:
   - Checks if bastion key pair already exists
   - Removes it from Terraform state if it does

6. **CloudTrail S3 Bucket Policy Issues**:
   - Fixes CloudTrail S3 bucket policy issues

7. **CI/CD Compatibility Issues**:
   - Updates modules for better CI/CD compatibility

## Best Practices for GitLab CI

1. **Avoid Complex Shell Scripts**: Use direct commands when possible to reduce complexity.
2. **Use `|| true` for Non-Critical Commands**: Prevents pipeline from failing when a command returns a non-zero exit code.
3. **Proper Error Handling**: Capture and log errors properly.
4. **Artifact Management**: Ensure artifacts are properly defined and collected.
5. **Consistent Environment Variables**: Ensure environment variables are properly set and consistent across jobs.
6. **Always use `skip_k8s_connection` in CI/CD environments**: Set `skip_k8s_connection = true` in terraform.tfvars to prevent Terraform from trying to connect to the Kubernetes cluster during infrastructure deployment.
7. **Handle existing resources properly**:
   - Use `count` or `for_each` with conditional creation
   - Import existing resources into the Terraform state
   - Use `depends_on` to ensure proper creation order
8. **Use proper ARNs for AWS resources**:
   - Use the correct region and account ID in ARNs
   - Use data sources to get the current region and account ID
9. **Handle S3 bucket policies properly**:
   - Add public access blocks for S3 buckets
   - Use the correct resource ARNs in bucket policies
   - Add dependencies to ensure proper creation order

## Testing the Pipeline

To test the pipeline locally before pushing to GitLab:

```bash
# Install GitLab Runner locally
brew install gitlab-runner

# Run a specific job locally
gitlab-runner exec docker tfsec_scan

# Run multiple jobs
gitlab-runner exec docker validate plan tfsec_scan
```

## Troubleshooting

If you encounter issues with the pipeline:

1. Check the job logs for specific error messages
2. Verify that all required tools are installed in the Docker images
3. Ensure that all environment variables are properly set
4. Check for syntax errors in the YAML files
5. Verify that all paths and file references are correct

## How to Use the Auto-Fix Script

The script is automatically run during the `apply-infrastructure` stage of the GitLab CI/CD pipeline. You don't need to run it manually.

If you want to run it manually, you can do so with:

```bash
./auto_fix_pipeline_issues.sh
```

## Maintenance

To update or extend the auto-fix script:

1. Edit `scripts/auto_fix_pipeline_issues.sh` to add new fixes
2. Test the changes locally before pushing to the repository
3. Update this documentation to reflect the new fixes
