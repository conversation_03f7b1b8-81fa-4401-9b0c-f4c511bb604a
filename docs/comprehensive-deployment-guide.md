# Comprehensive Infrastructure Deployment Guide

This document provides a complete guide for deploying, verifying, destroying, and redeploying the entire infrastructure.

## Table of Contents

1. [Infrastructure Overview](#infrastructure-overview)
2. [Prerequisites](#prerequisites)
3. [Initial Deployment](#initial-deployment)
4. [Kubernetes Components Deployment](#kubernetes-components-deployment)
5. [Application Deployment](#application-deployment)
6. [Monitoring and Alerting Setup](#monitoring-and-alerting-setup)
7. [Backup and Disaster Recovery Setup](#backup-and-disaster-recovery-setup)
8. [CI/CD Pipeline Setup](#cicd-pipeline-setup)
9. [Verification Steps](#verification-steps)
10. [Infrastructure Destruction](#infrastructure-destruction)
11. [Infrastructure Redeployment](#infrastructure-redeployment)
12. [Troubleshooting](#troubleshooting)
13. [Maintenance](#maintenance)

## Infrastructure Overview

Our infrastructure consists of the following components:

### AWS Resources
- **EKS Cluster**: Managed Kubernetes cluster
- **RDS Instances**: MySQL databases
- **VPC and Networking**: VPC, subnets, security groups, etc.
- **EC2 Instances**: Bastion host
- **S3 Buckets**: Storage for logs, backups, etc.
- **IAM Roles and Policies**: For access control

### Kubernetes Resources
- **Monitoring Stack**: Prometheus, Grafana, Loki
- **Service Mesh**: Istio, Kiali, Jaeger
- **Autoscaling**: Cluster Autoscaler
- **Sample Application**: Demo application

## Prerequisites

Before starting the deployment, ensure you have the following:

1. **AWS CLI** installed and configured with appropriate credentials
2. **Terraform** (version 1.5.0 or later)
3. **kubectl** installed
4. **Helm** (version 3.x or later)
5. **Git** installed
6. **GitLab CLI** (optional, for CI/CD setup)

## Initial Deployment

### 1. Clone the Repository

```bash
git clone https://github.com/your-organization/infra-provisioning.git
cd infra-provisioning
```

### 2. Initialize Terraform

```bash
terraform init
```

### 3. Create terraform.tfvars File

Create a file named `terraform.tfvars` with the following content:

```hcl
region = "eu-central-1"
environment = "production"
vpc_cidr = "10.0.0.0/16"
public_subnets = ["********/24", "********/24"]
private_subnets = ["********/24", "********/24"]
eks_cluster_name = "prod-architrave-eks"
eks_node_group_instance_type = "t3.medium"
eks_node_group_desired_capacity = 2
eks_node_group_min_size = 1
eks_node_group_max_size = 5
rds_instance_class = "db.t3.medium"
rds_allocated_storage = 20
rds_engine_version = "8.0.41"
rds_multi_az = true
bastion_instance_type = "t3.small"
check_if_cluster_exists = true
skip_k8s_connection = true
skip_kubernetes_resources = true
```

### 4. Plan the Deployment

```bash
terraform plan -out=tfplan
```

Review the plan to ensure it will create the expected resources.

### 5. Apply the Plan

```bash
terraform apply tfplan
```

This will create the following AWS resources:
- VPC with public and private subnets
- EKS cluster with node group
- RDS instances
- Bastion host
- S3 buckets
- IAM roles and policies

## Kubernetes Components Deployment

### 1. Configure kubectl

```bash
aws eks update-kubeconfig --name prod-architrave-eks --region eu-central-1
```

### 2. Create Namespaces

```bash
kubectl create namespace monitoring
kubectl create namespace observability
kubectl create namespace istio-system
kubectl create namespace autoscaling
kubectl create namespace sample-app
```

### 3. Deploy Prometheus and Grafana

```bash
helm repo add prometheus-community https://prometheus-community.github.io/helm-charts
helm repo update
helm install prometheus prometheus-community/kube-prometheus-stack --namespace monitoring
```

### 4. Deploy Loki

```bash
helm repo add grafana https://grafana.github.io/helm-charts
helm repo update
helm install loki grafana/loki-stack --namespace monitoring --set grafana.enabled=false
```

### 5. Deploy Jaeger

```bash
helm repo add jaegertracing https://jaegertracing.github.io/helm-charts
helm repo update
helm install jaeger jaegertracing/jaeger --namespace observability --set cassandra.config.max_heap_size=1G --set cassandra.config.heap_new_size=256M
```

### 6. Deploy Istio

```bash
helm repo add istio https://istio-release.storage.googleapis.com/charts
helm repo update
helm install istio-base istio/base --namespace istio-system
helm install istiod istio/istiod --namespace istio-system
helm install istio-ingressgateway istio/gateway --namespace istio-system
```

### 7. Deploy Kiali

```bash
helm repo add kiali https://kiali.org/helm-charts
helm repo update
helm install kiali kiali/kiali-server --namespace istio-system --set auth.strategy=anonymous
```

### 8. Deploy Cluster Autoscaler

```bash
helm repo add autoscaler https://kubernetes.github.io/autoscaler
helm repo update
helm install cluster-autoscaler autoscaler/cluster-autoscaler --namespace autoscaling --set autoDiscovery.clusterName=prod-architrave-eks --set awsRegion=eu-central-1
```

### 9. Fix Cluster Autoscaler IAM Permissions

```bash
aws iam attach-role-policy --role-name prod-architrave-eks-node-group-role --policy-arn arn:aws:iam::aws:policy/AutoScalingFullAccess
```

## Application Deployment

### 1. Label the Namespace for Istio Injection

```bash
kubectl label namespace sample-app istio-injection=enabled
```

### 2. Deploy the Application

```bash
kubectl apply -f kubernetes/deployment.yaml
```

### 3. Apply Network Policies

```bash
kubectl apply -f kubernetes/network-policy.yaml
```

### 4. Apply RBAC Configuration

```bash
kubectl apply -f kubernetes/rbac.yaml
```

## Monitoring and Alerting Setup

### 1. Import Grafana Dashboards

```bash
./monitoring/import-dashboards.sh
```

### 2. Apply Prometheus Alerting Rules

```bash
./monitoring/apply-alerts.sh
```

### 3. Apply Loki Configuration

```bash
./monitoring/apply-loki-config.sh
```

## Backup and Disaster Recovery Setup

### 1. Apply Backup Configuration

```bash
AWS_ACCESS_KEY_ID=your-access-key AWS_SECRET_ACCESS_KEY=your-secret-key ./kubernetes/apply-backup.sh
```

### 2. Test Recovery Procedures

```bash
./kubernetes/test-recovery.sh
```

## CI/CD Pipeline Setup

### 1. Set up GitLab CI/CD Pipeline

```bash
./setup-cicd.sh
```

## Verification Steps

After deploying all components, verify the infrastructure by:

### 1. Check AWS Resources

#### Check EKS Cluster

```bash
aws eks describe-cluster --name prod-architrave-eks
```

Expected output should show the cluster is `ACTIVE`.

#### Check RDS Instances

```bash
aws rds describe-db-instances
```

Expected output should show the RDS instances are `available`.

#### Check EC2 Instances

```bash
aws ec2 describe-instances --filters "Name=tag:Name,Values=*bastion*"
```

Expected output should show the bastion host is `running`.

#### Check S3 Buckets

```bash
aws s3 ls
```

Expected output should show all the required S3 buckets.

### 2. Check Kubernetes Resources

#### Check Namespaces

```bash
kubectl get namespaces
```

Expected output should include `monitoring`, `observability`, `istio-system`, `autoscaling`, and `sample-app`.

#### Check Pods

```bash
kubectl get pods --all-namespaces
```

All pods should be in `Running` state.

#### Check Services

```bash
kubectl get svc --all-namespaces
```

All services should be properly configured.

#### Check Deployments

```bash
kubectl get deployments --all-namespaces
```

All deployments should be available.

### 3. Check Monitoring

#### Access Grafana

```bash
kubectl port-forward -n monitoring svc/prometheus-grafana 3000:80
```

Then access Grafana at http://localhost:3000 (default credentials: admin/prom-operator).

#### Access Prometheus

```bash
kubectl port-forward -n monitoring svc/prometheus-kube-prometheus-prometheus 9090:9090
```

Then access Prometheus at http://localhost:9090.

### 4. Check Service Mesh

#### Access Kiali

```bash
kubectl port-forward -n istio-system svc/kiali 20001:20001
```

Then access Kiali at http://localhost:20001.

#### Access Jaeger

```bash
kubectl port-forward -n observability svc/jaeger-query 16686:16686
```

Then access Jaeger at http://localhost:16686.

### 5. Check Sample Application

```bash
kubectl port-forward -n sample-app svc/sample-app 8080:80
```

Then access the application at http://localhost:8080.

## Infrastructure Destruction

To destroy the entire infrastructure, follow these steps:

### 1. Delete Kubernetes Resources First

```bash
kubectl delete namespace sample-app
kubectl delete namespace monitoring
kubectl delete namespace observability
kubectl delete namespace istio-system
kubectl delete namespace autoscaling
```

### 2. Destroy Terraform Resources

```bash
terraform destroy
```

When prompted, type `yes` to confirm the destruction.

## Infrastructure Redeployment

To redeploy the infrastructure after destroying it, follow these steps:

### 1. Initialize Terraform Again

```bash
terraform init
```

### 2. Plan the Deployment

```bash
terraform plan -out=tfplan
```

### 3. Apply the Plan

```bash
terraform apply tfplan
```

### 4. Redeploy Kubernetes Components

Follow the steps in the [Kubernetes Components Deployment](#kubernetes-components-deployment) section.

### 5. Redeploy Application

Follow the steps in the [Application Deployment](#application-deployment) section.

### 6. Redeploy Monitoring and Alerting

Follow the steps in the [Monitoring and Alerting Setup](#monitoring-and-alerting-setup) section.

### 7. Redeploy Backup and Disaster Recovery

Follow the steps in the [Backup and Disaster Recovery Setup](#backup-and-disaster-recovery-setup) section.

### 8. Verify the Redeployment

Follow the steps in the [Verification Steps](#verification-steps) section.

## Troubleshooting

### Common Issues and Solutions

#### 1. Terraform Apply Fails

**Issue**: Terraform apply fails with an error.

**Solution**:
- Check the error message for specific issues
- Ensure AWS credentials are properly configured
- Check if resources already exist
- Try running with `-refresh=false` flag

```bash
terraform apply -refresh=false
```

#### 2. EKS Cluster Creation Fails

**Issue**: EKS cluster creation fails.

**Solution**:
- Check IAM permissions
- Ensure VPC and subnets are properly configured
- Check if the cluster already exists

```bash
aws eks describe-cluster --name prod-architrave-eks
```

#### 3. Kubernetes Pods in CrashLoopBackOff

**Issue**: Pods are in CrashLoopBackOff state.

**Solution**:
- Check pod logs

```bash
kubectl logs -n <namespace> <pod-name>
```

- Check pod events

```bash
kubectl describe pod -n <namespace> <pod-name>
```

- Check resource limits and adjust if necessary

#### 4. Jaeger Cassandra Issues

**Issue**: Jaeger Cassandra pods are in CrashLoopBackOff state.

**Solution**:
- Increase memory limits

```bash
helm upgrade jaeger jaegertracing/jaeger --namespace observability --set cassandra.config.max_heap_size=1G --set cassandra.config.heap_new_size=256M
```

#### 5. Cluster Autoscaler Issues

**Issue**: Cluster Autoscaler pod is in CrashLoopBackOff state.

**Solution**:
- Check IAM permissions
- Update the Cluster Autoscaler configuration

```bash
helm upgrade cluster-autoscaler autoscaler/cluster-autoscaler --namespace autoscaling --set autoDiscovery.clusterName=prod-architrave-eks --set awsRegion=eu-central-1
```

## Maintenance

### Regular Maintenance Tasks

#### 1. Update Kubernetes Version

```bash
terraform apply -var="eks_kubernetes_version=1.33"
```

#### 2. Update Node Group

```bash
terraform apply -var="eks_node_group_instance_type=t3.large"
```

#### 3. Update RDS Instance

```bash
terraform apply -var="rds_instance_class=db.t3.large"
```

#### 4. Backup RDS

```bash
aws rds create-db-snapshot --db-instance-identifier production-architrave-db --db-snapshot-identifier production-architrave-db-snapshot-$(date +%Y%m%d)
```

#### 5. Update Helm Charts

```bash
helm repo update
helm upgrade prometheus prometheus-community/kube-prometheus-stack --namespace monitoring
helm upgrade loki grafana/loki-stack --namespace monitoring
helm upgrade jaeger jaegertracing/jaeger --namespace observability
helm upgrade istio-base istio/base --namespace istio-system
helm upgrade istiod istio/istiod --namespace istio-system
helm upgrade istio-ingressgateway istio/gateway --namespace istio-system
helm upgrade kiali kiali/kiali-server --namespace istio-system
helm upgrade cluster-autoscaler autoscaler/cluster-autoscaler --namespace autoscaling
```

### Security Updates

#### 1. Update Security Groups

```bash
terraform apply -var="bastion_allowed_cidr=your-new-ip/32"
```

#### 2. Rotate IAM Keys

```bash
aws iam create-access-key --user-name your-user
aws iam delete-access-key --user-name your-user --access-key-id your-old-key
```

#### 3. Update Kubernetes RBAC

```bash
kubectl apply -f kubernetes/rbac.yaml
```

## Conclusion

This comprehensive guide provides all the necessary steps to deploy, verify, destroy, and redeploy the entire infrastructure. By following these steps, you can ensure that your infrastructure is properly set up and maintained.

For any issues or questions, please contact the infrastructure team.
