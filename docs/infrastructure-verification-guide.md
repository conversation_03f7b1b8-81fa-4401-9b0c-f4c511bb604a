# Infrastructure Verification Guide

This guide provides detailed instructions for verifying all AWS infrastructure components created by Terraform and deploying any missing components.

## Table of Contents

1. [Overview](#overview)
2. [Prerequisites](#prerequisites)
3. [Verification Process](#verification-process)
   - [Core Infrastructure](#core-infrastructure)
   - [Security Tools](#security-tools)
   - [Monitoring Tools](#monitoring-tools)
   - [Kubernetes Resources](#kubernetes-resources)
4. [Deployment Process](#deployment-process)
   - [Missing Components](#missing-components)
   - [Deployment Steps](#deployment-steps)
5. [Troubleshooting](#troubleshooting)

## Overview

The infrastructure consists of the following main components:

- **Core Infrastructure**: VPC, EKS, RDS, EC2, S3, DynamoDB
- **Security Tools**: Security Hub, AWS Config, CloudTrail, GuardDuty, Inspector
- **Monitoring Tools**: CloudWatch, Prometheus, Grafana
- **Kubernetes Resources**: Service Mesh, Dashboards, Autoscaling

## Prerequisites

- AWS CLI installed and configured
- jq installed for JSON parsing
- SSH key for connecting to the bastion host
- kubectl installed (for Kubernetes verification)

## Verification Process

### Core Infrastructure

Run the verification script to check all AWS resources:

```bash
./scripts/verify-all-resources.sh
```

This script will check:

1. **VPC**: Verifies that the production VPC exists
2. **EKS Cluster**: Verifies that the EKS cluster is active
3. **RDS**: Verifies that the RDS instance is available
4. **EC2 Instances**: Verifies that the bastion host and EKS nodes are running
5. **S3 Buckets**: Verifies that the required S3 buckets exist
6. **DynamoDB Tables**: Verifies that the required DynamoDB tables exist

### Security Tools

The verification script also checks:

1. **Security Hub**: Verifies that Security Hub is enabled with the required standards
2. **AWS Config**: Verifies that AWS Config is enabled and recording resources
3. **CloudTrail**: Verifies that CloudTrail is enabled with multi-region trail
4. **GuardDuty**: Verifies that GuardDuty is enabled
5. **Amazon Inspector**: Verifies that Inspector is enabled and finding vulnerabilities

### Monitoring Tools

The verification script checks:

1. **CloudWatch Dashboards**: Verifies that CloudWatch dashboards exist
2. **Systems Manager**: Verifies that Systems Manager is managing instances

### Kubernetes Resources

To verify Kubernetes resources, you need to connect to the bastion host:

```bash
./scripts/connect-to-bastion.sh
```

Follow the instructions provided by the script to connect to the bastion host and run kubectl commands.

Once connected to the bastion host, run the following commands:

1. **Configure kubectl**:
   ```bash
   aws eks update-kubeconfig --name prod-architrave-eks --region eu-central-1
   ```

2. **Check namespaces**:
   ```bash
   kubectl get namespaces
   ```
   
   Expected namespaces:
   - monitoring
   - istio-system (for Service Mesh)
   - autoscaling
   - tenant-app

3. **Check monitoring tools**:
   ```bash
   kubectl get pods -n monitoring
   ```
   
   Expected pods:
   - prometheus-server
   - grafana
   - alertmanager

4. **Check Service Mesh**:
   ```bash
   kubectl get pods -n istio-system
   ```
   
   Expected pods:
   - istiod
   - istio-ingressgateway
   - kiali (if enabled)
   - jaeger (if enabled)

5. **Check Autoscaling**:
   ```bash
   kubectl get hpa --all-namespaces
   ```
   
   Expected HPAs:
   - Various HPAs for deployed applications

6. **Check Dashboards**:
   ```bash
   kubectl get ingress -n monitoring
   ```
   
   Expected ingresses:
   - grafana
   - prometheus
   - kiali (if enabled)

## Deployment Process

### Missing Components

Based on the verification results, you may need to deploy missing components:

1. **Service Mesh**: If Istio is not deployed
2. **Advanced Monitoring**: If Prometheus and Grafana are not deployed
3. **Autoscaling**: If HPAs are not configured
4. **Customer Dashboards**: If custom dashboards are not deployed

### Deployment Steps

To deploy missing components, follow these steps:

1. **Initialize Terraform**:
   ```bash
   cd /path/to/terraform
   terraform init
   ```

2. **Apply Terraform with specific targets**:
   ```bash
   # For Service Mesh
   terraform apply -target=module.service_mesh
   
   # For Advanced Monitoring
   terraform apply -target=module.grafana -target=module.kubernetes_dashboard
   
   # For Autoscaling
   terraform apply -target=module.autoscaling
   ```

3. **Verify deployment**:
   Run the verification script again to ensure all components are deployed:
   ```bash
   ./scripts/verify-all-resources.sh
   ```

4. **Connect to the bastion host** and verify Kubernetes resources:
   ```bash
   ./scripts/connect-to-bastion.sh
   ```

## Troubleshooting

### Common Issues

1. **EKS Connectivity Issues**:
   - Ensure you're connecting through the bastion host
   - Verify that the bastion host has the necessary IAM permissions
   - Check security groups to ensure the bastion host can access the EKS cluster

2. **Missing Kubernetes Resources**:
   - Check Terraform logs for any errors during deployment
   - Verify that the EKS cluster has the necessary IAM roles and policies
   - Check if the Kubernetes resources were deployed but failed to start

3. **Security Tool Issues**:
   - Verify that the necessary IAM roles and policies are in place
   - Check if the security tools were enabled but not properly configured

### Getting Help

If you encounter issues that you cannot resolve, contact the infrastructure team for assistance.
