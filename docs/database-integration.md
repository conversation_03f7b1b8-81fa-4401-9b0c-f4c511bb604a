# Database Integration for Multi-Tenant Applications

## Overview

This document describes the database integration for multi-tenant applications in our infrastructure. It covers the architecture, security considerations, and best practices for using Aurora Serverless v2 and MySQL 8.0 in a multi-tenant environment.

## Architecture

### Database Architecture

Our infrastructure supports two database configurations:

1. **Standard MySQL 8.0 RDS Instance** (Default)
   - Instance name: `production-architrave-db-new`
   - Instance class: `db.t3.medium` (configurable in terraform.tfvars)
   - Storage type: gp3 (SSD)
   - Allocated storage: 20GB (configurable)
   - Multi-AZ deployment for high availability

2. **Aurora Serverless v2 Cluster** (Optional, enabled via `use_aurora_serverless = true`)
   - Cluster with 2 instances (1 writer + 1 reader)
   - Scaling range: 0.5-16 ACUs (Aurora Capacity Units)
   - Instance type: `db.serverless`
   - Auto-scaling based on CPU, memory, and connections

Both configurations use schema-based tenant isolation:

```
Database (MySQL 8.0 or Aurora MySQL 8.0)
├── Writer Instance (Primary)
├── Reader Instance (Optional for Aurora)
└── Schemas
    ├── tenant_tenant1_db
    │   ├── Table 1
    │   ├── Table 2
    │   └── ...
    ├── tenant_tenant2_db
    │   ├── Table 1
    │   ├── Table 2
    │   └── ...
    └── ...
```

### Tenant Isolation

Each tenant has its own schema and database user:

1. **Schema Isolation**: Each tenant has a dedicated schema
2. **User Isolation**: Each tenant has a dedicated database user
3. **Permission Isolation**: Each user has permissions only to its own schema

```sql
-- Create schema for tenant
CREATE DATABASE tenant_tenant1_db;

-- Create user for tenant
CREATE USER 'tenant_tenant1'@'%' IDENTIFIED BY 'password';

-- Grant permissions to tenant user
GRANT ALL PRIVILEGES ON tenant_tenant1_db.* TO 'tenant_tenant1'@'%';
```

### Connection Management

We use RDS Proxy to manage database connections (enabled via `enable_proxy = true`):

1. **Connection Pooling**: RDS Proxy pools connections to reduce connection overhead
   - Connection borrowing timeout: 120 seconds
   - Maximum connections: 100% of database limit
   - Idle connections: 50% of maximum

2. **Connection Multiplexing**: Multiple application connections share database connections
3. **Automatic Failover**: RDS Proxy handles failover with minimal application disruption
4. **IAM Authentication**: Required for secure access

```
┌─────────────────┐     ┌─────────────┐     ┌─────────────────┐
│                 │     │             │     │                 │
│ Application Pods│────▶│  RDS Proxy  │────▶│ RDS/Aurora DB   │
│                 │     │             │     │                 │
└─────────────────┘     └─────────────┘     └─────────────────┘
```

### Network Connectivity

Pods connect to the database through a secure network path:

1. **Network Policies**: Allow specific traffic from tenant namespaces to RDS
2. **Security Groups**: RDS security group allows MySQL access (port 3306) only from within the VPC
3. **Private Subnets**: Database instances are deployed in private subnets
4. **VPC Endpoints**: For secure AWS service access without internet exposure

## Security Considerations

### Authentication

We use IAM database authentication:

1. **IAM Authentication**: Database users are authenticated using IAM
2. **Token-Based Access**: Applications use short-lived tokens for authentication
3. **No Hardcoded Credentials**: No database credentials in application configuration

```java
// Get IAM authentication token
String authToken = RdsIamAuthTokenGenerator.builder()
    .credentials(new DefaultAWSCredentialsProviderChain())
    .region(Regions.EU_CENTRAL_1)
    .build()
    .generateAuthenticationToken(
        GetIamAuthTokenRequest.builder()
            .hostname(hostname)
            .port(port)
            .userName(username)
            .build()
    );

// Use token to connect to database
Properties connectionProps = new Properties();
connectionProps.put("user", username);
connectionProps.put("password", authToken);
Connection connection = DriverManager.getConnection(jdbcUrl, connectionProps);
```

### Encryption

All data is encrypted:

1. **Encryption at Rest**: All data is encrypted using AWS KMS
2. **Encryption in Transit**: All connections use SSL/TLS
3. **Secure Parameter Group**: Database parameter group enforces security settings

### Access Control

Access to the database is strictly controlled:

1. **Least Privilege**: Tenants can only access their own data
2. **Network Security**: Database is in a private subnet with security group restrictions
3. **VPC Endpoints**: Database access is restricted to the VPC

## Performance Optimization

### Serverless Scaling (Aurora Configuration)

Aurora Serverless v2 scales automatically:

1. **Capacity Range**: Scales from 0.5 to 16 ACUs (Aurora Capacity Units)
2. **Automatic Scaling**: Scales based on CPU, connections, and memory
3. **Rapid Scaling**: Scales in seconds without disruption
4. **Instance Count**: 2 instances (1 writer + 1 reader)

### Read Scaling

Read scaling depends on the database configuration:

1. **Aurora Configuration**:
   - Reader instance for read operations
   - Connection routing via RDS Proxy to the reader instance
   - Applications can choose consistency level

2. **Standard RDS Configuration**:
   - Read replicas can be added manually if needed
   - Connection routing must be handled at the application level

### Query Optimization

We optimize queries for performance:

1. **Indexing Strategy**: Proper indexes for common queries
2. **Query Analysis**: Regular analysis of slow queries
3. **Schema Optimization**: Optimized schema design for tenant workloads

## Cost Optimization

### Serverless Pricing

Aurora Serverless v2 pricing is based on actual usage:

1. **Pay for Usage**: Pay only for the ACUs used
2. **Minimum Capacity**: Minimum capacity of 0.5 ACUs
3. **Storage Pricing**: Pay for storage used and I/O operations

### Resource Scheduling

We implement resource scheduling to reduce costs:

1. **Off-Hours Scaling**: Scale down during off-hours
2. **Development Environments**: Stop development environments when not in use
3. **Automated Scheduling**: Automated scheduling based on usage patterns

### Multi-Tenant Efficiency

Multi-tenant architecture reduces costs:

1. **Shared Infrastructure**: Multiple tenants share the same infrastructure
2. **Resource Pooling**: Resources are pooled across tenants
3. **Efficient Utilization**: Higher utilization of resources

## Monitoring and Alerting

### Performance Insights

We use Performance Insights for monitoring:

1. **Query Performance**: Monitor query performance in real-time
2. **Load Analysis**: Analyze database load by wait type
3. **Top SQL**: Identify top SQL statements by load

### Enhanced Monitoring

We use Enhanced Monitoring for detailed metrics:

1. **OS Metrics**: CPU, memory, disk I/O, and network
2. **Process List**: Database processes and their resource usage
3. **Fine-Grained Metrics**: Metrics at 1-second intervals

### CloudWatch Metrics

We monitor database using CloudWatch metrics:

1. **CPU Utilization**: CPU usage of the database instances
2. **Memory Utilization**: Memory usage of the database instances
3. **Connection Count**: Number of database connections
4. **Disk Queue Depth**: Number of outstanding I/O requests

### Tenant-Specific Monitoring

We implement tenant-specific monitoring:

1. **Schema Size**: Size of each tenant's schema
2. **Query Count**: Number of queries executed by each tenant
3. **Query Duration**: Duration of queries executed by each tenant
4. **Connection Count**: Number of connections per tenant
5. **Resource Usage**: CPU, memory, and I/O usage per tenant

### Alerts

We have alerts for various database-related issues:

1. **High CPU**: Alert when CPU utilization exceeds a threshold
2. **High Memory**: Alert when memory utilization exceeds a threshold
3. **Connection Spikes**: Alert when connection count spikes
4. **Slow Queries**: Alert when slow queries are detected

## Backup and Recovery

### Automated Backups

We implement automated backups:

1. **Daily Backups**: Automated daily backups
2. **Backup Retention**: Backups are retained for a configurable period
3. **Point-in-Time Recovery**: Ability to restore to any point in time within the retention period

### Tenant-Specific Backups

We implement tenant-specific backups:

1. **Schema Export**: Export of tenant schema and data
2. **Tenant Restoration**: Ability to restore a specific tenant
3. **Cross-Environment Restoration**: Ability to restore a tenant to a different environment

### Disaster Recovery

We have disaster recovery procedures:

1. **Multi-AZ Deployment**: Automatic failover to a different availability zone
2. **Database Cloning**: Ability to clone the database for testing and recovery
3. **Disaster Recovery Plan**: Comprehensive plan for recovering from disasters

## Integration with Applications

### Connection Management

Applications manage database connections efficiently:

1. **Connection Pooling**: Applications use connection pools
2. **Connection Reuse**: Connections are reused to reduce overhead
3. **Connection Timeout**: Connections have appropriate timeouts

Example connection pool configuration:

```java
HikariConfig config = new HikariConfig();
config.setJdbcUrl(jdbcUrl);
config.setUsername(username);
config.setPassword(password);
config.setMaximumPoolSize(10);
config.setMinimumIdle(2);
config.setIdleTimeout(300000);
config.setConnectionTimeout(10000);

HikariDataSource dataSource = new HikariDataSource(config);
```

### Tenant Context

Applications maintain tenant context:

1. **Tenant Identification**: Applications identify the tenant for each request
2. **Schema Selection**: Applications select the appropriate schema
3. **Connection Isolation**: Connections are isolated by tenant

Example tenant context implementation:

```java
// Set tenant context for the current thread
TenantContext.setCurrentTenant(tenantId);

// Get the current tenant's schema
String schema = "tenant_" + TenantContext.getCurrentTenant() + "_db";

// Set the schema for the current connection
connection.createStatement().execute("USE " + schema);
```

### Transaction Management

Applications manage transactions properly:

1. **Transaction Boundaries**: Clear transaction boundaries
2. **Transaction Isolation**: Appropriate isolation level
3. **Error Handling**: Proper error handling and transaction rollback

Example transaction management:

```java
Connection connection = dataSource.getConnection();
try {
    connection.setAutoCommit(false);

    // Perform database operations

    connection.commit();
} catch (Exception e) {
    connection.rollback();
    throw e;
} finally {
    connection.close();
}
```

## Schema Management

### Schema Versioning

We implement schema versioning:

1. **Version Control**: Schema changes are version controlled
2. **Migration Scripts**: Scripts for upgrading and downgrading schemas
3. **Tenant-Specific Migrations**: Ability to migrate specific tenants

### Schema Evolution

We manage schema evolution:

1. **Backward Compatibility**: Schema changes maintain backward compatibility
2. **Zero-Downtime Migrations**: Schema changes without downtime
3. **Rollback Plan**: Plan for rolling back schema changes

### Tenant Provisioning

We automate tenant provisioning:

1. **Schema Creation**: Automated creation of tenant schema
2. **User Creation**: Automated creation of tenant user
3. **Initial Data**: Automated loading of initial data

## Best Practices

1. **Use Schema-Based Isolation**: Isolate tenant data using schemas
2. **Implement Least Privilege**: Grant only the permissions needed
3. **Enable Encryption**: Encrypt all data at rest and in transit
4. **Use Connection Pooling**: Efficiently manage database connections
5. **Monitor Performance**: Monitor database performance and query execution
6. **Implement Backup and Recovery**: Have procedures for backup and recovery
7. **Optimize Queries**: Regularly analyze and optimize queries
8. **Manage Schema Evolution**: Carefully manage schema changes
9. **Scale Appropriately**: Configure appropriate scaling parameters
10. **Test Thoroughly**: Test database operations under load

## Configuration Reference

### Terraform Variables

Key database configuration variables in `terraform.tfvars`:

```hcl
# RDS Configuration
rds_instance_class         = "db.t3.medium"
rds_engine_version         = "8.0"
db_backup_retention_period = 7
db_multi_az                = true
db_deletion_protection     = false
```

### Module Configuration

Key database module configuration in `main.tf`:

```hcl
module "rds" {
  # ... other configuration ...

  # RDS configuration
  instance_class          = var.rds_instance_class
  engine_version          = var.rds_engine_version
  backup_retention_period = var.db_backup_retention_period
  multi_az                = var.db_multi_az
  deletion_protection     = var.db_deletion_protection

  # Aurora Serverless v2 configuration
  use_aurora_serverless = true # Enable Aurora Serverless v2
  aurora_min_capacity   = 0.5  # Minimum capacity in ACUs
  aurora_max_capacity   = 16   # Maximum capacity in ACUs
  aurora_instance_count = 2    # 1 writer + 1 reader

  # RDS Proxy configuration
  enable_proxy = true
}
```
