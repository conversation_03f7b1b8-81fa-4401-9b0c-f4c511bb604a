# Advanced Tenant Onboarding Script (Go Version)

This document provides comprehensive documentation for the advanced tenant onboarding script, a production-ready Go application designed to automate the deployment of new tenants on the Architrave platform.

## Overview

The Go-based onboarding script is a significant upgrade from the previous Python version, introducing a robust, secure, and reliable automation system. Key enhancements include:

- **Enhanced Security**: All secrets, including RDS credentials, are managed exclusively through AWS Secrets Manager. Hardcoded credentials are strictly forbidden.
- **Production-Ready Architecture**: The script establishes a simplified and reliable container architecture with proper resource management, health checks, and automated rollback capabilities.
- **Comprehensive Automation**: The tool handles the end-to-end process, from database and Kubernetes setup to security configuration and infrastructure validation.

## Key Features

- **Database Configuration**:
    - Securely fetches RDS credentials from AWS Secrets Manager.
    - Establishes SSL-enabled connections to RDS Aurora Serverless.
    - Imports the database schema from a designated S3 bucket.
    - Initializes user roles for the Access Control List (ACL) system.
- **Kubernetes Deployment**:
    - Creates tenant-specific namespaces and resources.
    - Configures deployments with appropriate resource limits, requests, and health probes.
    - Monitors deployment status and performs automated rollbacks on failure.
- **Security and Compliance**:
    - Enforces SSL/TLS encryption for all critical communications.
    - Implements comprehensive input validation and sanitization.

## Prerequisites

- Go (version 1.18 or higher)
- `kubectl` configured with access to the Kubernetes cluster.
- AWS CLI configured with necessary permissions to access Secrets Manager, S3, and RDS.

## Usage

The advanced onboarding script is a command-line tool. Below are the available flags and commands.

### Command-Line Flags

**Required:**
- `--tenant-id`: A unique identifier for the tenant (e.g., `new-customer`).

**Optional:**
- `--tenant-name`: The display name for the tenant.
- `--subdomain`: The subdomain for the tenant's endpoint. Defaults to the tenant ID.
- `--domain`: The primary domain name (default: `architrave-assets.com`).
- `--environment`: The deployment environment (default: `production`).
- `--language`: The default language for the tenant (default: `en`).
- `--rds-secret-name`: The name of the secret in AWS Secrets Manager (default: `production/rds/master-new`).
- `--local-sql-file`: Path to a local SQL file for database initialization.
- `--s3-bucket`: The S3 bucket for the SQL schema (default: `architravetestdb`).
- `--s3-key`: The S3 key for the SQL schema file (default: `architrave_1.45.2.sql`).
- `--frontend-image`, `--backend-image`, `--nginx-image`, `--rabbitmq-image`: Specify custom container images.
- `--skip-s3-setup`, `--skip-istio`, `--skip-monitoring`, `--skip-dns`, `--skip-web-check`, `--skip-capacity-check`: Flags to bypass specific stages of the onboarding process.
- `--debug`, `--minimal`, `--production`: Flags to control the deployment mode and verbosity.

**Enhanced Functionality Flags:**
- `--enable-auto-fix`: Enables automatic resolution of common infrastructure issues.
- `--enable-hetzner-dns`: Enables integration with Hetzner DNS for automated record creation.
- `--enable-production-audit`: Runs a comprehensive audit to verify production readiness.
- `--enable-node-scaling`: Enables automatic node scaling.
- `--enable-service-mesh`: Enables service mesh configuration.
- `--ssl-certificate-arn`: The ARN of the SSL certificate to use for the ALB.
- `--hetzner-api-token`: The API token for Hetzner DNS.

### Example

```bash
go run tenant-management/scripts/advanced_tenant_onboard.go \
  --tenant-id "example-tenant" \
  --tenant-name "Example Tenant" \
  --enable-production-audit
```

## Onboarding Process

The script performs the following steps:

1.  **Initialization**: Initializes AWS and Kubernetes clients.
2.  **Cluster Health Validation**: Validates cluster capacity and health before proceeding.
3.  **Database Setup**:
    -   Retrieves RDS credentials from AWS Secrets Manager.
    -   Imports the database schema from S3.
    -   Initializes user roles required for the application's ACL system.
4.  **Kubernetes Resources**:
    -   Creates a dedicated namespace for the tenant (`tenant-<tenant-id>`).
    -   Creates ConfigMaps for application configuration and Secrets for database credentials.
5.  **Deployments**:
    -   Deploys RabbitMQ for messaging.
    -   Deploys the backend and frontend applications with appropriate configurations.
    -   Deploys a health check container to monitor the tenant's services.
6.  **Networking**:
    -   Creates an Istio VirtualService to expose the tenant's application externally via a shared gateway.
    -   (Optional) Configures DNS records in Hetzner.
7.  **Validation**:
    -   Waits for all deployments to become ready.
    -   Performs final validation of the tenant setup, including internet accessibility and WAF protection.
    -   (Optional) Runs a full production readiness audit.

## Security

The new onboarding script is built with a security-first approach:

-   **No Hardcoded Credentials**: Database passwords and other secrets are fetched from AWS Secrets Manager at runtime.
-   **SSL/TLS Everywhere**: Communication with the RDS database is secured with SSL.
-   **IAM Roles for Service Accounts (IRSA)**: Securely provides AWS permissions to Kubernetes pods.

## Troubleshooting

If you encounter issues during onboarding:

1.  **Check the script's output**: The script provides detailed logging for each step.
2.  **Inspect Pod Logs**:
    ```bash
    kubectl logs -n tenant-<tenant-id> <pod-name>
    ```
3.  **Check Pod Status**:
    ```bash
    kubectl get pods -n tenant-<tenant-id>
    ```
4.  **Review Events**:
    ```bash
    kubectl get events -n tenant-<tenant-id>