# Quick Start Guide

## One-Line Deployment

```bash
# Clone the repository and run the deployment script
git clone <repository-url> && cd infra-provisioning && ./deploy-all.sh
```

## Step-by-Step Deployment

1. **Set up AWS credentials**
   ```bash
   cat > credentials-velero <<EOF
   [default]
   aws_access_key_id=<your-access-key>
   aws_secret_access_key=<your-secret-key>
   EOF
   ```

2. **Deploy components**
   ```bash
   # Deploy backup and monitoring
   ./deploy-additional-components.sh
   ./deploy-monitoring.sh
   ```

3. **Verify deployment**
   ```bash
   # Check all components
   kubectl get pods -n velero
   kubectl get pods -n monitoring
   kubectl get pods -n testing
   ```

## Access Dashboards

- Backup Monitoring: `http://grafana.monitoring:3000/d/backup-monitoring`
- Performance Monitoring: `http://grafana.monitoring:3000/d/performance-monitoring`

## Common Commands

### Backup Operations
```bash
# List backups
velero backup get

# Create backup
velero backup create <backup-name>

# Restore from backup
velero restore create --from-backup <backup-name>
```

### Monitoring
```bash
# Check alerts
kubectl get prometheusrules -n monitoring

# View logs
kubectl logs -n monitoring -l app=grafana
kubectl logs -n monitoring -l app=prometheus
```

### Performance Testing
```bash
# Run test
kubectl create -f testing/performance-test.yaml

# View results
kubectl logs -n testing -l app=k6
```

## Troubleshooting

1. **Backup Issues**
   ```bash
   kubectl logs -n velero -l app.kubernetes.io/name=velero
   ```

2. **Monitoring Issues**
   ```bash
   kubectl logs -n monitoring -l app=grafana
   kubectl logs -n monitoring -l app=prometheus
   ```

3. **Performance Testing Issues**
   ```bash
   kubectl logs -n testing -l app=k6
   ```

## Support

For detailed documentation, see [DEPLOYMENT_GUIDE.md](DEPLOYMENT_GUIDE.md)
For support, contact: <EMAIL> 