# S3 Integration for Multi-Tenant Applications

## Overview

This document describes the S3 integration for multi-tenant applications in our infrastructure. It covers the architecture, security considerations, and best practices for using S3 in a multi-tenant environment.

## Architecture

### S3 Bucket Structure

We use a shared S3 bucket with prefix-based isolation for tenant data:

```
production-assets/
├── tenant-tenant1/
│   ├── uploads/
│   ├── exports/
│   └── backups/
├── tenant-tenant2/
│   ├── uploads/
│   ├── exports/
│   └── backups/
└── ...
```

### Access Control

Each tenant has its own IAM role with permissions limited to its own prefix:

```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "s3:ListBucket"
      ],
      "Resource": [
        "arn:aws:s3:::production-assets"
      ],
      "Condition": {
        "StringLike": {
          "s3:prefix": [
            "tenant-tenant1/*"
          ]
        }
      }
    },
    {
      "Effect": "Allow",
      "Action": [
        "s3:GetObject",
        "s3:PutObject",
        "s3:DeleteObject"
      ],
      "Resource": [
        "arn:aws:s3:::production-assets/tenant-tenant1/*"
      ]
    }
  ]
}
```

### IAM Roles for Service Accounts (IRSA)

We use IRSA to provide pod-level access to S3:

1. **Service Account**: Each tenant has a dedicated service account in its namespace
2. **IAM Role**: The service account is annotated with the ARN of the tenant's IAM role
3. **Pod Access**: Pods using the service account can access S3 with the permissions of the IAM role

```yaml
apiVersion: v1
kind: ServiceAccount
metadata:
  name: tenant-app-sa
  namespace: tenant-tenant1
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::************:role/production-tenant-tenant1-role
```

## Security Considerations

### Encryption

All data in S3 is encrypted:

1. **Server-Side Encryption**: SSE-S3 (AES-256) encryption for all objects
2. **Client-Side Encryption**: Optional client-side encryption for sensitive data
3. **TLS**: All data is encrypted in transit using TLS

### Access Control

Access to S3 is strictly controlled:

1. **Least Privilege**: Tenants can only access their own data
2. **IAM Policies**: Fine-grained IAM policies restrict access to specific prefixes
3. **Bucket Policies**: Additional bucket policies enforce security requirements
4. **VPC Endpoints**: S3 access is restricted to the VPC using VPC endpoints

### Audit Logging

All S3 operations are logged:

1. **S3 Access Logs**: All S3 access is logged to a dedicated logging bucket
2. **CloudTrail**: API calls are logged to CloudTrail
3. **Log Analysis**: Logs are analyzed for suspicious activity

## Data Lifecycle Management

### Lifecycle Policies

We use lifecycle policies to manage data lifecycle:

1. **Transition Rules**: Objects are transitioned to cheaper storage classes after a period of time
2. **Expiration Rules**: Objects are deleted after a retention period
3. **Tenant-Specific Rules**: Each tenant can have its own lifecycle rules

Example lifecycle configuration:

```json
{
  "Rules": [
    {
      "ID": "logs-transition",
      "Prefix": "tenant-tenant1/logs/",
      "Status": "Enabled",
      "Transition": {
        "Days": 30,
        "StorageClass": "STANDARD_IA"
      },
      "Expiration": {
        "Days": 90
      }
    },
    {
      "ID": "backups-transition",
      "Prefix": "tenant-tenant1/backups/",
      "Status": "Enabled",
      "Transition": {
        "Days": 30,
        "StorageClass": "GLACIER"
      }
    }
  ]
}
```

### Versioning

We use versioning to protect against accidental deletion:

1. **Bucket Versioning**: Versioning is enabled on the bucket
2. **Version Lifecycle**: Old versions are transitioned to cheaper storage classes
3. **Version Expiration**: Old versions are deleted after a retention period

## Performance Optimization

### CloudFront Integration

We use CloudFront to improve performance:

1. **Content Delivery**: CloudFront distributes content from edge locations
2. **Cache Control**: Cache control headers optimize caching
3. **Origin Access Identity**: CloudFront accesses S3 using an Origin Access Identity

### Transfer Acceleration

We use Transfer Acceleration for large uploads:

1. **Accelerated Transfers**: Transfer Acceleration uses the AWS edge network
2. **Multipart Uploads**: Large files are uploaded in parallel using multipart uploads
3. **Presigned URLs**: Presigned URLs allow direct uploads to S3

## Cost Optimization

### Storage Classes

We use different storage classes to optimize costs:

1. **Standard**: For frequently accessed data
2. **Standard-IA**: For infrequently accessed data
3. **Glacier**: For archival data

### Intelligent Tiering

We use Intelligent Tiering for data with changing access patterns:

1. **Automatic Tiering**: Objects are automatically moved between access tiers
2. **Monitoring Fee**: Small monitoring fee per object
3. **Cost Savings**: Significant cost savings for objects with changing access patterns

## Integration with Applications

### Direct Integration

Applications can integrate directly with S3:

1. **AWS SDK**: Applications use the AWS SDK to access S3
2. **IAM Roles**: Applications use IAM roles for authentication
3. **Prefix Isolation**: Applications use tenant-specific prefixes

Example code:

```java
// Get the tenant ID from the request
String tenantId = getTenantId(request);

// Create an S3 client
AmazonS3 s3Client = AmazonS3ClientBuilder.standard()
    .withRegion(Regions.EU_CENTRAL_1)
    .build();

// Upload a file to the tenant's prefix
s3Client.putObject(
    "production-assets",
    "tenant-" + tenantId + "/uploads/" + fileName,
    file
);
```

### Presigned URLs

Applications can generate presigned URLs for direct client access:

1. **Upload URLs**: Presigned URLs for direct uploads
2. **Download URLs**: Presigned URLs for direct downloads
3. **Expiration**: URLs expire after a short period

Example code:

```java
// Get the tenant ID from the request
String tenantId = getTenantId(request);

// Create an S3 client
AmazonS3 s3Client = AmazonS3ClientBuilder.standard()
    .withRegion(Regions.EU_CENTRAL_1)
    .build();

// Generate a presigned URL for upload
URL presignedUrl = s3Client.generatePresignedUrl(
    "production-assets",
    "tenant-" + tenantId + "/uploads/" + fileName,
    Date.from(Instant.now().plus(1, ChronoUnit.HOURS)),
    HttpMethod.PUT
);
```

## Monitoring and Alerting

### CloudWatch Metrics

We monitor S3 using CloudWatch metrics:

1. **Bucket Size**: Total size of the bucket
2. **Object Count**: Number of objects in the bucket
3. **Request Rate**: Number of requests per second
4. **Error Rate**: Number of error responses

### Custom Metrics

We collect custom metrics for tenant-specific monitoring:

1. **Tenant Storage**: Storage used by each tenant
2. **Tenant Requests**: Requests made by each tenant
3. **Tenant Errors**: Errors encountered by each tenant

### Alerts

We have alerts for various S3-related issues:

1. **High Error Rate**: Alert when error rate exceeds a threshold
2. **Storage Growth**: Alert when storage growth exceeds a threshold
3. **Access Denied**: Alert when access is denied to a tenant

## Disaster Recovery

### Cross-Region Replication

We use Cross-Region Replication for disaster recovery:

1. **Replication Configuration**: Objects are replicated to a bucket in another region
2. **Replication Time Control**: Predictable replication time
3. **Failover**: Ability to failover to the replica in case of a regional outage

### Backup and Restore

We have backup and restore procedures for S3 data:

1. **Regular Backups**: Regular backups of critical data
2. **Point-in-Time Recovery**: Ability to restore to any previous version
3. **Disaster Recovery Plan**: Comprehensive plan for recovering from disasters

## Best Practices

1. **Use Prefix-Based Isolation**: Isolate tenant data using prefixes
2. **Implement Least Privilege**: Grant only the permissions needed
3. **Enable Encryption**: Encrypt all data at rest and in transit
4. **Use Lifecycle Policies**: Manage data lifecycle to optimize costs
5. **Monitor Usage**: Monitor storage usage and access patterns
6. **Implement Backup and Restore**: Have procedures for backup and restore
7. **Use Versioning**: Enable versioning to protect against accidental deletion
8. **Optimize Performance**: Use CloudFront and Transfer Acceleration for better performance
