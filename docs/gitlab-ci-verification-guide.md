# GitLab CI Pipeline Verification Guide

This guide provides detailed instructions for verifying and fixing the GitLab CI pipeline to ensure it can create and deploy infrastructure without issues.

## Table of Contents

1. [Overview](#overview)
2. [Prerequisites](#prerequisites)
3. [Verification Process](#verification-process)
4. [Common Issues and Fixes](#common-issues-and-fixes)
5. [Testing the Pipeline](#testing-the-pipeline)

## Overview

The GitLab CI pipeline is configured to automate the deployment of infrastructure using Terraform. The pipeline consists of several stages:

1. **Validate**: Validates the Terraform configuration
2. **Plan**: Creates a Terraform plan
3. **Apply-Infrastructure**: Applies the Terraform plan to create/update infrastructure
4. **Apply-Kubernetes**: Applies Kubernetes resources
5. **Security**: Deploys security tools
6. **Monitoring**: Verifies monitoring setup
7. **Documentation**: Generates documentation
8. **Destroy**: Destroys all resources (manual only)

## Prerequisites

- Access to the GitLab repository
- GitLab Runner installed (for local validation)
- AWS CLI installed and configured
- Terraform installed

## Verification Process

### 1. Check GitLab CI Configuration

Run the GitLab CI verification script:

```bash
./scripts/gitlab_pipeline_check.sh
```

This script checks:
- If `.gitlab-ci.yml` exists
- If `provider.tf.ci` exists
- YAML syntax of `.gitlab-ci.yml`
- Bash syntax of scripts referenced in the pipeline

### 2. Validate GitLab CI Configuration

If you have GitLab Runner installed, you can validate the GitLab CI configuration:

```bash
gitlab-runner lint .gitlab-ci.yml
```

### 3. Check for Missing Variables

Ensure the following variables are properly set in the GitLab CI configuration:

- `TF_VAR_skip_k8s_connection: "true"`
- `TF_VAR_skip_kubernetes_resources: "true"`
- `TF_VAR_check_if_cluster_exists: "true"`

These variables are crucial for ensuring the pipeline can run without Kubernetes connectivity issues.

### 4. Check for Provider Configuration

Ensure that `provider.tf.ci` exists and contains the correct configuration for the AWS provider without Kubernetes provider configuration:

```hcl
provider "aws" {
  region = var.aws_region
}

# No Kubernetes provider configuration in CI/CD
```

## Common Issues and Fixes

### 1. Missing `check_if_cluster_exists` Variable

**Issue**: The pipeline fails with an error about an undeclared variable `check_if_cluster_exists`.

**Fix**: Add the variable to the `.gitlab-ci.yml` file:

```yaml
variables:
  # Other variables...
  TF_VAR_check_if_cluster_exists: "true"
```

### 2. Kubernetes Connectivity Issues

**Issue**: The pipeline fails when trying to connect to the Kubernetes cluster.

**Fix**: Ensure the following variables are set in the `.gitlab-ci.yml` file:

```yaml
variables:
  # Other variables...
  TF_VAR_skip_k8s_connection: "true"
  TF_VAR_skip_kubernetes_resources: "true"
```

Also, ensure that the `apply-infrastructure` job explicitly passes these variables:

```yaml
apply-infrastructure:
  # Other configuration...
  script:
    # Other commands...
    - terraform apply -auto-approve -compact-warnings -var="skip_k8s_connection=true" tfplan
```

### 3. Provider Configuration Issues

**Issue**: The pipeline fails with provider configuration errors.

**Fix**: Create or update `provider.tf.ci` with the correct configuration:

```hcl
provider "aws" {
  region = var.aws_region
}

# No Kubernetes provider configuration in CI/CD
```

And ensure the pipeline copies this file before running Terraform:

```yaml
script:
  - cp provider.tf.ci provider.tf
  - terraform init
  # Other commands...
```

### 4. Missing Dependencies

**Issue**: The pipeline fails because it's missing required dependencies.

**Fix**: Create an `install-ci-dependencies.sh` script and call it in the pipeline:

```bash
#!/bin/bash
set -e

# Install required dependencies
apt-get update
apt-get install -y curl jq

# Install AWS CLI
curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
unzip awscliv2.zip
./aws/install
```

And update the pipeline to call this script:

```yaml
script:
  - ./install-ci-dependencies.sh
  # Other commands...
```

## Testing the Pipeline

To test the pipeline, follow these steps:

### 1. Create a Test Branch

```bash
git checkout -b test-pipeline
```

### 2. Make Necessary Changes

Apply the fixes mentioned in the [Common Issues and Fixes](#common-issues-and-fixes) section.

### 3. Commit and Push Changes

```bash
git add .
git commit -m "Fix GitLab CI pipeline issues"
git push origin test-pipeline
```

### 4. Create a Merge Request

Create a merge request in GitLab and check if the pipeline runs successfully.

### 5. Monitor the Pipeline

Monitor the pipeline execution and check for any errors. If errors occur, refer to the [Common Issues and Fixes](#common-issues-and-fixes) section for solutions.

### 6. Merge Changes

If the pipeline runs successfully, merge the changes into the main branch.
