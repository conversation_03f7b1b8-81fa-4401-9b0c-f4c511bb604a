# GitLab CI/CD Pipeline Flow

This document visualizes the flow of the GitLab CI/CD pipeline for the infrastructure provisioning project.

## Pipeline Stages

The pipeline consists of the following stages:

1. **Validate**: Validates the Terraform configuration (automatic)
2. **Plan**: Creates a Terraform plan (automatic)
3. **Apply Infrastructure**: Applies the Terraform plan to create/update infrastructure (automatic)
4. **Apply Kubernetes**: Applies Kubernetes resources (automatic)
5. **Security**: Deploys security tools (automatic)
6. **Monitoring**: Verifies monitoring setup (automatic)
7. **Documentation**: Generates documentation (automatic)
8. **Destroy**: Destroys all resources (manual only)

## Pipeline Flow Diagram

```mermaid
flowchart TB
    subgraph "Validation Stage"
        B["validate"]
        style B fill:#90EE90,stroke:#333
    end

    subgraph "Planning Stage"
        C["plan"]
        style C fill:#87CEEB,stroke:#333
    end

    subgraph "Apply Stage"
        D["apply-infrastructure"]
        E["apply-kubernetes"]
        style D fill:#87CEEB,stroke:#333
        style E fill:#87CEEB,stroke:#333
    end

    subgraph "Security Stage"
        S["deploy_security_tools"]
        style S fill:#FFB6C1,stroke:#333
    end

    subgraph "Monitoring Stage"
        G["verify_monitoring"]
        style G fill:#FFB6C1,stroke:#333
    end

    subgraph "Documentation Stage"
        I["pipeline_docs"]
        style I fill:#90EE90,stroke:#333
    end

    subgraph "Destroy Stage"
        H["destroy"]
        style H fill:#ff6666,stroke:#333
    end

    B --> C
    C --> D
    D --> E
    E --> S
    S --> G
    G --> I
    I --> H

    classDef manual fill:#ffd700,stroke:#333,stroke-width:2px
    classDef auto fill:#90EE90,stroke:#333,stroke-width:2px
    classDef destructive fill:#ff6666,stroke:#333,stroke-width:2px
    class H manual
    class B,C,D,E,S,G,I auto
    class H destructive
```

## Pipeline Stages Description

- **Validation Stage**: Configuration validation and syntax checking
- **Planning Stage**: Infrastructure planning and change preview
- **Apply Stage**: Core infrastructure and Kubernetes deployment
- **Security Stage**: Deployment of security tools and configurations
- **Monitoring Stage**: Monitoring setup and verification of metrics collection
- **Documentation Stage**: Generation of pipeline and infrastructure documentation
- **Destroy Stage**: Resource cleanup and decommissioning (manual trigger required)

## Job Types

- 🟡 Yellow: Manual intervention required
- 🟢 Green: Validation and documentation jobs
- 🔵 Blue: Infrastructure deployment
- 🩷 Pink: Security and monitoring setup
- 🔴 Red: Destructive operations
