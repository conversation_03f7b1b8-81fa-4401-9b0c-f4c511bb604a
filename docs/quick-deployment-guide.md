# Quick Deployment Guide: Performance Optimization & Tenant Management UI

## 🚀 Quick Start (5 Minutes)

### Prerequisites
- Kubernetes cluster with kubectl access
- Helm 3.x installed
- Docker (for building UI images)
- Prometheus and Grafana already deployed

### Step 1: Deploy Performance Optimization Features

```bash
# Navigate to project root
cd /Users/<USER>/Projects/new_project/infra-provisioning

# Deploy enhanced autoscaling, monitoring, and cost tracking
./scripts/tenant/deploy-performance-optimization.sh

# For specific tenant optimization
./scripts/tenant/deploy-performance-optimization.sh --tenant-id demo-tenant --tier standard
```

**What this deploys**:
- ✅ KEDA for advanced autoscaling
- ✅ Vertical Pod Autoscaler (VPA)
- ✅ Enhanced Prometheus alerting rules
- ✅ Grafana dashboards for performance and cost tracking
- ✅ Database connection pooling optimization
- ✅ Cost calculation CronJob

### Step 2: Deploy Web-Based Tenant Management UI

```bash
# Deploy the complete tenant management interface
./scripts/tenant/deploy-tenant-ui.sh

# Access the UI
echo "127.0.0.1 tenant-management.local" | sudo tee -a /etc/hosts
```

**What this deploys**:
- ✅ React-based tenant management dashboard
- ✅ Backend API for tenant operations
- ✅ Self-service onboarding interface
- ✅ Resource management controls
- ✅ Cost tracking integration

### Step 3: Configure Log Aggregation with Tenant Isolation

```bash
# Apply Loki tenant isolation configuration
kubectl apply -f monitoring/loki-tenant-isolation-config.yaml

# Restart Loki to apply new configuration
kubectl rollout restart deployment/loki -n monitoring
```

## 🎯 Immediate Benefits

### Performance Optimization
- **Auto-scaling**: Pods automatically scale based on CPU, memory, and custom metrics
- **Database Optimization**: Connection pooling reduces database load by 40-60%
- **Cost Tracking**: Real-time cost monitoring with budget alerts
- **SLA Monitoring**: Automated alerts for response time, availability, and error rate violations

### Web-Based Management
- **Self-Service**: Tenants can onboard themselves in 5 minutes
- **Resource Control**: Real-time scaling and resource management
- **Cost Visibility**: Transparent cost tracking and optimization recommendations
- **Performance Insights**: Comprehensive dashboards for monitoring

## 📊 Access Your New Dashboards

### Grafana Dashboards
1. **Tenant Performance**: `http://grafana.local/d/tenant-performance`
2. **Cost Tracking**: `http://grafana.local/d/tenant-cost-tracking`

### Tenant Management UI
- **Main Interface**: `http://tenant-management.local`
- **Self-Service Onboarding**: `http://tenant-management.local/onboard`
- **Resource Management**: `http://tenant-management.local/resources`

## 🚀 Onboarding a New Tenant

You can onboard a new tenant using either the self-service web UI or the command-line Go script. Both methods ensure tenants are configured securely with HTTPS enabled by default.

### Using the Tenant Management UI (Self-Service)

The web UI provides a user-friendly wizard for onboarding new tenants.

1.  Navigate to the onboarding page: `http://tenant-management.local/onboard`
2.  Click **"Create New Tenant"**.
3.  Follow the 5-step wizard to enter tenant details.
4.  Watch the deployment progress in real-time.
5.  Once complete, the tenant will be ready and accessible.

### Using the Command-Line Script (Go)

For automated or scripted onboarding, use the `advanced_tenant_onboard.go` script. This script handles the entire onboarding process, including creating tenant-specific resources, configuring HTTPS with AWS Certificate Manager, and securely setting up the database schema from S3 using credentials from AWS Secrets Manager.

**1. Navigate to the script directory:**
```bash
cd /Users/<USER>/Projects/new_project/infra-provisioning/tenant-management/scripts
```

**2. Run the onboarding script:**
The script is designed to be run with minimal arguments for a standard setup.

```bash
# Onboard a new tenant with default settings (HTTPS enabled)
go run advanced_tenant_onboard.go \
    --tenant-id="new-cli-tenant" \
    --tenant-name="New CLI Tenant"
## 🔧 Quick Configuration

### Tier-Based Auto-Scaling

```bash
# Apply enhanced autoscaling to existing tenant
export TENANT_ID="demo-tenant"
export TENANT_TIER="standard"  # basic, standard, premium

# Apply configuration
envsubst < kubernetes/autoscaling/enhanced-tenant-autoscaling.yaml | kubectl apply -f -
```

### Database Connection Pool Optimization

Add to your tenant's environment variables:
```bash
# Standard tier settings
DB_POOL_MIN=5
DB_POOL_MAX=20
DB_POOL_IDLE_TIMEOUT=300
DB_POOL_MAX_LIFETIME=3600
DB_POOL_ACQUIRE_TIMEOUT=30
```

## 📈 Immediate Testing

### Test Auto-Scaling
```bash
# Generate load to trigger scaling
kubectl run load-test --image=busybox --rm -it --restart=Never -- \
  /bin/sh -c "while true; do wget -q -O- http://demo-tenant-app/api/health; done"

# Watch scaling in action
kubectl get hpa -n tenant-demo-tenant -w
```

### Test Cost Tracking
```bash
# Check cost annotations
kubectl get namespace tenant-demo-tenant -o yaml | grep cost

# View cost metrics in Prometheus
curl -s "http://prometheus.local/api/v1/query?query=tenant_hourly_cost"
```

### Test Self-Service Onboarding
1. Go to `http://tenant-management.local`
2. Click "Create New Tenant"
3. Follow the 5-step wizard
4. Watch deployment progress in real-time

## 🚨 Monitoring & Alerts

### Key Alerts Now Active
- **SLA Violations**: Response time >500ms, availability <99.5%
- **Cost Spikes**: 50% increase in hourly costs
- **Resource Exhaustion**: CPU >80%, Memory >85%
- **Security Issues**: High 403 error rates, suspicious traffic

### View Active Alerts
```bash
# Check Prometheus alerts
kubectl port-forward svc/prometheus-server 9090:80 -n monitoring &
open http://localhost:9090/alerts

# Check AlertManager
kubectl port-forward svc/alertmanager 9093:9093 -n monitoring &
open http://localhost:9093
```

## 🔍 Log Aggregation

### Query Tenant-Specific Logs
```bash
# Access Loki
kubectl port-forward svc/loki 3100:3100 -n monitoring &

# Example LogQL queries
curl -G -s "http://localhost:3100/loki/api/v1/query" \
  --data-urlencode 'query={namespace="tenant-demo-tenant"} |= "ERROR"'
```

### Grafana Log Dashboard
- Access: `http://grafana.local/explore`
- Data Source: Loki
- Query: `{namespace="tenant-demo-tenant"} |= "ERROR"`

## 🎛️ Resource Management

### Scale Tenant Resources
```bash
# Via kubectl
kubectl scale deployment demo-tenant-app --replicas=5 -n tenant-demo-tenant

# Via UI
# Go to http://tenant-management.local/tenants/demo-tenant/resources
# Adjust sliders for CPU, Memory, Replicas
# Click "Apply Changes"
```

### Update Tenant Tier
```bash
# Label namespace with new tier
kubectl label namespace tenant-demo-tenant tier=premium --overwrite

# Apply tier-specific configurations
./scripts/tenant/deploy-performance-optimization.sh --tenant-id demo-tenant --tier premium
```

## 💰 Cost Optimization

### View Cost Breakdown
1. **Grafana**: `http://grafana.local/d/tenant-cost-tracking`
2. **API**: `curl http://tenant-management.local/api/tenants/demo-tenant/costs`
3. **CLI**: `kubectl get namespace tenant-demo-tenant -o jsonpath='{.metadata.annotations.cost\.architrave\.com/hourly}'`

### Optimization Recommendations
- **Right-size resources**: Use VPA recommendations
- **Optimize scaling**: Adjust HPA thresholds based on usage patterns
- **Schedule workloads**: Use node affinity for cost-effective scheduling

## 🔧 Troubleshooting

### Common Issues

1. **UI Not Accessible**:
   ```bash
   kubectl get pods -n tenant-management
   kubectl logs -f deployment/tenant-management-ui -n tenant-management
   ```

2. **Auto-scaling Not Working**:
   ```bash
   kubectl describe hpa -n tenant-demo-tenant
   kubectl get --raw /apis/metrics.k8s.io/v1beta1/nodes
   ```

3. **Cost Tracking Missing**:
   ```bash
   kubectl logs cronjob/tenant-cost-calculator -n monitoring
   kubectl get configmap database-optimization-config -n monitoring
   ```

4. **Alerts Not Firing**:
   ```bash
   kubectl get prometheusrules -n monitoring
   kubectl logs deployment/prometheus-server -n monitoring
   ```

## 🎯 Next Steps

1. **Configure Authentication**: Integrate with your OAuth provider
2. **Set Up Notifications**: Configure Slack/email for alerts
3. **Customize Dashboards**: Add business-specific metrics
4. **Scale Testing**: Run load tests to validate auto-scaling
5. **Cost Budgets**: Set up budget alerts and limits

## 📞 Support

- **Logs**: Check pod logs in respective namespaces
- **Metrics**: Use Grafana dashboards for detailed analysis
- **Documentation**: Refer to `docs/performance-optimization-and-tenant-ui.md`
- **Issues**: Check GitHub issues or create new ones

---

**🎉 Congratulations!** You now have a production-ready, high-performance tenant management system with comprehensive monitoring, cost tracking, and self-service capabilities.
