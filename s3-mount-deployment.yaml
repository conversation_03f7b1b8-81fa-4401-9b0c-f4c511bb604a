apiVersion: apps/v1
kind: Deployment
metadata:
  name: test-tenant-maintenance-backend
  namespace: tenant-test-tenant-maintenance
spec:
  template:
    spec:
      volumes:
      - name: s3-assets
        emptyDir: {}
      - name: s3-mount-config
        configMap:
          name: s3-mount-config
      containers:
      - name: s3-mount-sidecar
        image: amazon/aws-cli:latest
        command:
        - /bin/bash
        - -c
        - |
          while true; do
            aws s3 sync s3://architravetestdb/assets /s3-assets --region eu-central-1
            sleep 300
          done
        volumeMounts:
        - name: s3-assets
          mountPath: /s3-assets
        env:
        - name: AWS_REGION
          value: eu-central-1
      - name: backend
        volumeMounts:
        - name: s3-assets
          mountPath: /storage/assets
          readOnly: true 