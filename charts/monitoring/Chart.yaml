apiVersion: v2
name: monitoring
description: A Helm chart for Kubernetes monitoring stack
type: application
version: 0.1.0
appVersion: "1.0"
dependencies:
  - name: prometheus
    version: "25.1.0"
    repository: "https://prometheus-community.github.io/helm-charts"
    condition: prometheus.enabled
  - name: grafana
    version: "7.3.1"
    repository: "https://grafana.github.io/helm-charts"
    condition: grafana.enabled
  - name: loki
    version: "5.46.1"
    repository: "https://grafana.github.io/helm-charts"
    condition: loki.enabled
