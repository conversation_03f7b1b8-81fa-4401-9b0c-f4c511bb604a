prometheus:
  server:
    persistentVolume:
      enabled: false
    service:
      type: ClusterIP
    alertmanagers:
      - static_configs:
          - targets:
              - alertmanager:9093

  alertmanager:
    enabled: true
    service:
      type: ClusterIP
    config:
      global:
        resolve_timeout: 5m
      route:
        group_by: ['alertname']
        receiver: 'default'
        routes:
          - match:
              severity: critical
            receiver: 'critical'
      receivers:
        - name: 'default'
          webhook_configs:
            - url: 'http://alert-webhook/'
        - name: 'critical'
          webhook_configs:
            - url: 'http://critical-alerts-webhook/'

grafana:
  adminPassword: "admin"
  service:
    type: ClusterIP
  datasources:
    datasources.yaml:
      apiVersion: 1
      datasources:
      - name: Prometheus
        type: prometheus
        url: http://prometheus-server
        access: proxy
  dashboards:
    default:
      k8s-cluster:
        gnetId: 315
        revision: 3
        datasource: Prometheus
      node-exporter:
        gnetId: 1860
        revision: 28
        datasource: Prometheus

loki:
  enabled: true
  service:
    type: ClusterIP
  config:
    auth_enabled: false
    ingester:
      chunk_idle_period: 3m
      chunk_target_size: 1536000
    schema_config:
      configs:
        - from: 2020-10-24
          store: boltdb-shipper
          object_store: filesystem
          schema: v11
          index:
            prefix: index_
            period: 24h
