data "aws_organizations_organization" "org" {}

# Create Platform OU
module "platform_ou" {
  source    = "../../modules/aws/organizations_organizational_unit"
  ou_name   = var.ou_name
  parent_id = var.parent_id
  tags      = var.ou_tags
}

# Create multiple accounts dynamically
module "accounts" {
  source        = "../../modules/aws/organizations_account"
  for_each      = var.accounts
  account_name  = each.key
  account_email = each.value.email
  parent_id     = module.platform_ou.ou_id
  tags          = each.value.tags
}