aws_profile = "aws_root_account"
region      = "eu-central-1"
ou_name     = "Platform OU"
parent_id   = "r-iue4"
ou_tags     = { "Type" = "migration_architrave" }

accounts = {
  production = { email = "<EMAIL>"
    tags = {
      "Name" = "Platform Production account"
      "Type" = "migration_architrave"
    }
  }
  staging = { email = "<EMAIL>"
    tags = {
      "Name" = "Platform Staging account"
      "Type" = "migration_architrave"
    }
  }
  development = { email = "<EMAIL>"
    tags = {
      "Name" = "Platform Development account"
      "Type" = "migration_architrave"
    }
  }
}

organization_id = "o-y8lj880yet"