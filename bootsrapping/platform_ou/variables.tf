variable "aws_profile" {
  description = "AWS profile to create resources"
  type        = string
}

variable "region" {
  description = "The AWS region to create resources in"
  type        = string
}

variable "ou_name" {
  description = "Name of the Platform Organizational Unit"
  type        = string
}

variable "parent_id" {
  description = "Parent Organizational Unit or Root ID"
  type        = string
}

variable "accounts" {
  description = "Map of AWS accounts to create under Platform OU"
  type = map(object({
    email = string
    tags  = map(string)
  }))
}

variable "organization_id" {
  description = "root Organization ID"
  type        = string
}

variable "ou_tags" {
  description = "Tags for the Platform Organizational Unit"
  type        = map(string)
  default     = { "Type" = "migration_architrave" }
}

variable "aws_root_arn" {
  description = "ARN of the AWS root account"
  type        = string
}
