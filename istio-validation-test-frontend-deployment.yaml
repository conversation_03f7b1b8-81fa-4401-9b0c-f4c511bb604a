apiVersion: apps/v1
kind: Deployment
metadata:
  name: istio-validation-test-frontend
  namespace: tenant-istio-validation-test
  labels:
    app: istio-validation-test-frontend
    managed-by: advanced-tenant-onboard
spec:
  replicas: 1
  selector:
    matchLabels:
      app: istio-validation-test-frontend
  template:
    metadata:
      labels:
        app: istio-validation-test-frontend
    spec:
      containers:
      - name: frontend
        image: nginx:1.21.6-alpine
        ports:
        - containerPort: 80
          name: http
        env:
        - name: TENANT_ID
          value: "istio-validation-test"
        - name: DOMAIN
          value: "architrave-assets.com"
        - name: ENVIRONMENT
          value: "production"
        - name: LANGUAGE
          value: "en"
        volumeMounts:
        - name: nginx-config
          mountPath: /etc/nginx/conf.d/default.conf
          subPath: default.conf
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "200m"
        livenessProbe:
          httpGet:
            path: /
            port: 80
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /
            port: 80
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: nginx-config
        configMap:
          name: istio-validation-test-frontend-config
