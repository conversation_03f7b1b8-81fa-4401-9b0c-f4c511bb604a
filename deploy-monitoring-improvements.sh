#!/bin/bash

# Colors for output
CYAN='\033[0;36m'
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

echo -e "${CYAN}Starting monitoring improvements deployment...${NC}"

# Create monitoring namespace if it doesn't exist
echo -e "${YELLOW}Creating monitoring namespace...${NC}"
kubectl create namespace monitoring --dry-run=client -o yaml | kubectl apply -f -

# Apply custom metrics
echo -e "${YELLOW}Deploying custom metrics...${NC}"
kubectl apply -f monitoring/custom-metrics.yaml

# Apply enhanced metrics
echo -e "${YELLOW}Deploying enhanced metrics...${NC}"
kubectl apply -f monitoring/enhanced-metrics.yaml

# Apply enhanced alerts
echo -e "${YELLOW}Deploying enhanced alerts...${NC}"
kubectl apply -f monitoring/enhanced-alerts.yaml

# Apply performance dashboards
echo -e "${YELLOW}Deploying performance dashboards...${NC}"
kubectl apply -f monitoring/performance-dashboards.yaml

# Apply tenant dashboards
echo -e "${YELLOW}Deploying tenant dashboards...${NC}"
kubectl apply -f monitoring/tenant-dashboards.yaml

# Create Elasticsearch credentials secret
echo -e "${YELLOW}Creating Elasticsearch credentials...${NC}"
kubectl create secret generic elasticsearch-credentials \
  --from-literal=password=changeme \
  -n monitoring \
  --dry-run=client -o yaml | kubectl apply -f -

# Apply enhanced log aggregation
echo -e "${YELLOW}Deploying enhanced log aggregation...${NC}"
kubectl apply -f monitoring/enhanced-log-aggregation.yaml

# Wait for deployments
echo -e "${YELLOW}Waiting for deployments to be ready...${NC}"
kubectl rollout status daemonset/enhanced-fluentd -n monitoring

# Verify deployments
echo -e "${YELLOW}Verifying deployments...${NC}"
kubectl get pods -n monitoring

# Verify metrics
echo -e "${YELLOW}Verifying metrics...${NC}"
kubectl port-forward svc/prometheus-server 9090:9090 -n monitoring &
sleep 5
curl -s localhost:9090/api/v1/targets | grep -q "custom:tenant_"
if [ $? -eq 0 ]; then
    echo -e "${GREEN}Custom metrics are being collected${NC}"
else
    echo -e "${RED}Custom metrics are not being collected${NC}"
fi
kill %1

# Verify logs
echo -e "${YELLOW}Verifying logs...${NC}"
kubectl port-forward svc/elasticsearch-master 9200:9200 -n monitoring &
sleep 5
curl -s -u elastic:changeme localhost:9200/k8s-*/_count | grep -q "count"
if [ $? -eq 0 ]; then
    echo -e "${GREEN}Logs are being collected${NC}"
else
    echo -e "${RED}Logs are not being collected${NC}"
fi
kill %1

# Verify dashboards
echo -e "${YELLOW}Verifying dashboards...${NC}"
kubectl port-forward svc/grafana 3000:3000 -n monitoring &
sleep 5
curl -s -u admin:admin localhost:3000/api/dashboards | grep -q "tenant"
if [ $? -eq 0 ]; then
    echo -e "${GREEN}Dashboards are available${NC}"
else
    echo -e "${RED}Dashboards are not available${NC}"
fi
kill %1

echo -e "${GREEN}Monitoring improvements deployment completed!${NC}"
echo -e "${CYAN}Next steps:${NC}"
echo -e "1. Access Grafana dashboards at: http://localhost:3000"
echo -e "   - Tenant Overview: /d/tenant-overview"
echo -e "   - Tenant Performance: /d/tenant-performance"
echo -e "2. Check Prometheus alerts at: http://localhost:9090/alerts"
echo -e "3. View logs in Elasticsearch at: http://localhost:9200"
echo -e "   - All logs: /k8s-*/_search"
echo -e "   - Error logs: /k8s-errors-*/_search"
echo -e "4. Monitor custom metrics at: http://localhost:9090/metrics"
echo -e "   - Tenant metrics: custom:tenant_*" 