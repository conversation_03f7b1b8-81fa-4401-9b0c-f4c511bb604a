<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Infrastructure Cost Dashboard - development</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }
        .cost-summary {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin-bottom: 30px;
        }
        .cost-card {
            flex: 1;
            min-width: 200px;
            padding: 15px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .cost-card.primary {
            background-color: #e3f2fd;
            border-left: 4px solid #2196f3;
        }
        .cost-card h3 {
            margin-top: 0;
            color: #555;
        }
        .cost-card .value {
            font-size: 24px;
            font-weight: bold;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
        }
        th, td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #f8f8f8;
        }
        tr:hover {
            background-color: #f5f5f5;
        }
        .chart-container {
            margin-bottom: 30px;
            height: 400px;
        }
        .recommendations {
            background-color: #fff8e1;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #ffc107;
        }
        .recommendations h2 {
            margin-top: 0;
            color: #555;
        }
        .footer {
            margin-top: 30px;
            text-align: center;
            color: #777;
            font-size: 12px;
        }
    </style>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Infrastructure Cost Dashboard</h1>
            <div>
                <strong>Environment:</strong> development<br>
                <strong>Generated:</strong> 2025-04-19 16:37:05<br>
                <strong>Report Date:</strong> 2025-04-19 16:37:05
            </div>
        </div>

        <div class="cost-summary">
            <div class="cost-card primary">
                <h3>Estimated Monthly Cost</h3>
                <div class="value">$$598.45</div>
            </div>
            <div class="cost-card">
                <h3>Daily Cost</h3>
                <div class="value">$$(echo "scale=2; 598.45 / 30" | bc)</div>
            </div>
            <div class="cost-card">
                <h3>Hourly Cost</h3>
                <div class="value">$$(echo "scale=2; 598.45 / 30 / 24" | bc)</div>
            </div>
        </div>

        <h2>Cost Breakdown by Resource Type</h2>
        <div class="chart-container">
            <canvas id="resourceTypeChart"></canvas>
        </div>

        <h2>Top 10 Most Expensive Resources</h2>
        <table>
            <thead>
                <tr>
                    <th>Resource</th>
                    <th>Monthly Cost</th>
                </tr>
            </thead>
            <tbody>
<tr><td>aws_instance.eks_node</td><td>$94.90</td></tr>
<tr><td>aws_instance.bastion</td><td>$87.60</td></tr>
<tr><td>aws_db_instance.main</td><td>$306.60</td></tr>
<tr><td>aws_s3_bucket.data</td><td>$3.65</td></tr>
<tr><td>aws_s3_bucket.logs</td><td>$2.92</td></tr>
<tr><td>aws_nat_gateway.main</td><td>$102.05</td></tr>
<tr><td>aws_s3_bucket.backups</td><td>$0.73</td></tr>
            </tbody>
        </table>

        <div class="recommendations">
            <h2>Cost-Saving Recommendations</h2>
            <ul>
<li>Consider rightsizing EC2 instances and RDS instances based on actual usage patterns</li>
<li>Consider using NAT instances instead of NAT gateways for development environments</li>
                <li>Use Auto Scaling Groups to automatically scale resources based on demand</li>
                <li>Implement instance scheduling to turn off development resources during non-working hours</li>
                <li>Use Spot Instances for non-critical workloads</li>
                <li>Enable cost allocation tags to track costs by project or team</li>
                <li>Regularly review and delete unused resources (EBS volumes, EIPs, etc.)</li>
            </ul>
        </div>

        <div class="footer">
            <p>Generated by Infracost and Cost Dashboard Generator</p>
        </div>
    </div>

    <script>
        // Extract data for charts
        const resourceTypeData = {
            labels: ["aws_instance","aws_db_instance","aws_s3_bucket","aws_nat_gateway"],
            values: ["182.50","306.60","7.30","102.05"]
        };

        // Create resource type chart
        const resourceTypeCtx = document.getElementById('resourceTypeChart').getContext('2d');
        new Chart(resourceTypeCtx, {
            type: 'bar',
            data: {
                labels: resourceTypeData.labels,
                datasets: [{
                    label: 'Monthly Cost ($)',
                    data: resourceTypeData.values,
                    backgroundColor: 'rgba(54, 162, 235, 0.5)',
                    borderColor: 'rgba(54, 162, 235, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Monthly Cost ($)'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'Resource Type'
                        }
                    }
                }
            }
        });
    </script>
</body>
</html>
