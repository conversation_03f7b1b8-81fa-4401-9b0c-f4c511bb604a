variable "aws_profile" {
  description = "AWS profile to create resources"
  type        = string
}

variable "region" {
  description = "The AWS region to create resources in"
  type        = string
}

variable "organization_id" {
  description = "root Organization ID"
  type        = string
}

variable "aws_account_id" {
  description = "AWS Account ID"
  type        = string
}

variable "aws_ou_name" {
  type        = string
  description = "Account OU will be prefixed to the account name, to make it distinguishable in AWS' Login Screen"
}

variable "aws_account_name" {
  type        = string
  description = "Account name prefix to role. So its distinguishable in AWS' Login Screen"
  default     = ""
}