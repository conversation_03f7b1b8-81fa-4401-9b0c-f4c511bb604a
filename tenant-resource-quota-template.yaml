apiVersion: v1
kind: ResourceQuota
metadata:
  name: {{TENANT_ID}}-resource-quota
  namespace: tenant-{{TENANT_ID}}
  labels:
    app.kubernetes.io/managed-by: advanced-tenant-onboard
    tenant: {{TENANT_ID}}
spec:
  hard:
    # Compute Resources
    requests.cpu: "2"      # 2 CPU cores
    requests.memory: 4Gi   # 4GB RAM
    limits.cpu: "4"        # Max 4 CPU cores
    limits.memory: 8Gi     # Max 8GB RAM
    
    # Storage Resources
    requests.storage: 20Gi # 20GB storage
    persistentvolumeclaims: "5" # Max 5 PVCs
    
    # Object Counts
    pods: "20"             # Max 20 pods
    services: "10"         # Max 10 services
    secrets: "20"          # Max 20 secrets
    configmaps: "20"       # Max 20 configmaps
    
    # Network Resources
    services.loadbalancers: "0"  # No LoadBalancers (force shared gateway)
    services.nodeports: "0"      # No NodePorts
---
apiVersion: v1
kind: LimitRange
metadata:
  name: {{TENANT_ID}}-limit-range
  namespace: tenant-{{TENANT_ID}}
  labels:
    app.kubernetes.io/managed-by: advanced-tenant-onboard
    tenant: {{TENANT_ID}}
spec:
  limits:
  # Pod limits
  - type: Pod
    max:
      cpu: "2"
      memory: 4Gi
    min:
      cpu: 100m
      memory: 128Mi
  
  # Container limits
  - type: Container
    default:
      cpu: 500m
      memory: 512Mi
    defaultRequest:
      cpu: 100m
      memory: 128Mi
    max:
      cpu: "1"
      memory: 2Gi
    min:
      cpu: 50m
      memory: 64Mi
  
  # PVC limits
  - type: PersistentVolumeClaim
    max:
      storage: 10Gi
    min:
      storage: 1Gi
