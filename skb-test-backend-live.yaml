apiVersion: apps/v1
kind: Deployment
metadata:
  annotations:
    deployment.kubernetes.io/revision: "1"
    kubectl.kubernetes.io/last-applied-configuration: |
      {"apiVersion":"apps/v1","kind":"Deployment","metadata":{"annotations":{"deployment.kubernetes.io/revision":"4"},"creationTimestamp":"2025-07-16T14:31:18Z","generation":6,"labels":{"app":"skb-test-backend","component":"backend","tenant":"skb-test"},"name":"skb-test-backend","namespace":"tenant-skb-test","resourceVersion":"806673","uid":"c7411669-8727-49c6-8c86-b514155cc13b"},"spec":{"progressDeadlineSeconds":600,"replicas":0,"revisionHistoryLimit":10,"selector":{"matchLabels":{"app":"skb-test-backend","component":"backend","tenant":"skb-test"}},"strategy":{"rollingUpdate":{"maxSurge":"25%","maxUnavailable":"25%"},"type":"RollingUpdate"},"template":{"metadata":{"annotations":{"kubectl.kubernetes.io/restartedAt":"2025-07-16T16:34:05+02:00"},"creationTimestamp":null,"labels":{"app":"skb-test-backend","component":"backend","tenant":"skb-test"}},"spec":{"containers":[{"env":[{"name":"AWS_REGION","value":"eu-central-1"},{"name":"AWS_DEFAULT_REGION","value":"eu-central-1"},{"name":"TENANT_ID","value":"skb-test"},{"name":"DB_HOST","value":"production-aurora-serverless.cluster-cpmagwki2kv8.eu-central-1.rds.amazonaws.com"},{"name":"DB_PORT","value":"3306"},{"name":"DB_NAME","value":"architrave"},{"name":"DB_USER","valueFrom":{"secretKeyRef":{"key":"DB_USER","name":"skb-test-db-secret"}}},{"name":"DB_PASSWORD","valueFrom":{"secretKeyRef":{"key":"DB_PASSWORD","name":"skb-test-db-secret"}}},{"name":"DB_SSL","valueFrom":{"secretKeyRef":{"key":"DB_SSL","name":"skb-test-db-secret"}}},{"name":"DB_SSL_CA","valueFrom":{"secretKeyRef":{"key":"DB_SSL_CA","name":"skb-test-db-secret"}}},{"name":"DB_SSL_VERIFY","valueFrom":{"secretKeyRef":{"key":"DB_SSL_VERIFY","name":"skb-test-db-secret"}}},{"name":"RABBITMQ_URL","value":"amqp://guest:guest@skb-test-rabbitmq-service:5672/"},{"name":"ENVIRONMENT","value":"production"},{"name":"LANGUAGE","value":"en"},{"name":"AWS_SHARED_CREDENTIALS_FILE","value":"/tmp/.aws/credentials"},{"name":"AWS_CONFIG_FILE","value":"/tmp/.aws/config"}],"image":"545009857703.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:2.0.56-test","imagePullPolicy":"IfNotPresent","livenessProbe":{"failureThreshold":3,"initialDelaySeconds":60,"periodSeconds":30,"successThreshold":1,"tcpSocket":{"port":9000},"timeoutSeconds":1},"name":"backend","ports":[{"containerPort":9000,"protocol":"TCP"}],"readinessProbe":{"failureThreshold":3,"initialDelaySeconds":30,"periodSeconds":30,"successThreshold":1,"tcpSocket":{"port":9000},"timeoutSeconds":1},"resources":{"limits":{"cpu":"500m","memory":"512Mi"},"requests":{"cpu":"100m","memory":"256Mi"}},"terminationMessagePath":"/dev/termination-log","terminationMessagePolicy":"File","volumeMounts":[{"mountPath":"/storage/ArchAssets","name":"s3-archassets"},{"mountPath":"/tmp","name":"ssl-certs"}]},{"image":"nginx:1.21-alpine","imagePullPolicy":"IfNotPresent","livenessProbe":{"failureThreshold":5,"httpGet":{"httpHeaders":[{"name":"User-Agent","value":"k8s-liveness-probe"}],"path":"/api/health","port":8080,"scheme":"HTTP"},"initialDelaySeconds":90,"periodSeconds":60,"successThreshold":1,"timeoutSeconds":10},"name":"nginx","ports":[{"containerPort":8080,"protocol":"TCP"}],"readinessProbe":{"failureThreshold":10,"httpGet":{"httpHeaders":[{"name":"User-Agent","value":"k8s-readiness-probe"}],"path":"/api/health","port":8080,"scheme":"HTTP"},"initialDelaySeconds":60,"periodSeconds":30,"successThreshold":1,"timeoutSeconds":5},"resources":{"limits":{"cpu":"200m","memory":"128Mi"},"requests":{"cpu":"50m","memory":"64Mi"}},"terminationMessagePath":"/dev/termination-log","terminationMessagePolicy":"File","volumeMounts":[{"mountPath":"/etc/nginx/conf.d","name":"nginx-config"}]}],"dnsPolicy":"ClusterFirst","initContainers":[{"command":["sh","-c","echo \"Syncing S3 content for tenant skb-test...\"\naws s3 sync s3://architravetestdb/skb-test/ /storage/ArchAssets/ --delete || echo \"S3 sync completed (some files may not exist yet)\"\necho \"S3 content sync completed\"\n"],"env":[{"name":"AWS_REGION","value":"eu-central-1"},{"name":"AWS_DEFAULT_REGION","value":"eu-central-1"}],"image":"amazon/aws-cli:latest","name":"s3-sync","volumeMounts":[{"mountPath":"/storage/ArchAssets","name":"s3-archassets"}]}],"restartPolicy":"Always","schedulerName":"default-scheduler","securityContext":{},"serviceAccount":"skb-test-s3-service-account","serviceAccountName":"skb-test-s3-service-account","terminationGracePeriodSeconds":30,"volumes":[{"name":"s3-archassets","persistentVolumeClaim":{"claimName":"s3-pvc"}},{"emptyDir":{},"name":"ssl-certs"},{"emptyDir":{},"name":"nginx-config"}]}}},"status":{"conditions":[{"lastTransitionTime":"2025-07-16T14:31:18Z","lastUpdateTime":"2025-07-16T14:35:32Z","message":"ReplicaSet \"skb-test-backend-5fff5c5d6c\" has successfully progressed.","reason":"NewReplicaSetAvailable","status":"True","type":"Progressing"},{"lastTransitionTime":"2025-07-16T14:38:38Z","lastUpdateTime":"2025-07-16T14:38:38Z","message":"Deployment has minimum availability.","reason":"MinimumReplicasAvailable","status":"True","type":"Available"}],"observedGeneration":6}}
  creationTimestamp: "2025-07-16T20:17:44Z"
  generation: 2
  labels:
    app: skb-test-backend
    component: backend
    tenant: skb-test
  name: skb-test-backend
  namespace: tenant-skb-test
  resourceVersion: "894856"
  uid: fded75c5-9ff9-43a8-87b5-016e74e46f4c
spec:
  progressDeadlineSeconds: 600
  replicas: 2
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      app: skb-test-backend
      component: backend
      tenant: skb-test
  strategy:
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
    type: RollingUpdate
  template:
    metadata:
      annotations:
        kubectl.kubernetes.io/restartedAt: "2025-07-16T16:34:05+02:00"
      creationTimestamp: null
      labels:
        app: skb-test-backend
        component: backend
        tenant: skb-test
    spec:
      containers:
      - env:
        - name: AWS_REGION
          value: eu-central-1
        - name: AWS_DEFAULT_REGION
          value: eu-central-1
        - name: TENANT_ID
          value: skb-test
        - name: DB_HOST
          value: production-aurora-serverless.cluster-cpmagwki2kv8.eu-central-1.rds.amazonaws.com
        - name: DB_PORT
          value: "3306"
        - name: DB_NAME
          value: architrave
        - name: DB_USER
          valueFrom:
            secretKeyRef:
              key: DB_USER
              name: skb-test-db-secret
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              key: DB_PASSWORD
              name: skb-test-db-secret
        - name: DB_SSL
          valueFrom:
            secretKeyRef:
              key: DB_SSL
              name: skb-test-db-secret
        - name: DB_SSL_CA
          valueFrom:
            secretKeyRef:
              key: DB_SSL_CA
              name: skb-test-db-secret
        - name: DB_SSL_VERIFY
          valueFrom:
            secretKeyRef:
              key: DB_SSL_VERIFY
              name: skb-test-db-secret
        - name: RABBITMQ_URL
          value: amqp://guest:guest@skb-test-rabbitmq-service:5672/
        - name: ENVIRONMENT
          value: production
        - name: LANGUAGE
          value: en
        - name: AWS_SHARED_CREDENTIALS_FILE
          value: /tmp/.aws/credentials
        - name: AWS_CONFIG_FILE
          value: /tmp/.aws/config
        image: 545009857703.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:2.0.56-test
        imagePullPolicy: IfNotPresent
        livenessProbe:
          failureThreshold: 3
          initialDelaySeconds: 60
          periodSeconds: 30
          successThreshold: 1
          tcpSocket:
            port: 9000
          timeoutSeconds: 1
        name: backend
        ports:
        - containerPort: 9000
          protocol: TCP
        readinessProbe:
          failureThreshold: 3
          initialDelaySeconds: 30
          periodSeconds: 30
          successThreshold: 1
          tcpSocket:
            port: 9000
          timeoutSeconds: 1
        resources:
          limits:
            cpu: 500m
            memory: 512Mi
          requests:
            cpu: 100m
            memory: 256Mi
        terminationMessagePath: /dev/termination-log
        terminationMessagePolicy: File
        volumeMounts:
        - mountPath: /storage/ArchAssets
          name: s3-archassets
        - mountPath: /tmp
          name: ssl-certs
      - image: nginx:1.21-alpine
        imagePullPolicy: IfNotPresent
        livenessProbe:
          failureThreshold: 5
          httpGet:
            httpHeaders:
            - name: User-Agent
              value: k8s-liveness-probe
            path: /api/health
            port: 8080
            scheme: HTTP
          initialDelaySeconds: 90
          periodSeconds: 60
          successThreshold: 1
          timeoutSeconds: 10
        name: nginx
        ports:
        - containerPort: 8080
          protocol: TCP
        readinessProbe:
          failureThreshold: 10
          httpGet:
            httpHeaders:
            - name: User-Agent
              value: k8s-readiness-probe
            path: /api/health
            port: 8080
            scheme: HTTP
          initialDelaySeconds: 60
          periodSeconds: 30
          successThreshold: 1
          timeoutSeconds: 5
        resources:
          limits:
            cpu: 200m
            memory: 128Mi
          requests:
            cpu: 50m
            memory: 64Mi
        terminationMessagePath: /dev/termination-log
        terminationMessagePolicy: File
        volumeMounts:
        - mountPath: /etc/nginx/conf.d
          name: nginx-config
      dnsPolicy: ClusterFirst
      initContainers:
      - command:
        - sh
        - -c
        - |
          echo "Syncing S3 content for tenant skb-test..."
          aws s3 sync s3://architravetestdb/skb-test/ /storage/ArchAssets/ --delete || echo "S3 sync completed (some files may not exist yet)"
          echo "S3 content sync completed"
        env:
        - name: AWS_REGION
          value: eu-central-1
        - name: AWS_DEFAULT_REGION
          value: eu-central-1
        image: amazon/aws-cli:latest
        imagePullPolicy: Always
        name: s3-sync
        resources: {}
        terminationMessagePath: /dev/termination-log
        terminationMessagePolicy: File
        volumeMounts:
        - mountPath: /storage/ArchAssets
          name: s3-archassets
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext: {}
      serviceAccount: skb-test-s3-service-account
      serviceAccountName: skb-test-s3-service-account
      terminationGracePeriodSeconds: 30
      volumes:
      - name: s3-archassets
        persistentVolumeClaim:
          claimName: s3-pvc
      - emptyDir: {}
        name: ssl-certs
      - emptyDir: {}
        name: nginx-config
status:
  conditions:
  - lastTransitionTime: "2025-07-16T20:17:44Z"
    lastUpdateTime: "2025-07-16T20:17:44Z"
    message: ReplicaSet "skb-test-backend-6ddcc486bd" has successfully progressed.
    reason: NewReplicaSetAvailable
    status: "True"
    type: Progressing
  - lastTransitionTime: "2025-07-16T20:17:51Z"
    lastUpdateTime: "2025-07-16T20:17:51Z"
    message: Deployment does not have minimum availability.
    reason: MinimumReplicasUnavailable
    status: "False"
    type: Available
  observedGeneration: 2
  replicas: 2
  unavailableReplicas: 2
  updatedReplicas: 2
