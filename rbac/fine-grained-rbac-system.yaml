---
# Fine-grained RBAC with Tenant-Specific Permissions
apiVersion: v1
kind: Namespace
metadata:
  name: rbac-system
  labels:
    pod-security.kubernetes.io/enforce: restricted
    pod-security.kubernetes.io/audit: restricted
    pod-security.kubernetes.io/warn: restricted
---
# RBAC Manager for Tenant-Specific Permissions
apiVersion: apps/v1
kind: Deployment
metadata:
  name: rbac-manager
  namespace: rbac-system
  labels:
    app: rbac-manager
spec:
  replicas: 2
  selector:
    matchLabels:
      app: rbac-manager
  template:
    metadata:
      labels:
        app: rbac-manager
    spec:
      serviceAccountName: rbac-manager
      securityContext:
        runAsNonRoot: true
        runAsUser: 65534
        runAsGroup: 65534
        fsGroup: 65534
        seccompProfile:
          type: RuntimeDefault
      containers:
      - name: rbac-manager
        image: python:3.11-alpine
        command: ["/bin/sh", "-c"]
        args:
        - |
          pip install flask kubernetes pyjwt cryptography
          cat > /app/rbac_manager.py << 'EOF'
          #!/usr/bin/env python3
          import json
          import jwt
          import time
          from datetime import datetime, timedelta
          from flask import Flask, request, jsonify
          from kubernetes import client, config
          
          app = Flask(__name__)
          
          # Load Kubernetes config
          try:
              config.load_incluster_config()
          except:
              config.load_kube_config()
          
          k8s_client = client.CoreV1Api()
          rbac_client = client.RbacAuthorizationV1Api()
          
          # JWT secret for token signing
          JWT_SECRET = "your-jwt-secret-key"  # In production, use proper secret management
          
          # Permission definitions
          PERMISSION_DEFINITIONS = {
              "tenant-admin": {
                  "description": "Full administrative access to tenant resources",
                  "permissions": [
                      "pods:*", "services:*", "deployments:*", "configmaps:*",
                      "secrets:read", "ingress:*", "networkpolicies:*",
                      "monitoring:read", "logs:read", "metrics:read"
                  ],
                  "scope": "tenant"
              },
              "tenant-developer": {
                  "description": "Development access to tenant resources",
                  "permissions": [
                      "pods:read", "pods:create", "pods:update", "pods:delete",
                      "services:read", "services:create", "services:update",
                      "deployments:read", "deployments:create", "deployments:update",
                      "configmaps:read", "configmaps:create", "configmaps:update",
                      "logs:read", "metrics:read"
                  ],
                  "scope": "tenant"
              },
              "tenant-viewer": {
                  "description": "Read-only access to tenant resources",
                  "permissions": [
                      "pods:read", "services:read", "deployments:read",
                      "configmaps:read", "logs:read", "metrics:read"
                  ],
                  "scope": "tenant"
              },
              "tenant-operator": {
                  "description": "Operational access for monitoring and troubleshooting",
                  "permissions": [
                      "pods:read", "pods:logs", "services:read", "deployments:read",
                      "monitoring:read", "logs:read", "metrics:read", "events:read"
                  ],
                  "scope": "tenant"
              },
              "platform-admin": {
                  "description": "Full platform administrative access",
                  "permissions": ["*:*"],
                  "scope": "platform"
              },
              "platform-operator": {
                  "description": "Platform operational access",
                  "permissions": [
                      "namespaces:read", "nodes:read", "monitoring:read",
                      "logs:read", "metrics:read", "alerts:read"
                  ],
                  "scope": "platform"
              }
          }
          
          @app.route('/api/rbac/roles', methods=['GET'])
          def list_roles():
              """List all available roles and their permissions"""
              return jsonify({
                  "roles": PERMISSION_DEFINITIONS,
                  "count": len(PERMISSION_DEFINITIONS)
              })
          
          @app.route('/api/rbac/users/<tenant_id>', methods=['GET'])
          def list_tenant_users(tenant_id):
              """List users and their roles for a specific tenant"""
              try:
                  # Get role bindings for the tenant namespace
                  role_bindings = rbac_client.list_namespaced_role_binding(
                      namespace=f"tenant-{tenant_id}"
                  )
                  
                  users = []
                  for rb in role_bindings.items:
                      if rb.metadata.labels and rb.metadata.labels.get('managed-by') == 'rbac-manager':
                          for subject in rb.subjects or []:
                              if subject.kind == 'User':
                                  users.append({
                                      "username": subject.name,
                                      "role": rb.role_ref.name,
                                      "permissions": PERMISSION_DEFINITIONS.get(rb.role_ref.name, {}).get('permissions', []),
                                      "created_at": rb.metadata.creation_timestamp.isoformat() if rb.metadata.creation_timestamp else None
                                  })
                  
                  return jsonify({
                      "tenant_id": tenant_id,
                      "users": users,
                      "count": len(users)
                  })
              
              except Exception as e:
                  return jsonify({"error": str(e)}), 500
          
          @app.route('/api/rbac/users/<tenant_id>/<username>', methods=['POST'])
          def assign_user_role(tenant_id, username):
              """Assign a role to a user for a specific tenant"""
              try:
                  data = request.get_json()
                  role_name = data.get('role')
                  
                  if role_name not in PERMISSION_DEFINITIONS:
                      return jsonify({"error": "Invalid role"}), 400
                  
                  role_def = PERMISSION_DEFINITIONS[role_name]
                  
                  # Create Role if it doesn't exist
                  role = create_tenant_role(tenant_id, role_name, role_def)
                  
                  # Create RoleBinding
                  role_binding = create_role_binding(tenant_id, username, role_name)
                  
                  # Generate JWT token for the user
                  token = generate_user_token(username, tenant_id, role_name)
                  
                  return jsonify({
                      "status": "success",
                      "tenant_id": tenant_id,
                      "username": username,
                      "role": role_name,
                      "permissions": role_def['permissions'],
                      "token": token
                  })
              
              except Exception as e:
                  return jsonify({"error": str(e)}), 500
          
          @app.route('/api/rbac/users/<tenant_id>/<username>', methods=['DELETE'])
          def remove_user_role(tenant_id, username):
              """Remove a user's role from a specific tenant"""
              try:
                  # Delete RoleBinding
                  rbac_client.delete_namespaced_role_binding(
                      name=f"user-{username}-binding",
                      namespace=f"tenant-{tenant_id}"
                  )
                  
                  return jsonify({
                      "status": "success",
                      "tenant_id": tenant_id,
                      "username": username,
                      "action": "removed"
                  })
              
              except Exception as e:
                  return jsonify({"error": str(e)}), 500
          
          @app.route('/api/rbac/validate', methods=['POST'])
          def validate_permission():
              """Validate if a user has permission for a specific action"""
              try:
                  data = request.get_json()
                  token = data.get('token')
                  resource = data.get('resource')  # e.g., "pods"
                  action = data.get('action')      # e.g., "read", "create", "delete"
                  tenant_id = data.get('tenant_id')
                  
                  # Decode and validate JWT token
                  try:
                      payload = jwt.decode(token, JWT_SECRET, algorithms=['HS256'])
                      username = payload['username']
                      user_tenant_id = payload['tenant_id']
                      role = payload['role']
                  except jwt.InvalidTokenError:
                      return jsonify({"error": "Invalid token"}), 401
                  
                  # Check if user is accessing the correct tenant
                  if tenant_id and user_tenant_id != tenant_id:
                      return jsonify({"error": "Access denied: wrong tenant"}), 403
                  
                  # Check permissions
                  role_def = PERMISSION_DEFINITIONS.get(role, {})
                  permissions = role_def.get('permissions', [])
                  
                  # Check if user has the required permission
                  required_permission = f"{resource}:{action}"
                  has_permission = (
                      "*:*" in permissions or
                      f"{resource}:*" in permissions or
                      required_permission in permissions
                  )
                  
                  return jsonify({
                      "username": username,
                      "tenant_id": user_tenant_id,
                      "role": role,
                      "resource": resource,
                      "action": action,
                      "has_permission": has_permission,
                      "checked_at": datetime.utcnow().isoformat()
                  })
              
              except Exception as e:
                  return jsonify({"error": str(e)}), 500
          
          @app.route('/api/rbac/audit/<tenant_id>', methods=['GET'])
          def get_audit_log(tenant_id):
              """Get audit log for tenant RBAC activities"""
              try:
                  # In a real implementation, this would query an audit database
                  # For now, we'll return recent events from Kubernetes
                  events = k8s_client.list_namespaced_event(
                      namespace=f"tenant-{tenant_id}",
                      field_selector="reason=RoleBindingCreated,reason=RoleBindingDeleted"
                  )
                  
                  audit_entries = []
                  for event in events.items:
                      audit_entries.append({
                          "timestamp": event.first_timestamp.isoformat() if event.first_timestamp else None,
                          "action": event.reason,
                          "object": event.involved_object.name if event.involved_object else None,
                          "message": event.message,
                          "source": event.source.component if event.source else None
                      })
                  
                  return jsonify({
                      "tenant_id": tenant_id,
                      "audit_entries": audit_entries,
                      "count": len(audit_entries)
                  })
              
              except Exception as e:
                  return jsonify({"error": str(e)}), 500
          
          def create_tenant_role(tenant_id, role_name, role_def):
              """Create a Kubernetes Role for the tenant"""
              rules = []
              
              for permission in role_def['permissions']:
                  if permission == "*:*":
                      rules.append(client.V1PolicyRule(
                          api_groups=["*"],
                          resources=["*"],
                          verbs=["*"]
                      ))
                  else:
                      resource, verb = permission.split(':')
                      verbs = ["*"] if verb == "*" else [verb]
                      
                      # Map resource types to API groups
                      api_groups = get_api_groups_for_resource(resource)
                      
                      rules.append(client.V1PolicyRule(
                          api_groups=api_groups,
                          resources=[resource] if resource != "*" else ["*"],
                          verbs=verbs
                      ))
              
              role = client.V1Role(
                  metadata=client.V1ObjectMeta(
                      name=role_name,
                      namespace=f"tenant-{tenant_id}",
                      labels={
                          "managed-by": "rbac-manager",
                          "tenant-id": tenant_id,
                          "role-type": role_name
                      }
                  ),
                  rules=rules
              )
              
              try:
                  rbac_client.create_namespaced_role(
                      namespace=f"tenant-{tenant_id}",
                      body=role
                  )
              except client.ApiException as e:
                  if e.status != 409:  # Ignore if role already exists
                      raise
              
              return role
          
          def create_role_binding(tenant_id, username, role_name):
              """Create a RoleBinding for the user"""
              role_binding = client.V1RoleBinding(
                  metadata=client.V1ObjectMeta(
                      name=f"user-{username}-binding",
                      namespace=f"tenant-{tenant_id}",
                      labels={
                          "managed-by": "rbac-manager",
                          "tenant-id": tenant_id,
                          "username": username
                      }
                  ),
                  subjects=[client.V1Subject(
                      kind="User",
                      name=username,
                      api_group="rbac.authorization.k8s.io"
                  )],
                  role_ref=client.V1RoleRef(
                      kind="Role",
                      name=role_name,
                      api_group="rbac.authorization.k8s.io"
                  )
              )
              
              try:
                  rbac_client.create_namespaced_role_binding(
                      namespace=f"tenant-{tenant_id}",
                      body=role_binding
                  )
              except client.ApiException as e:
                  if e.status == 409:  # Role binding already exists, update it
                      rbac_client.patch_namespaced_role_binding(
                          name=f"user-{username}-binding",
                          namespace=f"tenant-{tenant_id}",
                          body=role_binding
                      )
                  else:
                      raise
              
              return role_binding
          
          def generate_user_token(username, tenant_id, role):
              """Generate JWT token for user authentication"""
              payload = {
                  "username": username,
                  "tenant_id": tenant_id,
                  "role": role,
                  "iat": datetime.utcnow(),
                  "exp": datetime.utcnow() + timedelta(hours=24)  # 24 hour expiry
              }
              
              return jwt.encode(payload, JWT_SECRET, algorithm='HS256')
          
          def get_api_groups_for_resource(resource):
              """Map resource types to their API groups"""
              api_group_mapping = {
                  "pods": [""],
                  "services": [""],
                  "configmaps": [""],
                  "secrets": [""],
                  "events": [""],
                  "deployments": ["apps"],
                  "replicasets": ["apps"],
                  "statefulsets": ["apps"],
                  "ingress": ["networking.k8s.io"],
                  "networkpolicies": ["networking.k8s.io"],
                  "monitoring": ["monitoring.coreos.com"],
                  "logs": [""],
                  "metrics": ["metrics.k8s.io"]
              }
              
              return api_group_mapping.get(resource, [""])
          
          @app.route('/health', methods=['GET'])
          def health_check():
              return jsonify({"status": "healthy", "timestamp": datetime.utcnow().isoformat()})
          
          if __name__ == '__main__':
              app.run(host='0.0.0.0', port=8080)
          EOF
          
          python /app/rbac_manager.py
        ports:
        - containerPort: 8080
          name: http
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          capabilities:
            drop: ["ALL"]
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "200m"
        volumeMounts:
        - name: tmp
          mountPath: /tmp
        - name: app
          mountPath: /app
        env:
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: rbac-secrets
              key: jwt-secret
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 10
      volumes:
      - name: tmp
        emptyDir: {}
      - name: app
        emptyDir: {}
---
apiVersion: v1
kind: Secret
metadata:
  name: rbac-secrets
  namespace: rbac-system
type: Opaque
data:
  jwt-secret: eW91ci1qd3Qtc2VjcmV0LWtleS1oZXJl  # base64 encoded "your-jwt-secret-key-here"
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: rbac-manager
  namespace: rbac-system
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: rbac-manager
rules:
- apiGroups: [""]
  resources: ["namespaces"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["rbac.authorization.k8s.io"]
  resources: ["roles", "rolebindings"]
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]
- apiGroups: [""]
  resources: ["events"]
  verbs: ["get", "list", "watch"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: rbac-manager
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: rbac-manager
subjects:
- kind: ServiceAccount
  name: rbac-manager
  namespace: rbac-system
---
apiVersion: v1
kind: Service
metadata:
  name: rbac-manager
  namespace: rbac-system
  labels:
    app: rbac-manager
spec:
  ports:
  - port: 8080
    targetPort: 8080
    name: http
  selector:
    app: rbac-manager
