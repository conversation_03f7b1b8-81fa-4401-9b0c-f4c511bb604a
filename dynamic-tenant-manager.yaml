apiVersion: v1
kind: ConfigMap
metadata:
  name: dynamic-tenant-ui
  namespace: tenant-management
data:
  index.html: |
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>🏢 Dynamic Tenant Manager</title>
        <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
        <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
        <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
        <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
        <style>
            * { margin: 0; padding: 0; box-sizing: border-box; }
            body {
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                min-height: 100vh;
                color: #333;
            }
            .container {
                max-width: 1400px;
                margin: 0 auto;
                padding: 20px;
            }
            .header {
                background: rgba(255,255,255,0.95);
                backdrop-filter: blur(10px);
                border-radius: 15px;
                padding: 30px;
                margin-bottom: 30px;
                box-shadow: 0 8px 32px rgba(0,0,0,0.1);
                text-align: center;
            }
            .header h1 {
                font-size: 2.5rem;
                background: linear-gradient(45deg, #667eea, #764ba2);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                margin-bottom: 10px;
            }
            .metrics-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                gap: 20px;
                margin-bottom: 30px;
            }
            .metric-card {
                background: rgba(255,255,255,0.95);
                backdrop-filter: blur(10px);
                border-radius: 15px;
                padding: 25px;
                box-shadow: 0 8px 32px rgba(0,0,0,0.1);
                transition: transform 0.3s ease, box-shadow 0.3s ease;
            }
            .metric-card:hover {
                transform: translateY(-5px);
                box-shadow: 0 12px 40px rgba(0,0,0,0.15);
            }
            .metric-value {
                font-size: 2.5rem;
                font-weight: bold;
                margin-bottom: 10px;
            }
            .metric-label {
                color: #666;
                font-size: 1.1rem;
                margin-bottom: 15px;
            }
            .metric-change {
                font-size: 0.9rem;
                padding: 5px 10px;
                border-radius: 20px;
                display: inline-block;
            }
            .positive { background: #d4edda; color: #155724; }
            .negative { background: #f8d7da; color: #721c24; }
            .neutral { background: #e2e3e5; color: #383d41; }
            .tenants-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
                gap: 20px;
                margin-bottom: 30px;
            }
            .tenant-card {
                background: rgba(255,255,255,0.95);
                backdrop-filter: blur(10px);
                border-radius: 15px;
                padding: 25px;
                box-shadow: 0 8px 32px rgba(0,0,0,0.1);
                transition: transform 0.3s ease;
            }
            .tenant-card:hover {
                transform: translateY(-3px);
            }
            .tenant-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 20px;
            }
            .tenant-name {
                font-size: 1.3rem;
                font-weight: bold;
                color: #333;
            }
            .tier-badge {
                padding: 5px 15px;
                border-radius: 20px;
                font-size: 0.8rem;
                font-weight: bold;
                text-transform: uppercase;
            }
            .tier-basic { background: #ffeaa7; color: #2d3436; }
            .tier-standard { background: #74b9ff; color: white; }
            .tier-premium { background: #fd79a8; color: white; }
            .status-indicator {
                width: 12px;
                height: 12px;
                border-radius: 50%;
                display: inline-block;
                margin-right: 8px;
            }
            .status-active { background: #00b894; }
            .status-inactive { background: #e17055; }
            .status-pending { background: #fdcb6e; }
            .tenant-metrics {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 15px;
                margin-top: 15px;
            }
            .tenant-metric {
                text-align: center;
                padding: 10px;
                background: rgba(0,0,0,0.05);
                border-radius: 8px;
            }
            .tenant-metric-value {
                font-size: 1.5rem;
                font-weight: bold;
                color: #667eea;
            }
            .tenant-metric-label {
                font-size: 0.8rem;
                color: #666;
                margin-top: 5px;
            }
            .charts-section {
                background: rgba(255,255,255,0.95);
                backdrop-filter: blur(10px);
                border-radius: 15px;
                padding: 30px;
                box-shadow: 0 8px 32px rgba(0,0,0,0.1);
                margin-bottom: 30px;
            }
            .charts-grid {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 30px;
            }
            .chart-container {
                position: relative;
                height: 300px;
            }
            .actions-section {
                background: rgba(255,255,255,0.95);
                backdrop-filter: blur(10px);
                border-radius: 15px;
                padding: 30px;
                box-shadow: 0 8px 32px rgba(0,0,0,0.1);
                text-align: center;
            }
            .btn {
                background: linear-gradient(45deg, #667eea, #764ba2);
                color: white;
                border: none;
                padding: 12px 30px;
                border-radius: 25px;
                font-size: 1rem;
                cursor: pointer;
                margin: 0 10px;
                transition: transform 0.3s ease;
            }
            .btn:hover {
                transform: translateY(-2px);
            }
            .loading {
                display: inline-block;
                width: 20px;
                height: 20px;
                border: 3px solid #f3f3f3;
                border-top: 3px solid #667eea;
                border-radius: 50%;
                animation: spin 1s linear infinite;
            }
            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
            .real-time-indicator {
                position: fixed;
                top: 20px;
                right: 20px;
                background: rgba(0,184,148,0.9);
                color: white;
                padding: 10px 20px;
                border-radius: 25px;
                font-size: 0.9rem;
                display: flex;
                align-items: center;
                gap: 8px;
            }
            .pulse {
                width: 8px;
                height: 8px;
                background: white;
                border-radius: 50%;
                animation: pulse 2s infinite;
            }
            @keyframes pulse {
                0% { opacity: 1; }
                50% { opacity: 0.5; }
                100% { opacity: 1; }
            }
        </style>
    </head>
    <body>
        <div id="root"></div>

        <script type="text/babel">
            const { useState, useEffect } = React;

            function TenantManager() {
                const [metrics, setMetrics] = useState({
                    totalTenants: 0,
                    activeTenants: 0,
                    totalCost: 0,
                    avgResponseTime: 0,
                    lastUpdate: new Date()
                });

                const [tenants, setTenants] = useState([]);
                const [loading, setLoading] = useState(true);
                const [charts, setCharts] = useState({});

                // Fetch real-time data from API
                const fetchMetrics = async () => {
                    try {
                        // Fetch tenants data from real API
                        const tenantsResponse = await fetch('/api/tenants');
                        const tenantsData = await tenantsResponse.json();

                        // Fetch summary metrics
                        const metricsResponse = await fetch('/api/metrics/summary');
                        const metricsData = await metricsResponse.json();

                        // Transform tenant data for UI
                        const transformedTenants = tenantsData.tenants.map(tenant => ({
                            id: tenant.id,
                            name: tenant.name,
                            tier: tenant.tier,
                            status: tenant.status,
                            cost: tenant.cost,
                            responseTime: tenant.metrics.responseTime,
                            cpu: tenant.metrics.cpu,
                            memory: tenant.metrics.memory,
                            requests: tenant.metrics.requests,
                            uptime: tenant.metrics.uptime,
                            pods: tenant.pods
                        }));

                        setTenants(transformedTenants);
                        setMetrics({
                            totalTenants: metricsData.totalTenants,
                            activeTenants: metricsData.activeTenants,
                            totalCost: metricsData.totalCost,
                            avgResponseTime: metricsData.avgResponseTime,
                            lastUpdate: new Date()
                        });

                        setLoading(false);
                    } catch (error) {
                        console.error('Error fetching metrics:', error);
                        // Fallback to mock data if API fails
                        const mockTenants = [
                            {
                                id: 'enterprise-corp',
                                name: 'Enterprise Corp',
                                tier: 'premium',
                                status: 'active',
                                cost: 195.50,
                                responseTime: 145,
                                cpu: 65,
                                memory: 72,
                                requests: 1250,
                                uptime: 99.8
                            },
                            {
                                id: 'fast-test',
                                name: 'Fast Test',
                                tier: 'standard',
                                status: 'active',
                                cost: 95.25,
                                responseTime: 220,
                                cpu: 45,
                                memory: 58,
                                requests: 850,
                                uptime: 99.5
                            }
                        ];

                        setTenants(mockTenants);
                        setMetrics({
                            totalTenants: mockTenants.length,
                            activeTenants: mockTenants.filter(t => t.status === 'active').length,
                            totalCost: mockTenants.reduce((sum, t) => sum + t.cost, 0),
                            avgResponseTime: Math.round(mockTenants.reduce((sum, t) => sum + t.responseTime, 0) / mockTenants.length),
                            lastUpdate: new Date()
                        });
                        setLoading(false);
                    }
                };

                // Update charts
                const updateCharts = () => {
                    if (tenants.length === 0) return;

                    // Cost chart
                    const costCtx = document.getElementById('costChart');
                    if (costCtx && !charts.cost) {
                        const costChart = new Chart(costCtx, {
                            type: 'doughnut',
                            data: {
                                labels: tenants.map(t => t.name),
                                datasets: [{
                                    data: tenants.map(t => t.cost),
                                    backgroundColor: [
                                        '#fd79a8', '#74b9ff', '#00b894', '#ffeaa7'
                                    ]
                                }]
                            },
                            options: {
                                responsive: true,
                                maintainAspectRatio: false,
                                plugins: {
                                    title: {
                                        display: true,
                                        text: 'Cost Distribution by Tenant'
                                    }
                                }
                            }
                        });
                        setCharts(prev => ({ ...prev, cost: costChart }));
                    }

                    // Performance chart
                    const perfCtx = document.getElementById('performanceChart');
                    if (perfCtx && !charts.performance) {
                        const perfChart = new Chart(perfCtx, {
                            type: 'bar',
                            data: {
                                labels: tenants.map(t => t.name),
                                datasets: [
                                    {
                                        label: 'CPU %',
                                        data: tenants.map(t => t.cpu),
                                        backgroundColor: '#667eea'
                                    },
                                    {
                                        label: 'Memory %',
                                        data: tenants.map(t => t.memory),
                                        backgroundColor: '#764ba2'
                                    }
                                ]
                            },
                            options: {
                                responsive: true,
                                maintainAspectRatio: false,
                                plugins: {
                                    title: {
                                        display: true,
                                        text: 'Resource Utilization'
                                    }
                                },
                                scales: {
                                    y: {
                                        beginAtZero: true,
                                        max: 100
                                    }
                                }
                            }
                        });
                        setCharts(prev => ({ ...prev, performance: perfChart }));
                    }
                };

                useEffect(() => {
                    fetchMetrics();
                    const interval = setInterval(fetchMetrics, 5000); // Update every 5 seconds
                    return () => clearInterval(interval);
                }, []);

                useEffect(() => {
                    if (tenants.length > 0) {
                        setTimeout(updateCharts, 100); // Small delay to ensure DOM is ready
                    }
                }, [tenants]);

                const getTierClass = (tier) => `tier-${tier}`;
                const getStatusClass = (status) => `status-${status}`;

                return (
                    <div className="container">
                        <div className="real-time-indicator">
                            <div className="pulse"></div>
                            Live Data - Last Update: {metrics.lastUpdate.toLocaleTimeString()}
                        </div>

                        <div className="header">
                            <h1>🏢 Dynamic Tenant Manager</h1>
                            <p>Real-time monitoring and management of multi-tenant infrastructure</p>
                        </div>

                        <div className="metrics-grid">
                            <div className="metric-card">
                                <div className="metric-value" style={{color: '#667eea'}}>
                                    {loading ? <div className="loading"></div> : metrics.totalTenants}
                                </div>
                                <div className="metric-label">Total Tenants</div>
                                <div className="metric-change positive">+2 this month</div>
                            </div>

                            <div className="metric-card">
                                <div className="metric-value" style={{color: '#00b894'}}>
                                    {loading ? <div className="loading"></div> : metrics.activeTenants}
                                </div>
                                <div className="metric-label">Active Tenants</div>
                                <div className="metric-change positive">100% uptime</div>
                            </div>

                            <div className="metric-card">
                                <div className="metric-value" style={{color: '#fd79a8'}}>
                                    {loading ? <div className="loading"></div> : `$${metrics.totalCost.toFixed(2)}`}
                                </div>
                                <div className="metric-label">Monthly Cost</div>
                                <div className="metric-change neutral">-5% vs last month</div>
                            </div>

                            <div className="metric-card">
                                <div className="metric-value" style={{color: '#fdcb6e'}}>
                                    {loading ? <div className="loading"></div> : `${metrics.avgResponseTime}ms`}
                                </div>
                                <div className="metric-label">Avg Response Time</div>
                                <div className="metric-change positive">-15ms improvement</div>
                            </div>
                        </div>

                        <div className="tenants-grid">
                            {tenants.map(tenant => (
                                <div key={tenant.id} className="tenant-card">
                                    <div className="tenant-header">
                                        <div>
                                            <div className="tenant-name">
                                                <span className={getStatusClass(tenant.status)}></span>
                                                {tenant.name}
                                            </div>
                                        </div>
                                        <div className={`tier-badge ${getTierClass(tenant.tier)}`}>
                                            {tenant.tier}
                                        </div>
                                    </div>

                                    <div className="tenant-metrics">
                                        <div className="tenant-metric">
                                            <div className="tenant-metric-value">{tenant.cpu}%</div>
                                            <div className="tenant-metric-label">CPU Usage</div>
                                        </div>
                                        <div className="tenant-metric">
                                            <div className="tenant-metric-value">{tenant.memory}%</div>
                                            <div className="tenant-metric-label">Memory Usage</div>
                                        </div>
                                        <div className="tenant-metric">
                                            <div className="tenant-metric-value">{tenant.responseTime}ms</div>
                                            <div className="tenant-metric-label">Response Time</div>
                                        </div>
                                        <div className="tenant-metric">
                                            <div className="tenant-metric-value">{tenant.requests}</div>
                                            <div className="tenant-metric-label">Requests/min</div>
                                        </div>
                                    </div>

                                    <div style={{marginTop: '15px', textAlign: 'center'}}>
                                        <strong>Monthly Cost: ${tenant.cost}</strong> |
                                        <strong> Uptime: {tenant.uptime}%</strong>
                                    </div>
                                </div>
                            ))}
                        </div>

                        <div className="charts-section">
                            <h2 style={{marginBottom: '20px', textAlign: 'center'}}>📊 Real-time Analytics</h2>
                            <div className="charts-grid">
                                <div className="chart-container">
                                    <canvas id="costChart"></canvas>
                                </div>
                                <div className="chart-container">
                                    <canvas id="performanceChart"></canvas>
                                </div>
                            </div>
                        </div>

                        <div className="actions-section">
                            <h2 style={{marginBottom: '20px'}}>🚀 Quick Actions</h2>
                            <button className="btn" onClick={() => window.location.reload()}>
                                🔄 Refresh Data
                            </button>
                            <button className="btn" onClick={() => alert('Self-service onboarding coming soon!')}>
                                ➕ Add New Tenant
                            </button>
                            <button className="btn" onClick={() => alert('Scaling controls coming soon!')}>
                                📈 Scale Resources
                            </button>
                            <button className="btn" onClick={() => alert('Cost optimization coming soon!')}>
                                💰 Optimize Costs
                            </button>
                        </div>
                    </div>
                );
            }

            ReactDOM.render(<TenantManager />, document.getElementById('root'));
        </script>
    </body>
    </html>
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: dynamic-tenant-manager
  namespace: tenant-management
  labels:
    app: dynamic-tenant-manager
spec:
  replicas: 2
  selector:
    matchLabels:
      app: dynamic-tenant-manager
  template:
    metadata:
      labels:
        app: dynamic-tenant-manager
    spec:
      containers:
      - name: ui
        image: nginx:alpine
        ports:
        - containerPort: 80
        volumeMounts:
        - name: ui-content
          mountPath: /usr/share/nginx/html
        resources:
          requests:
            memory: "64Mi"
            cpu: "50m"
          limits:
            memory: "128Mi"
            cpu: "100m"
      volumes:
      - name: ui-content
        configMap:
          name: dynamic-tenant-ui
---
apiVersion: v1
kind: Service
metadata:
  name: dynamic-tenant-manager
  namespace: tenant-management
  labels:
    app: dynamic-tenant-manager
spec:
  selector:
    app: dynamic-tenant-manager
  ports:
  - port: 80
    targetPort: 80
    name: http
  type: ClusterIP
