apiVersion: v1
kind: ConfigMap
metadata:
  name: tenant-offboard-history
  namespace: tenant-management
  labels:
    app: tenant-tracking
data:
  offboarded-tenants.json: |
    {
      "offboardedTenants": [
        {
          "id": "demo-client",
          "name": "Demo Client Corp",
          "namespace": "tenant-demo-client",
          "tier": "standard",
          "created": "2024-12-15T14:30:00Z",
          "offboarded": "2024-12-25T16:45:00Z",
          "reason": "Contract ended",
          "finalCost": 2156.80,
          "duration": "10 days",
          "pods": [
            {"name": "demo-client-backend-1", "status": "Terminated"},
            {"name": "demo-client-frontend-1", "status": "Terminated"}
          ],
          "resources": {
            "cpu": "1000m",
            "memory": "2Gi",
            "storage": "50Gi"
          },
          "offboardedBy": "<EMAIL>"
        },
        {
          "id": "test-company",
          "name": "Test Company Ltd",
          "namespace": "tenant-test-company",
          "tier": "basic",
          "created": "2024-12-10T09:15:00Z",
          "offboarded": "2024-12-20T11:30:00Z",
          "reason": "Migration to self-hosted",
          "finalCost": 1024.50,
          "duration": "10 days",
          "pods": [
            {"name": "test-company-app-1", "status": "Terminated"}
          ],
          "resources": {
            "cpu": "500m",
            "memory": "1Gi",
            "storage": "25Gi"
          },
          "offboardedBy": "<EMAIL>"
        },
        {
          "id": "pilot-project",
          "name": "Pilot Project Team",
          "namespace": "tenant-pilot-project",
          "tier": "premium",
          "created": "2024-12-05T12:00:00Z",
          "offboarded": "2024-12-18T14:20:00Z",
          "reason": "Pilot completed",
          "finalCost": 4567.20,
          "duration": "13 days",
          "pods": [
            {"name": "pilot-project-backend-1", "status": "Terminated"},
            {"name": "pilot-project-frontend-1", "status": "Terminated"},
            {"name": "pilot-project-db-1", "status": "Terminated"}
          ],
          "resources": {
            "cpu": "2000m",
            "memory": "4Gi",
            "storage": "100Gi"
          },
          "offboardedBy": "<EMAIL>"
        }
      ],
      "lastUpdated": "2024-12-27T13:33:00Z"
    }
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: tenant-onboard-history
  namespace: tenant-management
  labels:
    app: tenant-tracking
data:
  onboarded-tenants.json: |
    {
      "onboardedTenants": [
        {
          "id": "enterprise-corp",
          "name": "Enterprise Corporation",
          "namespace": "tenant-enterprise-corp",
          "tier": "premium",
          "created": "2024-12-27T10:30:00Z",
          "status": "active",
          "onboardedBy": "<EMAIL>",
          "environment": "production",
          "resources": {
            "cpu": "2000m",
            "memory": "4Gi",
            "storage": "100Gi"
          },
          "labels": {
            "tenant.architrave.io/tenant-id": "enterprise-corp",
            "tenant.architrave.io/tenant-name": "Enterprise-Corporation",
            "tier": "premium",
            "monitoring": "enabled",
            "istio-injection": "enabled"
          }
        },
        {
          "id": "fast-test",
          "name": "Fast Test",
          "namespace": "tenant-fast-test",
          "tier": "standard",
          "created": "2024-12-27T08:15:00Z",
          "status": "active",
          "onboardedBy": "<EMAIL>",
          "environment": "development",
          "resources": {
            "cpu": "1000m",
            "memory": "2Gi",
            "storage": "50Gi"
          },
          "labels": {
            "tenant": "fast-test",
            "tier": "standard",
            "monitoring": "enabled",
            "istio-injection": "enabled"
          }
        },
        {
          "id": "global-solutions",
          "name": "Global Solutions Ltd",
          "namespace": "tenant-global-solutions",
          "tier": "standard",
          "created": "2024-12-27T07:55:00Z",
          "status": "active",
          "onboardedBy": "<EMAIL>",
          "environment": "production",
          "resources": {
            "cpu": "1200m",
            "memory": "2.5Gi",
            "storage": "60Gi"
          },
          "labels": {
            "tenant.architrave.io/tenant-id": "global-solutions",
            "tenant.architrave.io/tenant-name": "Global-Solutions-Ltd",
            "tier": "standard",
            "monitoring": "enabled",
            "istio-injection": "enabled"
          }
        },
        {
          "id": "tech-startup",
          "name": "Tech Startup Inc",
          "namespace": "tenant-tech-startup",
          "tier": "basic",
          "created": "2024-12-27T07:41:00Z",
          "status": "active",
          "onboardedBy": "<EMAIL>",
          "environment": "production",
          "resources": {
            "cpu": "500m",
            "memory": "1Gi",
            "storage": "25Gi"
          },
          "labels": {
            "tenant.architrave.io/tenant-id": "tech-startup",
            "tenant.architrave.io/tenant-name": "Tech-Startup-Inc",
            "tier": "basic",
            "monitoring": "enabled",
            "istio-injection": "enabled"
          }
        }
      ],
      "lastUpdated": "2024-12-27T13:33:00Z"
    }
