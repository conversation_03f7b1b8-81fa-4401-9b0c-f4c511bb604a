apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: {{TENANT_ID}}-isolation-policy
  namespace: tenant-{{TENANT_ID}}
  labels:
    app.kubernetes.io/managed-by: advanced-tenant-onboard
    tenant: {{TENANT_ID}}
spec:
  podSelector: {}
  policyTypes:
  - Ingress
  - Egress
  
  # INGRESS RULES - What can access this tenant
  ingress:
  # Allow traffic from Istio Gateway
  - from:
    - namespaceSelector:
        matchLabels:
          name: istio-system
    ports:
    - protocol: TCP
      port: 80
    - protocol: TCP
      port: 8080
    - protocol: TCP
      port: 443
  
  # Allow intra-namespace communication (within tenant)
  - from:
    - namespaceSelector:
        matchLabels:
          name: tenant-{{TENANT_ID}}
  
  # Allow monitoring from system namespaces
  - from:
    - namespaceSelector:
        matchLabels:
          name: kube-system
  - from:
    - namespaceSelector:
        matchLabels:
          name: monitoring
  
  # EGRESS RULES - What this tenant can access
  egress:
  # Allow DNS resolution
  - to: []
    ports:
    - protocol: UDP
      port: 53
    - protocol: TCP
      port: 53
  
  # Allow HTTPS outbound (for external APIs, S3, etc.)
  - to: []
    ports:
    - protocol: TCP
      port: 443
    - protocol: TCP
      port: 80
  
  # Allow database access (Aurora Serverless)
  - to: []
    ports:
    - protocol: TCP
      port: 3306
    - protocol: TCP
      port: 5432
  
  # Allow intra-namespace communication
  - to:
    - namespaceSelector:
        matchLabels:
          name: tenant-{{TENANT_ID}}
  
  # Block all other cross-tenant communication
  # (This is implicit - anything not explicitly allowed is denied)
---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: {{TENANT_ID}}-deny-cross-tenant
  namespace: tenant-{{TENANT_ID}}
  labels:
    app.kubernetes.io/managed-by: advanced-tenant-onboard
    tenant: {{TENANT_ID}}
spec:
  podSelector: {}
  policyTypes:
  - Ingress
  - Egress
  
  # EXPLICITLY DENY cross-tenant communication
  ingress:
  - from:
    - namespaceSelector:
        matchExpressions:
        - key: name
          operator: NotIn
          values:
          - tenant-{{TENANT_ID}}
          - istio-system
          - kube-system
          - monitoring
    ports: []  # Deny all ports
  
  egress:
  - to:
    - namespaceSelector:
        matchExpressions:
        - key: name
          operator: In
          values:
          - tenant-{{OTHER_TENANT_ID}}  # This would be dynamically generated to block other tenants
    ports: []  # Deny all ports
---
apiVersion: v1
kind: NetworkPolicy
metadata:
  name: {{TENANT_ID}}-istio-gateway-access
  namespace: tenant-{{TENANT_ID}}
  labels:
    app.kubernetes.io/managed-by: advanced-tenant-onboard
    tenant: {{TENANT_ID}}
spec:
  podSelector:
    matchLabels:
      app: {{TENANT_ID}}-frontend
  policyTypes:
  - Ingress
  
  ingress:
  # ONLY allow Istio Gateway to access frontend
  - from:
    - namespaceSelector:
        matchLabels:
          name: istio-system
      podSelector:
        matchLabels:
          istio: ingress
    ports:
    - protocol: TCP
      port: 80
    - protocol: TCP
      port: 443
