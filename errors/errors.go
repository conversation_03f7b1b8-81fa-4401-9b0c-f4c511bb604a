package errors

import (
	"fmt"
	"time"
)

// ErrorType represents the type of error
type ErrorType string

const (
	ErrorTypeValidation   ErrorType = "VALIDATION"
	ErrorTypeSecurity     ErrorType = "SECURITY"
	ErrorTypeDatabase     ErrorType = "DATABASE"
	ErrorTypeNetwork      ErrorType = "NETWORK"
	ErrorTypeInfrastructure ErrorType = "INFRASTRUCTURE"
	ErrorTypeApplication  ErrorType = "APPLICATION"
	ErrorTypeUnknown      ErrorType = "UNKNOWN"
)

// ErrorSeverity represents the severity of an error
type ErrorSeverity string

const (
	ErrorSeverityCritical ErrorSeverity = "CRITICAL"
	ErrorSeverityHigh     ErrorSeverity = "HIGH"
	ErrorSeverityMedium   ErrorSeverity = "MEDIUM"
	ErrorSeverityLow      ErrorSeverity = "LOW"
)

// StructuredError represents a structured error with context
type StructuredError struct {
	Type      ErrorType     `json:"type"`
	Severity  ErrorSeverity `json:"severity"`
	Message   string        `json:"message"`
	Field     string        `json:"field,omitempty"`
	Value     string        `json:"value,omitempty"`
	Cause     error         `json:"cause,omitempty"`
	Timestamp time.Time     `json:"timestamp"`
	CorrelationID string    `json:"correlation_id,omitempty"`
	Context   map[string]interface{} `json:"context,omitempty"`
}

func (e *StructuredError) Error() string {
	if e.Cause != nil {
		return fmt.Sprintf("[%s] %s: %v", e.Type, e.Message, e.Cause)
	}
	return fmt.Sprintf("[%s] %s", e.Type, e.Message)
}

// NewValidationError creates a new validation error
func NewValidationError(message, field, value string) *StructuredError {
	return &StructuredError{
		Type:      ErrorTypeValidation,
		Severity:  ErrorSeverityHigh,
		Message:   message,
		Field:     field,
		Value:     value,
		Timestamp: time.Now(),
	}
}

// NewSecurityError creates a new security error
func NewSecurityError(message string, cause error) *StructuredError {
	return &StructuredError{
		Type:      ErrorTypeSecurity,
		Severity:  ErrorSeverityCritical,
		Message:   message,
		Cause:     cause,
		Timestamp: time.Now(),
	}
}

// NewDatabaseError creates a new database error
func NewDatabaseError(message, operation string, cause error) *StructuredError {
	return &StructuredError{
		Type:      ErrorTypeDatabase,
		Severity:  ErrorSeverityHigh,
		Message:   message,
		Context:   map[string]interface{}{"operation": operation},
		Cause:     cause,
		Timestamp: time.Now(),
	}
}

// NewInfrastructureError creates a new infrastructure error
func NewInfrastructureError(message, component string, cause error) *StructuredError {
	return &StructuredError{
		Type:      ErrorTypeInfrastructure,
		Severity:  ErrorSeverityHigh,
		Message:   message,
		Context:   map[string]interface{}{"component": component},
		Cause:     cause,
		Timestamp: time.Now(),
	}
}

// WithCorrelationID adds a correlation ID to an error
func (e *StructuredError) WithCorrelationID(correlationID string) *StructuredError {
	e.CorrelationID = correlationID
	return e
}

// WithContext adds context to an error
func (e *StructuredError) WithContext(key string, value interface{}) *StructuredError {
	if e.Context == nil {
		e.Context = make(map[string]interface{})
	}
	e.Context[key] = value
	return e
}

// IsValidationError checks if an error is a validation error
func IsValidationError(err error) bool {
	if structuredErr, ok := err.(*StructuredError); ok {
		return structuredErr.Type == ErrorTypeValidation
	}
	return false
}

// IsSecurityError checks if an error is a security error
func IsSecurityError(err error) bool {
	if structuredErr, ok := err.(*StructuredError); ok {
		return structuredErr.Type == ErrorTypeSecurity
	}
	return false
}

// IsDatabaseError checks if an error is a database error
func IsDatabaseError(err error) bool {
	if structuredErr, ok := err.(*StructuredError); ok {
		return structuredErr.Type == ErrorTypeDatabase
	}
	return false
} 