---
# Distributed Tracing with Jaeger/OpenTelemetry
apiVersion: v1
kind: Namespace
metadata:
  name: tracing-system
  labels:
    pod-security.kubernetes.io/enforce: restricted
    pod-security.kubernetes.io/audit: restricted
    pod-security.kubernetes.io/warn: restricted
---
# Jaeger Operator
apiVersion: apps/v1
kind: Deployment
metadata:
  name: jaeger-operator
  namespace: tracing-system
  labels:
    app: jaeger-operator
spec:
  replicas: 1
  selector:
    matchLabels:
      app: jaeger-operator
  template:
    metadata:
      labels:
        app: jaeger-operator
    spec:
      serviceAccountName: jaeger-operator
      securityContext:
        runAsNonRoot: true
        runAsUser: 65534
        runAsGroup: 65534
        fsGroup: 65534
        seccompProfile:
          type: RuntimeDefault
      containers:
      - name: jaeger-operator
        image: jaegertracing/jaeger-operator:latest
        ports:
        - containerPort: 8080
          name: metrics
        - containerPort: 9443
          name: webhook
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          capabilities:
            drop: ["ALL"]
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "200m"
        env:
        - name: WATCH_NAMESPACE
          value: ""
        - name: POD_NAME
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: OPERATOR_NAME
          value: "jaeger-operator"
        volumeMounts:
        - name: tmp
          mountPath: /tmp
        livenessProbe:
          httpGet:
            path: /healthz
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /readyz
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 10
      volumes:
      - name: tmp
        emptyDir: {}
---
# Jaeger Instance
apiVersion: jaegertracing.io/v1
kind: Jaeger
metadata:
  name: jaeger-production
  namespace: tracing-system
spec:
  strategy: production
  storage:
    type: elasticsearch
    elasticsearch:
      nodeCount: 3
      redundancyPolicy: SingleRedundancy
      resources:
        requests:
          memory: "2Gi"
          cpu: "500m"
        limits:
          memory: "4Gi"
          cpu: "1000m"
  collector:
    replicas: 3
    resources:
      requests:
        memory: "512Mi"
        cpu: "200m"
      limits:
        memory: "1Gi"
        cpu: "500m"
    config: |
      receivers:
        otlp:
          protocols:
            grpc:
              endpoint: 0.0.0.0:14250
            http:
              endpoint: 0.0.0.0:14268
        jaeger:
          protocols:
            grpc:
              endpoint: 0.0.0.0:14250
            thrift_http:
              endpoint: 0.0.0.0:14268
            thrift_compact:
              endpoint: 0.0.0.0:6831
            thrift_binary:
              endpoint: 0.0.0.0:6832
      processors:
        batch:
          timeout: 1s
          send_batch_size: 1024
        memory_limiter:
          limit_mib: 512
        resource:
          attributes:
            - key: tenant.id
              from_attribute: tenant_id
              action: upsert
      exporters:
        jaeger:
          endpoint: jaeger-production-collector:14250
          tls:
            insecure: true
      service:
        pipelines:
          traces:
            receivers: [otlp, jaeger]
            processors: [memory_limiter, resource, batch]
            exporters: [jaeger]
  query:
    replicas: 2
    resources:
      requests:
        memory: "256Mi"
        cpu: "100m"
      limits:
        memory: "512Mi"
        cpu: "200m"
  agent:
    strategy: DaemonSet
    resources:
      requests:
        memory: "128Mi"
        cpu: "100m"
      limits:
        memory: "256Mi"
        cpu: "200m"
---
# OpenTelemetry Collector
apiVersion: apps/v1
kind: Deployment
metadata:
  name: otel-collector
  namespace: tracing-system
  labels:
    app: otel-collector
spec:
  replicas: 3
  selector:
    matchLabels:
      app: otel-collector
  template:
    metadata:
      labels:
        app: otel-collector
    spec:
      serviceAccountName: otel-collector
      securityContext:
        runAsNonRoot: true
        runAsUser: 65534
        runAsGroup: 65534
        fsGroup: 65534
        seccompProfile:
          type: RuntimeDefault
      containers:
      - name: otel-collector
        image: otel/opentelemetry-collector-contrib:latest
        command: ["/otelcol-contrib"]
        args: ["--config=/etc/otel-collector-config.yaml"]
        ports:
        - containerPort: 4317
          name: otlp-grpc
        - containerPort: 4318
          name: otlp-http
        - containerPort: 8888
          name: metrics
        - containerPort: 8889
          name: prom-metrics
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          capabilities:
            drop: ["ALL"]
        resources:
          requests:
            memory: "512Mi"
            cpu: "200m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        volumeMounts:
        - name: otel-collector-config
          mountPath: /etc/otel-collector-config.yaml
          subPath: otel-collector-config.yaml
        - name: tmp
          mountPath: /tmp
        env:
        - name: GOMEMLIMIT
          value: "750MiB"
        livenessProbe:
          httpGet:
            path: /
            port: 8888
          initialDelaySeconds: 30
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /
            port: 8888
          initialDelaySeconds: 5
          periodSeconds: 10
      volumes:
      - name: otel-collector-config
        configMap:
          name: otel-collector-config
      - name: tmp
        emptyDir: {}
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: otel-collector-config
  namespace: tracing-system
data:
  otel-collector-config.yaml: |
    receivers:
      otlp:
        protocols:
          grpc:
            endpoint: 0.0.0.0:4317
          http:
            endpoint: 0.0.0.0:4318
      jaeger:
        protocols:
          grpc:
            endpoint: 0.0.0.0:14250
          thrift_http:
            endpoint: 0.0.0.0:14268
          thrift_compact:
            endpoint: 0.0.0.0:6831
          thrift_binary:
            endpoint: 0.0.0.0:6832
      prometheus:
        config:
          scrape_configs:
            - job_name: 'otel-collector'
              static_configs:
                - targets: ['localhost:8888']
    
    processors:
      batch:
        timeout: 1s
        send_batch_size: 1024
        send_batch_max_size: 2048
      
      memory_limiter:
        limit_mib: 750
        spike_limit_mib: 150
      
      resource:
        attributes:
          - key: service.namespace
            from_attribute: k8s.namespace.name
            action: upsert
          - key: service.instance.id
            from_attribute: k8s.pod.name
            action: upsert
          - key: tenant.id
            from_attribute: tenant_id
            action: upsert
          - key: cluster.name
            value: "architrave-cluster"
            action: upsert
      
      attributes:
        actions:
          - key: tenant_id
            action: extract
            pattern: ^tenant-(.+)$
            from_attribute: k8s.namespace.name
          - key: environment
            value: "production"
            action: upsert
      
      span:
        name:
          to_attributes:
            rules:
              - ^\/api\/v1\/tenants\/(?P<tenant_id>.+)\/.*$
      
      filter:
        traces:
          span:
            - 'attributes["http.route"] == "/health"'
            - 'attributes["http.route"] == "/metrics"'
    
    exporters:
      jaeger:
        endpoint: jaeger-production-collector.tracing-system.svc.cluster.local:14250
        tls:
          insecure: true
      
      prometheus:
        endpoint: "0.0.0.0:8889"
        namespace: "otel"
        const_labels:
          cluster: "architrave-cluster"
      
      logging:
        loglevel: info
        sampling_initial: 5
        sampling_thereafter: 200
    
    extensions:
      health_check:
        endpoint: 0.0.0.0:8888
      
      pprof:
        endpoint: 0.0.0.0:1777
      
      zpages:
        endpoint: 0.0.0.0:55679
    
    service:
      extensions: [health_check, pprof, zpages]
      pipelines:
        traces:
          receivers: [otlp, jaeger]
          processors: [memory_limiter, resource, attributes, span, filter, batch]
          exporters: [jaeger, logging]
        
        metrics:
          receivers: [prometheus]
          processors: [memory_limiter, resource, batch]
          exporters: [prometheus]
      
      telemetry:
        logs:
          level: "info"
        metrics:
          address: 0.0.0.0:8888
---
# Tenant Tracing Instrumentation
apiVersion: apps/v1
kind: Deployment
metadata:
  name: tracing-instrumentation
  namespace: tracing-system
  labels:
    app: tracing-instrumentation
spec:
  replicas: 2
  selector:
    matchLabels:
      app: tracing-instrumentation
  template:
    metadata:
      labels:
        app: tracing-instrumentation
    spec:
      serviceAccountName: tracing-instrumentation
      securityContext:
        runAsNonRoot: true
        runAsUser: 65534
        runAsGroup: 65534
        fsGroup: 65534
        seccompProfile:
          type: RuntimeDefault
      containers:
      - name: instrumentation
        image: python:3.11-alpine
        command: ["/bin/sh", "-c"]
        args:
        - |
          pip install flask kubernetes opentelemetry-api opentelemetry-sdk opentelemetry-instrumentation opentelemetry-exporter-jaeger opentelemetry-instrumentation-flask opentelemetry-instrumentation-requests
          cat > /app/tracing_service.py << 'EOF'
          #!/usr/bin/env python3
          import os
          import time
          from flask import Flask, request, jsonify
          from kubernetes import client, config
          from opentelemetry import trace
          from opentelemetry.sdk.trace import TracerProvider
          from opentelemetry.sdk.trace.export import BatchSpanProcessor
          from opentelemetry.exporter.jaeger.thrift import JaegerExporter
          from opentelemetry.instrumentation.flask import FlaskInstrumentor
          from opentelemetry.instrumentation.requests import RequestsInstrumentor
          from opentelemetry.sdk.resources import Resource
          
          # Initialize OpenTelemetry
          resource = Resource.create({
              "service.name": "tracing-instrumentation",
              "service.version": "1.0.0",
              "deployment.environment": "production"
          })
          
          trace.set_tracer_provider(TracerProvider(resource=resource))
          tracer = trace.get_tracer(__name__)
          
          # Configure Jaeger exporter
          jaeger_exporter = JaegerExporter(
              agent_host_name="jaeger-production-agent.tracing-system.svc.cluster.local",
              agent_port=6831,
          )
          
          span_processor = BatchSpanProcessor(jaeger_exporter)
          trace.get_tracer_provider().add_span_processor(span_processor)
          
          app = Flask(__name__)
          
          # Auto-instrument Flask and requests
          FlaskInstrumentor().instrument_app(app)
          RequestsInstrumentor().instrument()
          
          # Load Kubernetes config
          try:
              config.load_incluster_config()
          except:
              config.load_kube_config()
          
          k8s_client = client.CoreV1Api()
          apps_client = client.AppsV1Api()
          
          @app.route('/api/tracing/instrument/<tenant_id>', methods=['POST'])
          def instrument_tenant(tenant_id):
              """Add tracing instrumentation to a tenant"""
              with tracer.start_as_current_span("instrument_tenant") as span:
                  span.set_attribute("tenant.id", tenant_id)
                  span.set_attribute("operation", "instrument")
                  
                  try:
                      # Add tracing configuration to tenant deployments
                      result = add_tracing_to_tenant(tenant_id)
                      
                      span.set_attribute("instrumentation.success", True)
                      span.set_attribute("deployments.updated", len(result.get('updated_deployments', [])))
                      
                      return jsonify({
                          "status": "success",
                          "tenant_id": tenant_id,
                          "instrumentation_added": result,
                          "jaeger_endpoint": f"http://jaeger-production-query.tracing-system.svc.cluster.local:16686"
                      })
                  
                  except Exception as e:
                      span.set_attribute("instrumentation.success", False)
                      span.set_attribute("error.message", str(e))
                      span.record_exception(e)
                      return jsonify({"error": str(e)}), 500
          
          @app.route('/api/tracing/analyze/<tenant_id>', methods=['GET'])
          def analyze_tenant_traces(tenant_id):
              """Analyze traces for a tenant"""
              with tracer.start_as_current_span("analyze_tenant_traces") as span:
                  span.set_attribute("tenant.id", tenant_id)
                  span.set_attribute("operation", "analyze")
                  
                  try:
                      # Get trace analysis
                      analysis = get_trace_analysis(tenant_id)
                      
                      span.set_attribute("traces.analyzed", analysis.get('trace_count', 0))
                      span.set_attribute("analysis.success", True)
                      
                      return jsonify(analysis)
                  
                  except Exception as e:
                      span.set_attribute("analysis.success", False)
                      span.record_exception(e)
                      return jsonify({"error": str(e)}), 500
          
          @app.route('/api/tracing/performance/<tenant_id>', methods=['GET'])
          def get_performance_insights(tenant_id):
              """Get performance insights from traces"""
              with tracer.start_as_current_span("get_performance_insights") as span:
                  span.set_attribute("tenant.id", tenant_id)
                  span.set_attribute("operation", "performance_analysis")
                  
                  try:
                      insights = analyze_performance_traces(tenant_id)
                      
                      span.set_attribute("insights.generated", len(insights.get('bottlenecks', [])))
                      
                      return jsonify(insights)
                  
                  except Exception as e:
                      span.record_exception(e)
                      return jsonify({"error": str(e)}), 500
          
          def add_tracing_to_tenant(tenant_id):
              """Add OpenTelemetry instrumentation to tenant deployments"""
              namespace = f"tenant-{tenant_id}"
              updated_deployments = []
              
              try:
                  deployments = apps_client.list_namespaced_deployment(namespace=namespace)
                  
                  for deployment in deployments.items:
                      # Add OpenTelemetry environment variables
                      containers = deployment.spec.template.spec.containers
                      
                      for container in containers:
                          if not container.env:
                              container.env = []
                          
                          # Add OpenTelemetry configuration
                          otel_env_vars = [
                              client.V1EnvVar(name="OTEL_SERVICE_NAME", value=f"{deployment.metadata.name}"),
                              client.V1EnvVar(name="OTEL_SERVICE_VERSION", value="1.0.0"),
                              client.V1EnvVar(name="OTEL_EXPORTER_JAEGER_AGENT_HOST", value="jaeger-production-agent.tracing-system.svc.cluster.local"),
                              client.V1EnvVar(name="OTEL_EXPORTER_JAEGER_AGENT_PORT", value="6831"),
                              client.V1EnvVar(name="OTEL_RESOURCE_ATTRIBUTES", value=f"service.namespace={namespace},tenant.id={tenant_id}"),
                              client.V1EnvVar(name="OTEL_TRACES_EXPORTER", value="jaeger"),
                              client.V1EnvVar(name="OTEL_METRICS_EXPORTER", value="prometheus"),
                              client.V1EnvVar(name="OTEL_LOGS_EXPORTER", value="none")
                          ]
                          
                          # Remove existing OTEL env vars and add new ones
                          container.env = [env for env in container.env if not env.name.startswith('OTEL_')]
                          container.env.extend(otel_env_vars)
                      
                      # Add tracing annotation
                      if not deployment.spec.template.metadata.annotations:
                          deployment.spec.template.metadata.annotations = {}
                      
                      deployment.spec.template.metadata.annotations['tracing.architrave.com/instrumented'] = 'true'
                      deployment.spec.template.metadata.annotations['tracing.architrave.com/instrumented-at'] = time.strftime('%Y-%m-%dT%H:%M:%SZ')
                      
                      # Update deployment
                      apps_client.patch_namespaced_deployment(
                          name=deployment.metadata.name,
                          namespace=namespace,
                          body=deployment
                      )
                      
                      updated_deployments.append(deployment.metadata.name)
              
              except Exception as e:
                  print(f"Error adding tracing to tenant {tenant_id}: {e}")
              
              return {
                  "updated_deployments": updated_deployments,
                  "namespace": namespace,
                  "instrumentation_type": "opentelemetry"
              }
          
          def get_trace_analysis(tenant_id):
              """Analyze traces for a tenant (placeholder)"""
              # In production, this would query Jaeger API
              return {
                  "tenant_id": tenant_id,
                  "trace_count": 1250,
                  "services": [
                      {"name": f"tenant-{tenant_id}-backend", "span_count": 850},
                      {"name": f"tenant-{tenant_id}-frontend", "span_count": 400}
                  ],
                  "avg_response_time": "245ms",
                  "error_rate": "0.8%",
                  "top_operations": [
                      {"operation": "GET /api/documents", "count": 320, "avg_duration": "180ms"},
                      {"operation": "POST /api/upload", "count": 180, "avg_duration": "850ms"},
                      {"operation": "GET /api/users", "count": 150, "avg_duration": "95ms"}
                  ],
                  "analysis_period": "last_24_hours"
              }
          
          def analyze_performance_traces(tenant_id):
              """Analyze performance from traces"""
              return {
                  "tenant_id": tenant_id,
                  "bottlenecks": [
                      {
                          "service": f"tenant-{tenant_id}-backend",
                          "operation": "database_query",
                          "avg_duration": "450ms",
                          "p95_duration": "1.2s",
                          "recommendation": "Add database index on frequently queried columns"
                      },
                      {
                          "service": f"tenant-{tenant_id}-backend",
                          "operation": "file_upload",
                          "avg_duration": "2.1s",
                          "p95_duration": "5.8s",
                          "recommendation": "Implement async file processing"
                      }
                  ],
                  "performance_score": 78,
                  "recommendations": [
                      "Optimize database queries with proper indexing",
                      "Implement caching for frequently accessed data",
                      "Use async processing for file uploads",
                      "Add connection pooling for external services"
                  ],
                  "sla_compliance": {
                      "target_response_time": "500ms",
                      "actual_p95": "680ms",
                      "compliance_percentage": 85.2
                  }
              }
          
          @app.route('/health', methods=['GET'])
          def health_check():
              with tracer.start_as_current_span("health_check"):
                  return jsonify({"status": "healthy", "service": "tracing-instrumentation"})
          
          if __name__ == '__main__':
              app.run(host='0.0.0.0', port=8080)
          EOF
          
          python /app/tracing_service.py
        ports:
        - containerPort: 8080
          name: http
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          capabilities:
            drop: ["ALL"]
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "200m"
        volumeMounts:
        - name: tmp
          mountPath: /tmp
        - name: app
          mountPath: /app
        env:
        - name: OTEL_SERVICE_NAME
          value: "tracing-instrumentation"
        - name: OTEL_EXPORTER_JAEGER_AGENT_HOST
          value: "jaeger-production-agent.tracing-system.svc.cluster.local"
        - name: OTEL_EXPORTER_JAEGER_AGENT_PORT
          value: "6831"
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 10
      volumes:
      - name: tmp
        emptyDir: {}
      - name: app
        emptyDir: {}
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: jaeger-operator
  namespace: tracing-system
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: otel-collector
  namespace: tracing-system
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: tracing-instrumentation
  namespace: tracing-system
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: jaeger-operator
rules:
- apiGroups: [""]
  resources: ["pods", "services", "endpoints", "persistentvolumeclaims", "events", "configmaps", "secrets", "serviceaccounts"]
  verbs: ["*"]
- apiGroups: ["apps"]
  resources: ["deployments", "daemonsets", "replicasets", "statefulsets"]
  verbs: ["*"]
- apiGroups: ["monitoring.coreos.com"]
  resources: ["servicemonitors"]
  verbs: ["get", "create"]
- apiGroups: ["jaegertracing.io"]
  resources: ["*"]
  verbs: ["*"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: tracing-instrumentation
rules:
- apiGroups: [""]
  resources: ["namespaces", "pods"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["apps"]
  resources: ["deployments"]
  verbs: ["get", "list", "watch", "update", "patch"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: jaeger-operator
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: jaeger-operator
subjects:
- kind: ServiceAccount
  name: jaeger-operator
  namespace: tracing-system
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: tracing-instrumentation
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: tracing-instrumentation
subjects:
- kind: ServiceAccount
  name: tracing-instrumentation
  namespace: tracing-system
---
apiVersion: v1
kind: Service
metadata:
  name: otel-collector
  namespace: tracing-system
  labels:
    app: otel-collector
spec:
  ports:
  - port: 4317
    targetPort: 4317
    name: otlp-grpc
  - port: 4318
    targetPort: 4318
    name: otlp-http
  - port: 8888
    targetPort: 8888
    name: metrics
  - port: 8889
    targetPort: 8889
    name: prom-metrics
  selector:
    app: otel-collector
---
apiVersion: v1
kind: Service
metadata:
  name: tracing-instrumentation
  namespace: tracing-system
  labels:
    app: tracing-instrumentation
spec:
  ports:
  - port: 8080
    targetPort: 8080
    name: http
  selector:
    app: tracing-instrumentation
