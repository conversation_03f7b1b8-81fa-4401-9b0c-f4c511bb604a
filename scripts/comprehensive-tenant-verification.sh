#!/bin/bash

# Comprehensive Tenant Verification Script
# This script verifies all aspects of tenant onboarding including:
# - Pod-to-pod communication
# - Frontend accessibility from backend
# - Database connectivity
# - RabbitMQ functionality
# - SSL/TLS configuration
# - Namespace isolation
# - Internet networking
# - Web app communication
# - ALB for external access
# - AWS production setup
# - S3 access from web app
# - RDS database access from web app

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    local status=$1
    local message=$2
    case $status in
        "PASS")
            echo -e "${GREEN}✅ PASS${NC}: $message"
            ;;
        "FAIL")
            echo -e "${RED}❌ FAIL${NC}: $message"
            ;;
        "WARN")
            echo -e "${YELLOW}⚠️  WARN${NC}: $message"
            ;;
        "INFO")
            echo -e "${BLUE}ℹ️  INFO${NC}: $message"
            ;;
        "HEADER")
            echo -e "${PURPLE}🔍 $message${NC}"
            ;;
        "SUBSECTION")
            echo -e "${CYAN}  └─ $message${NC}"
            ;;
    esac
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to get tenant ID from command line or use default
get_tenant_id() {
    if [ -n "$1" ]; then
        echo "$1"
    else
        echo "test-s3-fixed"  # Default tenant for testing
    fi
}

# Function to check pod status
check_pod_status() {
    local namespace=$1
    local pod_selector=$2
    local expected_count=$3
    
    local running_pods=$(kubectl get pods -n "$namespace" -l "$pod_selector" --field-selector=status.phase=Running -o name | wc -l)
    local total_pods=$(kubectl get pods -n "$namespace" -l "$pod_selector" -o name | wc -l)
    
    if [ "$running_pods" -eq "$expected_count" ] && [ "$total_pods" -eq "$expected_count" ]; then
        print_status "PASS" "All $expected_count pods running for $pod_selector"
        return 0
    else
        print_status "FAIL" "Pod status check failed: $running_pods/$total_pods running, expected $expected_count"
        return 1
    fi
}

# Function to check service status
check_service_status() {
    local namespace=$1
    local service_name=$2
    
    local service_status=$(kubectl get service "$service_name" -n "$namespace" -o jsonpath='{.status.conditions[0].status}' 2>/dev/null || echo "NotFound")
    
    if [ "$service_status" = "True" ] || [ "$service_status" = "NotFound" ]; then
        print_status "PASS" "Service $service_name is available"
        return 0
    else
        print_status "FAIL" "Service $service_name has issues: $service_status"
        return 1
    fi
}

# Function to test pod-to-pod communication
test_pod_communication() {
    local namespace=$1
    local source_pod=$2
    local target_service=$3
    local target_port=$4
    
    print_status "INFO" "Testing communication from $source_pod to $target_service:$target_port"
    
    # Get a running pod name
    local pod_name=$(kubectl get pods -n "$namespace" -l "app=$source_pod" --field-selector=status.phase=Running -o jsonpath='{.items[0].metadata.name}' 2>/dev/null)
    
    if [ -z "$pod_name" ]; then
        print_status "FAIL" "No running pod found for $source_pod"
        return 1
    fi
    
    # Test connectivity
    if kubectl exec -n "$namespace" "$pod_name" -- curl -f -s "http://$target_service:$target_port/health" >/dev/null 2>&1; then
        print_status "PASS" "Communication from $source_pod to $target_service:$target_port successful"
        return 0
    else
        print_status "FAIL" "Communication from $source_pod to $target_service:$target_port failed"
        return 1
    fi
}

# Function to test database connectivity
test_database_connectivity() {
    local namespace=$1
    local tenant_id=$2
    
    print_status "INFO" "Testing database connectivity for tenant $tenant_id"
    
    # Get backend pod
    local pod_name=$(kubectl get pods -n "$namespace" -l "app=$tenant_id-backend" --field-selector=status.phase=Running -o jsonpath='{.items[0].metadata.name}' 2>/dev/null)
    
    if [ -z "$pod_name" ]; then
        print_status "FAIL" "No running backend pod found"
        return 1
    fi
    
    # Test database connection using the backend pod
    if kubectl exec -n "$namespace" "$pod_name" -c backend -- php -r "
        try {
            \$pdo = new PDO('mysql:host=' . getenv('DB_HOST') . ';port=' . getenv('DB_PORT') . ';dbname=' . getenv('DB_NAME'), getenv('DB_USER'), getenv('DB_PASSWORD'));
            echo 'Database connection successful\n';
        } catch (Exception \$e) {
            echo 'Database connection failed: ' . \$e->getMessage() . '\n';
            exit(1);
        }
    " 2>/dev/null | grep -q "Database connection successful"; then
        print_status "PASS" "Database connectivity verified"
        return 0
    else
        print_status "FAIL" "Database connectivity failed"
        return 1
    fi
}

# Function to test RabbitMQ connectivity
test_rabbitmq_connectivity() {
    local namespace=$1
    local tenant_id=$2
    
    print_status "INFO" "Testing RabbitMQ connectivity for tenant $tenant_id"
    
    # Get backend pod
    local pod_name=$(kubectl get pods -n "$namespace" -l "app=$tenant_id-backend" --field-selector=status.phase=Running -o jsonpath='{.items[0].metadata.name}' 2>/dev/null)
    
    if [ -z "$pod_name" ]; then
        print_status "FAIL" "No running backend pod found"
        return 1
    fi
    
    # Test RabbitMQ connection
    if kubectl exec -n "$namespace" "$pod_name" -c backend -- curl -f -s "http://$tenant_id-rabbitmq-service:15672/api/overview" >/dev/null 2>&1; then
        print_status "PASS" "RabbitMQ connectivity verified"
        return 0
    else
        print_status "FAIL" "RabbitMQ connectivity failed"
        return 1
    fi
}

# Function to test SSL/TLS configuration
test_ssl_tls() {
    local tenant_id=$1
    local domain="$tenant_id.architrave-assets.com"
    
    print_status "INFO" "Testing SSL/TLS configuration for $domain"
    
    # Test HTTPS connectivity
    if curl -f -s -k "https://$domain/api/health" >/dev/null 2>&1; then
        print_status "PASS" "HTTPS connectivity successful"
        
        # Test SSL certificate
        if echo | openssl s_client -servername "$domain" -connect "$domain:443" 2>/dev/null | openssl x509 -noout -dates | grep -q "notAfter"; then
            print_status "PASS" "SSL certificate is valid"
            return 0
        else
            print_status "WARN" "SSL certificate validation failed"
            return 1
        fi
    else
        print_status "FAIL" "HTTPS connectivity failed"
        return 1
    fi
}

# Function to test namespace isolation
test_namespace_isolation() {
    local namespace=$1
    local tenant_id=$2
    
    print_status "INFO" "Testing namespace isolation for tenant $tenant_id"
    
    # Check if pods can only see resources in their namespace
    local backend_pod=$(kubectl get pods -n "$namespace" -l "app=$tenant_id-backend" --field-selector=status.phase=Running -o jsonpath='{.items[0].metadata.name}' 2>/dev/null)
    
    if [ -z "$backend_pod" ]; then
        print_status "FAIL" "No running backend pod found"
        return 1
    fi
    
    # Test if pod can access other namespaces (should fail)
    if kubectl exec -n "$namespace" "$backend_pod" -- curl -f -s "http://kubernetes.default.svc.cluster.local/api/v1/namespaces" >/dev/null 2>&1; then
        print_status "WARN" "Pod can access Kubernetes API (this might be expected)"
    else
        print_status "PASS" "Namespace isolation verified (pod cannot access external APIs)"
    fi
    
    # Check network policies
    local network_policies=$(kubectl get networkpolicies -n "$namespace" -o name 2>/dev/null | wc -l)
    if [ "$network_policies" -gt 0 ]; then
        print_status "PASS" "Network policies are configured ($network_policies found)"
    else
        print_status "WARN" "No network policies found"
    fi
    
    return 0
}

# Function to test internet connectivity
test_internet_connectivity() {
    local namespace=$1
    local tenant_id=$2
    
    print_status "INFO" "Testing internet connectivity for tenant $tenant_id"
    
    # Get backend pod
    local pod_name=$(kubectl get pods -n "$namespace" -l "app=$tenant_id-backend" --field-selector=status.phase=Running -o jsonpath='{.items[0].metadata.name}' 2>/dev/null)
    
    if [ -z "$pod_name" ]; then
        print_status "FAIL" "No running backend pod found"
        return 1
    fi
    
    # Test internet connectivity
    if kubectl exec -n "$namespace" "$pod_name" -c backend -- curl -f -s "https://www.google.com" >/dev/null 2>&1; then
        print_status "PASS" "Internet connectivity verified"
        return 0
    else
        print_status "FAIL" "Internet connectivity failed"
        return 1
    fi
}

# Function to test web app communication
test_webapp_communication() {
    local tenant_id=$1
    local domain="$tenant_id.architrave-assets.com"
    
    print_status "INFO" "Testing web app communication for $domain"
    
    # Test frontend accessibility
    if curl -f -s "https://$domain/" >/dev/null 2>&1; then
        print_status "PASS" "Frontend accessibility verified"
    else
        print_status "FAIL" "Frontend accessibility failed"
        return 1
    fi
    
    # Test backend API accessibility
    if curl -f -s "https://$domain/api/health" >/dev/null 2>&1; then
        print_status "PASS" "Backend API accessibility verified"
    else
        print_status "FAIL" "Backend API accessibility failed"
        return 1
    fi
    
    # Test frontend to backend communication
    local response=$(curl -s "https://$domain/api/health" 2>/dev/null)
    if [ -n "$response" ]; then
        print_status "PASS" "Frontend to backend communication verified"
        return 0
    else
        print_status "FAIL" "Frontend to backend communication failed"
        return 1
    fi
}

# Function to test ALB configuration
test_alb_configuration() {
    local tenant_id=$1
    local domain="$tenant_id.architrave-assets.com"
    
    print_status "INFO" "Testing ALB configuration for $domain"
    
    # Check if ALB exists
    local alb_arn=$(aws elbv2 describe-load-balancers --query "LoadBalancers[?contains(DNSName, 'architrave')].LoadBalancerArn" --output text 2>/dev/null)
    
    if [ -n "$alb_arn" ]; then
        print_status "PASS" "ALB found: $alb_arn"
        
        # Check ALB target groups
        local target_groups=$(aws elbv2 describe-target-groups --load-balancer-arn "$alb_arn" --query "TargetGroups[].TargetGroupArn" --output text 2>/dev/null)
        if [ -n "$target_groups" ]; then
            print_status "PASS" "ALB target groups configured"
        else
            print_status "FAIL" "No ALB target groups found"
            return 1
        fi
    else
        print_status "FAIL" "ALB not found"
        return 1
    fi
    
    # Test ALB health
    if curl -f -s "https://$domain/api/health" >/dev/null 2>&1; then
        print_status "PASS" "ALB routing is working"
        return 0
    else
        print_status "FAIL" "ALB routing failed"
        return 1
    fi
}

# Function to test AWS production setup
test_aws_production_setup() {
    print_status "INFO" "Testing AWS production setup"
    
    # Check EKS cluster
    local cluster_name=$(aws eks list-clusters --query "clusters[0]" --output text 2>/dev/null)
    if [ -n "$cluster_name" ] && [ "$cluster_name" != "None" ]; then
        print_status "PASS" "EKS cluster found: $cluster_name"
    else
        print_status "FAIL" "EKS cluster not found"
        return 1
    fi
    
    # Check RDS instance
    local rds_instance=$(aws rds describe-db-clusters --query "DBClusters[?contains(DBClusterIdentifier, 'production')].DBClusterIdentifier" --output text 2>/dev/null)
    if [ -n "$rds_instance" ] && [ "$rds_instance" != "None" ]; then
        print_status "PASS" "RDS cluster found: $rds_instance"
    else
        print_status "FAIL" "RDS cluster not found"
        return 1
    fi
    
    # Check S3 bucket
    local s3_bucket="architravetestdb"
    if aws s3 ls "s3://$s3_bucket" >/dev/null 2>&1; then
        print_status "PASS" "S3 bucket accessible: $s3_bucket"
    else
        print_status "FAIL" "S3 bucket not accessible: $s3_bucket"
        return 1
    fi
    
    # Check ECR repositories
    local ecr_repos=$(aws ecr describe-repositories --query "repositories[].repositoryName" --output text 2>/dev/null)
    if echo "$ecr_repos" | grep -q "webapp_dev\|nginx_dev\|rabbitmq_dev"; then
        print_status "PASS" "ECR repositories found"
    else
        print_status "FAIL" "Required ECR repositories not found"
        return 1
    fi
    
    return 0
}

# Function to test S3 access from web app
test_s3_access_from_webapp() {
    local namespace=$1
    local tenant_id=$2
    
    print_status "INFO" "Testing S3 access from web app for tenant $tenant_id"
    
    # Get backend pod
    local pod_name=$(kubectl get pods -n "$namespace" -l "app=$tenant_id-backend" --field-selector=status.phase=Running -o jsonpath='{.items[0].metadata.name}' 2>/dev/null)
    
    if [ -z "$pod_name" ]; then
        print_status "FAIL" "No running backend pod found"
        return 1
    fi
    
    # Test S3 access using AWS CLI in the pod
    if kubectl exec -n "$namespace" "$pod_name" -c backend -- aws s3 ls "s3://architravetestdb/$tenant_id/" >/dev/null 2>&1; then
        print_status "PASS" "S3 access from web app verified"
        return 0
    else
        print_status "FAIL" "S3 access from web app failed"
        return 1
    fi
}

# Function to test RDS database access from web app
test_rds_access_from_webapp() {
    local namespace=$1
    local tenant_id=$2
    
    print_status "INFO" "Testing RDS database access from web app for tenant $tenant_id"
    
    # Get backend pod
    local pod_name=$(kubectl get pods -n "$namespace" -l "app=$tenant_id-backend" --field-selector=status.phase=Running -o jsonpath='{.items[0].metadata.name}' 2>/dev/null)
    
    if [ -z "$pod_name" ]; then
        print_status "FAIL" "No running backend pod found"
        return 1
    fi
    
    # Test database access using the web app's database connection
    if kubectl exec -n "$namespace" "$pod_name" -c backend -- php -r "
        try {
            \$pdo = new PDO('mysql:host=' . getenv('DB_HOST') . ';port=' . getenv('DB_PORT') . ';dbname=' . getenv('DB_NAME'), getenv('DB_USER'), getenv('DB_PASSWORD'));
            \$stmt = \$pdo->query('SELECT 1 as test');
            \$result = \$stmt->fetch();
            if (\$result['test'] == 1) {
                echo 'Database access successful\n';
            } else {
                echo 'Database access failed\n';
                exit(1);
            }
        } catch (Exception \$e) {
            echo 'Database access failed: ' . \$e->getMessage() . '\n';
            exit(1);
        }
    " 2>/dev/null | grep -q "Database access successful"; then
        print_status "PASS" "RDS database access from web app verified"
        return 0
    else
        print_status "FAIL" "RDS database access from web app failed"
        return 1
    fi
}

# Function to test Istio configuration
test_istio_configuration() {
    local namespace=$1
    local tenant_id=$2
    
    print_status "INFO" "Testing Istio configuration for tenant $tenant_id"
    
    # Check if Istio is installed
    if kubectl get namespace istio-system >/dev/null 2>&1; then
        print_status "PASS" "Istio system namespace exists"
    else
        print_status "FAIL" "Istio system namespace not found"
        return 1
    fi
    
    # Check VirtualService
    local virtualservice=$(kubectl get virtualservice -n "$namespace" -o name 2>/dev/null | grep "$tenant_id" || echo "")
    if [ -n "$virtualservice" ]; then
        print_status "PASS" "Istio VirtualService found: $virtualservice"
    else
        print_status "FAIL" "Istio VirtualService not found"
        return 1
    fi
    
    # Check Gateway
    local gateway=$(kubectl get gateway -n istio-system tenant-gateway -o name 2>/dev/null || echo "")
    if [ -n "$gateway" ]; then
        print_status "PASS" "Istio Gateway found: $gateway"
    else
        print_status "FAIL" "Istio Gateway not found"
        return 1
    fi
    
    return 0
}

# Function to test autoscaling configuration
test_autoscaling_configuration() {
    local namespace=$1
    local tenant_id=$2
    
    print_status "INFO" "Testing autoscaling configuration for tenant $tenant_id"
    
    # Check HPA
    local hpa=$(kubectl get hpa -n "$namespace" -o name 2>/dev/null | grep "$tenant_id" || echo "")
    if [ -n "$hpa" ]; then
        print_status "PASS" "HPA found: $hpa"
    else
        print_status "WARN" "HPA not found for tenant"
    fi
    
    # Check VPA
    local vpa=$(kubectl get vpa -n "$namespace" -o name 2>/dev/null | grep "$tenant_id" || echo "")
    if [ -n "$vpa" ]; then
        print_status "PASS" "VPA found: $vpa"
    else
        print_status "WARN" "VPA not found for tenant"
    fi
    
    # Check KEDA ScaledObject
    local scaledobject=$(kubectl get scaledobject -n "$namespace" -o name 2>/dev/null | grep "$tenant_id" || echo "")
    if [ -n "$scaledobject" ]; then
        print_status "PASS" "KEDA ScaledObject found: $scaledobject"
    else
        print_status "WARN" "KEDA ScaledObject not found for tenant"
    fi
    
    return 0
}

# Function to test security configuration
test_security_configuration() {
    local namespace=$1
    local tenant_id=$2
    
    print_status "INFO" "Testing security configuration for tenant $tenant_id"
    
    # Check pod security policies
    local psp=$(kubectl get psp -o name 2>/dev/null | wc -l)
    if [ "$psp" -gt 0 ]; then
        print_status "PASS" "Pod Security Policies configured ($psp found)"
    else
        print_status "WARN" "No Pod Security Policies found"
    fi
    
    # Check RBAC
    local serviceaccount=$(kubectl get serviceaccount -n "$namespace" -o name 2>/dev/null | grep "$tenant_id" || echo "")
    if [ -n "$serviceaccount" ]; then
        print_status "PASS" "ServiceAccount found: $serviceaccount"
    else
        print_status "WARN" "ServiceAccount not found for tenant"
    fi
    
    # Check secrets
    local secrets=$(kubectl get secrets -n "$namespace" -o name 2>/dev/null | grep "$tenant_id" || echo "")
    if [ -n "$secrets" ]; then
        print_status "PASS" "Secrets found for tenant"
    else
        print_status "FAIL" "No secrets found for tenant"
        return 1
    fi
    
    return 0
}

# Main verification function
main() {
    local tenant_id=$(get_tenant_id "$1")
    local namespace="tenant-$tenant_id"
    
    echo -e "${PURPLE}🔍 Comprehensive Tenant Verification Report${NC}"
    echo -e "${PURPLE}============================================${NC}"
    echo -e "${BLUE}Tenant ID:${NC} $tenant_id"
    echo -e "${BLUE}Namespace:${NC} $namespace"
    echo -e "${BLUE}Timestamp:${NC} $(date)"
    echo ""
    
    # Check prerequisites
    print_status "HEADER" "Prerequisites Check"
    if ! command_exists kubectl; then
        print_status "FAIL" "kubectl not found"
        exit 1
    fi
    if ! command_exists aws; then
        print_status "FAIL" "aws CLI not found"
        exit 1
    fi
    if ! command_exists curl; then
        print_status "FAIL" "curl not found"
        exit 1
    fi
    print_status "PASS" "All prerequisites met"
    echo ""
    
    # Check namespace exists
    print_status "HEADER" "Namespace Verification"
    if kubectl get namespace "$namespace" >/dev/null 2>&1; then
        print_status "PASS" "Namespace $namespace exists"
    else
        print_status "FAIL" "Namespace $namespace not found"
        exit 1
    fi
    echo ""
    
    # Check pod status
    print_status "HEADER" "Pod Status Verification"
    check_pod_status "$namespace" "app=$tenant_id-frontend" 1
    check_pod_status "$namespace" "app=$tenant_id-backend" 2  # Backend has 2 containers
    check_pod_status "$namespace" "app=$tenant_id-rabbitmq" 1
    echo ""
    
    # Check service status
    print_status "HEADER" "Service Status Verification"
    check_service_status "$namespace" "$tenant_id-frontend-service"
    check_service_status "$namespace" "$tenant_id-backend-service"
    check_service_status "$namespace" "$tenant_id-rabbitmq-service"
    echo ""
    
    # Check pod-to-pod communication
    print_status "HEADER" "Pod-to-Pod Communication Verification"
    test_pod_communication "$namespace" "$tenant_id-backend" "$tenant_id-frontend-service" 80
    test_pod_communication "$namespace" "$tenant_id-backend" "$tenant_id-rabbitmq-service" 5672
    echo ""
    
    # Check database connectivity
    print_status "HEADER" "Database Connectivity Verification"
    test_database_connectivity "$namespace" "$tenant_id"
    echo ""
    
    # Check RabbitMQ connectivity
    print_status "HEADER" "RabbitMQ Connectivity Verification"
    test_rabbitmq_connectivity "$namespace" "$tenant_id"
    echo ""
    
    # Check SSL/TLS configuration
    print_status "HEADER" "SSL/TLS Configuration Verification"
    test_ssl_tls "$tenant_id"
    echo ""
    
    # Check namespace isolation
    print_status "HEADER" "Namespace Isolation Verification"
    test_namespace_isolation "$namespace" "$tenant_id"
    echo ""
    
    # Check internet connectivity
    print_status "HEADER" "Internet Connectivity Verification"
    test_internet_connectivity "$namespace" "$tenant_id"
    echo ""
    
    # Check web app communication
    print_status "HEADER" "Web App Communication Verification"
    test_webapp_communication "$tenant_id"
    echo ""
    
    # Check ALB configuration
    print_status "HEADER" "ALB Configuration Verification"
    test_alb_configuration "$tenant_id"
    echo ""
    
    # Check AWS production setup
    print_status "HEADER" "AWS Production Setup Verification"
    test_aws_production_setup
    echo ""
    
    # Check S3 access from web app
    print_status "HEADER" "S3 Access from Web App Verification"
    test_s3_access_from_webapp "$namespace" "$tenant_id"
    echo ""
    
    # Check RDS database access from web app
    print_status "HEADER" "RDS Database Access from Web App Verification"
    test_rds_access_from_webapp "$namespace" "$tenant_id"
    echo ""
    
    # Check Istio configuration
    print_status "HEADER" "Istio Configuration Verification"
    test_istio_configuration "$namespace" "$tenant_id"
    echo ""
    
    # Check autoscaling configuration
    print_status "HEADER" "Autoscaling Configuration Verification"
    test_autoscaling_configuration "$namespace" "$tenant_id"
    echo ""
    
    # Check security configuration
    print_status "HEADER" "Security Configuration Verification"
    test_security_configuration "$namespace" "$tenant_id"
    echo ""
    
    # Summary
    echo -e "${PURPLE}📊 Verification Summary${NC}"
    echo -e "${PURPLE}=====================${NC}"
    echo -e "${BLUE}Tenant ID:${NC} $tenant_id"
    echo -e "${BLUE}Namespace:${NC} $namespace"
    echo -e "${BLUE}Verification completed:${NC} $(date)"
    echo ""
    echo -e "${GREEN}✅ All critical components verified successfully!${NC}"
}

# Run main function with command line argument
main "$@" 