global:
  resolve_timeout: 5m
  slack_api_url: '${slack_webhook_url}'
  pagerduty_url: 'https://events.pagerduty.com/v2/enqueue'

route:
  group_by: ['alertname', 'job', 'severity']
  group_wait: 30s
  group_interval: 5m
  repeat_interval: 12h
  receiver: 'slack-notifications'
  routes:
  - match:
      severity: critical
    receiver: 'pagerduty-critical'
    continue: true
  - match:
      severity: warning
    receiver: 'slack-notifications'
    continue: true
  - match:
      severity: info
    receiver: 'slack-notifications'
    continue: true

receivers:
- name: 'slack-notifications'
  slack_configs:
  - channel: '${slack_channel}'
    send_resolved: true
    title: '[{{ .Status | toUpper }}{{ if eq .Status "firing" }}:{{ .Alerts.Firing | len }}{{ end }}] ${environment} Monitoring Alert'
    text: >-
      {{ range .Alerts }}
        *Alert:* {{ .Labels.alertname }}{{ if .Labels.severity }} - `{{ .Labels.severity }}`{{ end }}
        *Description:* {{ .Annotations.description }}
        *Details:*
        {{ range .Labels.SortedPairs }} • *{{ .Name }}:* `{{ .Value }}`
        {{ end }}
      {{ end }}
    icon_emoji: '{{ if eq .Status "firing" }}:fire:{{ else }}:ok:{{ end }}'
    username: 'Prometheus'

- name: 'pagerduty-critical'
  pagerduty_configs:
  - service_key: '${pagerduty_service_key}'
    send_resolved: true
    description: '{{ .CommonLabels.alertname }}'
    client: 'Prometheus'
    client_url: 'https://prometheus.${environment}.example.com'
    details:
      firing: '{{ .Alerts.Firing | len }}'
      status: '{{ .Status }}'
      instance: '{{ .CommonLabels.instance }}'
      job: '{{ .CommonLabels.job }}'
      description: '{{ .CommonAnnotations.description }}'

inhibit_rules:
- source_match:
    severity: 'critical'
  target_match:
    severity: 'warning'
  equal: ['alertname', 'instance']
