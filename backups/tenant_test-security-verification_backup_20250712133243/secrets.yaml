apiVersion: v1
items:
- apiVersion: v1
  data:
    DB_HOST: cHJvZHVjdGlvbi1hdXJvcmEtc2VydmVybGVzcy5jbHVzdGVyLWNwbWFnd2tpMmt2OC5ldS1jZW50cmFsLTEucmRzLmFtYXpvbmF3cy5jb20=
    DB_NAME: ""
    DB_PASSWORD: ""
    DB_PORT: MzMwNg==
    DB_SSL: dHJ1ZQ==
    DB_SSL_CA: L3RtcC9yZHMtY2EtMjAxOS1yb290LnBlbQ==
    DB_SSL_VERIFY: ZmFsc2U=
    DB_USER: ""
  kind: Secret
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"v1","kind":"Secret","metadata":{"annotations":{},"name":"test-security-verification-db-secret","namespace":"tenant-test-security-verification"},"stringData":{"DB_HOST":"production-aurora-serverless.cluster-cpmagwki2kv8.eu-central-1.rds.amazonaws.com","DB_NAME":"","DB_PASSWORD":"","DB_PORT":"3306","DB_SSL":"true","DB_SSL_CA":"/tmp/rds-ca-2019-root.pem","DB_SSL_VERIFY":"false","DB_USER":""},"type":"Opaque"}
    creationTimestamp: "2025-07-12T10:41:59Z"
    name: test-security-verification-db-secret
    namespace: tenant-test-security-verification
    resourceVersion: "24761050"
    uid: 029211fb-c664-4665-ac83-6e569c04783a
  type: Opaque
- apiVersion: v1
  data:
    DB_HOST: cHJvZHVjdGlvbi1hdXJvcmEtc2VydmVybGVzcy5jbHVzdGVyLWNwbWFnd2tpMmt2OC5ldS1jZW50cmFsLTEucmRzLmFtYXpvbmF3cy5jb20=
    DB_NAME: YXJjaGl0cmF2ZQ==
    DB_PASSWORD: JkJaellfPEFLKD1hKlVoWg==
    DB_PORT: MzMwNg==
    DB_SSL: dHJ1ZQ==
    DB_SSL_CA: L3RtcC9yZHMtY2EtMjAxOS1yb290LnBlbQ==
    DB_SSL_MODE: UkVRVUlSRUQ=
    DB_USER: YWRtaW4=
  kind: Secret
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"v1","data":{"DB_HOST":"cHJvZHVjdGlvbi1hdXJvcmEtc2VydmVybGVzcy5jbHVzdGVyLWNwbWFnd2tpMmt2OC5ldS1jZW50cmFsLTEucmRzLmFtYXpvbmF3cy5jb20=","DB_NAME":"YXJjaGl0cmF2ZQ==","DB_PASSWORD":"JkJaellfPEFLKD1hKlVoWg==","DB_PORT":"MzMwNg==","DB_SSL":"dHJ1ZQ==","DB_SSL_CA":"L3RtcC9yZHMtY2EtMjAxOS1yb290LnBlbQ==","DB_SSL_MODE":"UkVRVUlSRUQ=","DB_USER":"YWRtaW4="},"kind":"Secret","metadata":{"annotations":{},"creationTimestamp":null,"name":"test-security-verification-secret","namespace":"tenant-test-security-verification"}}
    creationTimestamp: "2025-07-12T10:40:12Z"
    name: test-security-verification-secret
    namespace: tenant-test-security-verification
    resourceVersion: "24760307"
    uid: b76f6103-ef93-427f-8d6e-1a2bb684bada
  type: Opaque
kind: List
metadata:
  resourceVersion: ""
