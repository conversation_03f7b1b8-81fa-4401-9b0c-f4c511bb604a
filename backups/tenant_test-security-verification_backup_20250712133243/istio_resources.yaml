apiVersion: v1
items:
- apiVersion: networking.istio.io/v1beta1
  kind: VirtualService
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"networking.istio.io/v1beta1","kind":"VirtualService","metadata":{"annotations":{},"labels":{"managed-by":"tenant-onboarding","tenant":"test-security-verification"},"name":"tenant-test-security-verification-vs","namespace":"tenant-test-security-verification"},"spec":{"gateways":["istio-system/tenant-gateway"],"hosts":["test-security-verification.architrave-assets.com"],"http":[{"headers":{"request":{"set":{"X-Forwarded-Proto":"https"}}},"match":[{"uri":{"prefix":"/api"}}],"retries":{"attempts":3,"perTryTimeout":"10s","retryOn":"gateway-error,connect-failure,refused-stream"},"route":[{"destination":{"host":"test-security-verification-backend-service","port":{"number":8080}}}],"timeout":"30s"},{"headers":{"request":{"set":{"X-Forwarded-Proto":"https"}}},"retries":{"attempts":2,"perTryTimeout":"5s","retryOn":"gateway-error,connect-failure,refused-stream"},"route":[{"destination":{"host":"test-security-verification-frontend-service","port":{"number":80}}}],"timeout":"15s"}]}}
    creationTimestamp: "2025-07-12T10:43:13Z"
    generation: 1
    labels:
      managed-by: tenant-onboarding
      tenant: test-security-verification
    name: tenant-test-security-verification-vs
    namespace: tenant-test-security-verification
    resourceVersion: "24761639"
    uid: bb54e570-5767-4f39-8795-423d762797fa
  spec:
    gateways:
    - istio-system/tenant-gateway
    hosts:
    - test-security-verification.architrave-assets.com
    http:
    - headers:
        request:
          set:
            X-Forwarded-Proto: https
      match:
      - uri:
          prefix: /api
      retries:
        attempts: 3
        perTryTimeout: 10s
        retryOn: gateway-error,connect-failure,refused-stream
      route:
      - destination:
          host: test-security-verification-backend-service
          port:
            number: 8080
      timeout: 30s
    - headers:
        request:
          set:
            X-Forwarded-Proto: https
      retries:
        attempts: 2
        perTryTimeout: 5s
        retryOn: gateway-error,connect-failure,refused-stream
      route:
      - destination:
          host: test-security-verification-frontend-service
          port:
            number: 80
      timeout: 15s
kind: List
metadata:
  resourceVersion: ""
