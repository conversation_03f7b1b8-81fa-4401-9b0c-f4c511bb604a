apiVersion: v1
items:
- apiVersion: v1
  data:
    root-cert.pem: |
      -----BEGIN CERTIFICATE-----
      MIIC/DCCAeSgAwIBAgIQXfni3UolXFGMB7n3Gz1PtzANBgkqhkiG9w0BAQsFADAY
      MRYwFAYDVQQKEw1jbHVzdGVyLmxvY2FsMB4XDTI1MDUxOTExNTcyNVoXDTM1MDUx
      NzExNTcyNVowGDEWMBQGA1UEChMNY2x1c3Rlci5sb2NhbDCCASIwDQYJKoZIhvcN
      AQEBBQADggEPADCCAQoCggEBAM+87t5Oblkhucrq6yNPmqYj3DcoYaUFyyVXZYYv
      hkJt1jSiYmI05qIHEOqLDwWOMVkccoyojrxkpD2xZFb+j9PHKp3TJW8nAd6zLvbb
      wJ1PD53yjCyNiGnynfsrIV9k5YbntqY1AHJkw2BhPiKZtHPF2SWubOJSmkCdNMIW
      9YhDMS8+GWflRn7Z1KK1EDL+SJTmPaGY5K/FdPRCyCm9jX70wMh2KmlAQ9K1cP3J
      0YYmunhVoss37P+rU+T66uVV0GEjDYzTI4/PuV7gBx86U1/b3skyfIBHndCusQdt
      JrbIed7l6lPBZfbVjdbM6ablZh1gleWJSRbdeiHaYcK2Xm8CAwEAAaNCMEAwDgYD
      VR0PAQH/BAQDAgIEMA8GA1UdEwEB/wQFMAMBAf8wHQYDVR0OBBYEFB5Oxg5+5K80
      Ul6m6kcqTddYX8nkMA0GCSqGSIb3DQEBCwUAA4IBAQA3027Zh6Gg24ReIC5I+pOD
      e4aq+xT0kWXNefHX5ds+PI+zk1fQoVMJhtvsEyxLk9/VJx32BXlxyMP2ewUBAZFw
      YGluXfStWU+HIarUrYzrW4kKKw0vZeKN3mjP4k2T0Zmd7XAprzBuGpCzeU4nCAvj
      R+3YS8QOQ5rqYrO23tM6ZGA2mE6Omnc0pTAUs3hdRuTVI2GiWxuXEk+uDZzLuMeb
      elIl/7l+j3fVit+FyO1vA7LP7++xMHyDuP4ylpNkyB5vI+ZSA4qWKa8stW+QMPbL
      lKfVTnXBS7W2MuVdobv2qery8dbZswTRyiOUx6aL2lC+dSrwjUlHv8TJb5ntichn
      -----END CERTIFICATE-----
  kind: ConfigMap
  metadata:
    creationTimestamp: "2025-07-12T10:39:53Z"
    labels:
      istio.io/config: "true"
    name: istio-ca-root-cert
    namespace: tenant-test-security-verification
    resourceVersion: "24760169"
    uid: 971fdcd4-e645-4a83-bfd0-ef900a39afcd
- apiVersion: v1
  data:
    ca.crt: |
      -----BEGIN CERTIFICATE-----
      MIIDBTCCAe2gAwIBAgIIeqdINANIu1EwDQYJKoZIhvcNAQELBQAwFTETMBEGA1UE
      AxMKa3ViZXJuZXRlczAeFw0yNTA1MTkxMTA3MzJaFw0zNTA1MTcxMTEyMzJaMBUx
      EzARBgNVBAMTCmt1YmVybmV0ZXMwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEK
      AoIBAQDdBUGgHk+uRWRI/xF/BBGl+wZemobH2q1Bt6CyFXBxU7yLsjy10e8uAxuQ
      VQji+IwL2yItJXF7PPhjv2A8gQJxZoxgPtaRJ3zg8Yq6mn5d4U8HwCIV8b3xkyW5
      8hFRbDogT9TIIncuhGDciAw4Be1Y+XS/21HumtJe9tJTTod369DynvCn8YSQDX/p
      YRvvAXssjqHFr1u32w7xarNZ1TnN+cA6LzckL6UYZByjBnyzIL5bkAXpyCuH+agl
      vcXosIfgWT9JA0WU8oTY9fOvxol0saMu42FyxcbxnQTLvhG8enuY5bu1lF7NVuk/
      Z/7ovm+X5V/KbvR8sHoLgpdvAXpVAgMBAAGjWTBXMA4GA1UdDwEB/wQEAwICpDAP
      BgNVHRMBAf8EBTADAQH/MB0GA1UdDgQWBBSFA+meGiVoz61wd/Q5WM1L7kPL4DAV
      BgNVHREEDjAMggprdWJlcm5ldGVzMA0GCSqGSIb3DQEBCwUAA4IBAQAo4w0PJk6K
      OsSVuZaofBQFV2otZbMYuY8TY0eSI6nqg9RIfkPM0ugUJDUriL1T9XxQArfxAlRy
      RLBXTMf4mERUY5YfLSBIXqt3xKig9oxflvdWU3+ej/8R5tCjH4dqJILXOXce/ITg
      ifnosF33zqJvnIBJlG0gScBODW1GmCQU7VuRTPy0LuOPTa5G+wOzIwu/w5arvEwU
      8mKM950z+kY2RfpkdU5sG+y9vc1DQ2aAzn2bdrhQqLkLoy023lSojRinXnYZbx6N
      nE/uQqJZkdQKgNhXz+j4GpiqBkH1s9R1H2IHq7r6ChilRKjIHsOiZJM+th2/8Zy1
      QOOTHWYuRCAq
      -----END CERTIFICATE-----
  kind: ConfigMap
  metadata:
    annotations:
      kubernetes.io/description: Contains a CA bundle that can be used to verify the
        kube-apiserver when using internal endpoints such as the internal service
        IP or kubernetes.default.svc. No other usage is guaranteed across distributions
        of Kubernetes clusters.
    creationTimestamp: "2025-07-12T10:39:53Z"
    name: kube-root-ca.crt
    namespace: tenant-test-security-verification
    resourceVersion: "24760168"
    uid: 639ad647-fd21-4fa5-a57a-8d41f2bd7f81
- apiVersion: v1
  data:
    DOMAIN: architrave-assets.com
    ENVIRONMENT: production
    LANGUAGE: en
    TENANT_ID: test-security-verification
  kind: ConfigMap
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"v1","data":{"DOMAIN":"architrave-assets.com","ENVIRONMENT":"production","LANGUAGE":"en","TENANT_ID":"test-security-verification"},"kind":"ConfigMap","metadata":{"annotations":{},"creationTimestamp":null,"name":"test-security-verification-config","namespace":"tenant-test-security-verification"}}
    creationTimestamp: "2025-07-12T10:40:11Z"
    name: test-security-verification-config
    namespace: tenant-test-security-verification
    resourceVersion: "24760302"
    uid: a0e97355-1590-4d84-9356-4616ac9de17c
kind: List
metadata:
  resourceVersion: ""
