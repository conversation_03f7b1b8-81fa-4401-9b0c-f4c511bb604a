apiVersion: v1
items:
- apiVersion: v1
  data:
    DB_HOST: cHJvZHVjdGlvbi1hdXJvcmEtc2VydmVybGVzcy5jbHVzdGVyLWNwbWFnd2tpMmt2OC5ldS1jZW50cmFsLTEucmRzLmFtYXpvbmF3cy5jb20=
    DB_NAME: YXJjaGl0cmF2ZQ==
    DB_PASSWORD: JkJaellfPEFLKD1hKlVoWg==
    DB_PORT: MzMwNg==
    DB_SSL_CA: L3RtcC9yZHMtY2EtMjAxOS1yb290LnBlbQ==
    DB_SSL_MODE: UkVRVUlSRUQ=
    DB_USER: YWRtaW4=
  kind: Secret
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"v1","data":{"DB_HOST":"cHJvZHVjdGlvbi1hdXJvcmEtc2VydmVybGVzcy5jbHVzdGVyLWNwbWFnd2tpMmt2OC5ldS1jZW50cmFsLTEucmRzLmFtYXpvbmF3cy5jb20=","DB_NAME":"YXJjaGl0cmF2ZQ==","DB_PASSWORD":"JkJaellfPEFLKD1hKlVoWg==","DB_PORT":"MzMwNg==","DB_SSL_CA":"L3RtcC9yZHMtY2EtMjAxOS1yb290LnBlbQ==","DB_SSL_MODE":"UkVRVUlSRUQ=","DB_USER":"YWRtaW4="},"kind":"Secret","metadata":{"annotations":{},"creationTimestamp":null,"name":"test-cycle-01-secret","namespace":"tenant-test-cycle-01"}}
    creationTimestamp: "2025-07-05T16:01:48Z"
    name: test-cycle-01-secret
    namespace: tenant-test-cycle-01
    resourceVersion: "20786621"
    uid: 5130e3fe-bf1d-44c0-ab74-05293d71d858
  type: Opaque
kind: List
metadata:
  resourceVersion: ""
