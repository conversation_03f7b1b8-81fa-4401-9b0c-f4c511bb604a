apiVersion: v1
items:
- apiVersion: v1
  data:
    DB_HOST: cHJvZHVjdGlvbi1hdXJvcmEtc2VydmVybGVzcy5jbHVzdGVyLWNwbWFnd2tpMmt2OC5ldS1jZW50cmFsLTEucmRzLmFtYXpvbmF3cy5jb20=
    DB_NAME: YXJjaGl0cmF2ZQ==
    DB_PASSWORD: JkJaellfPEFLKD1hKlVoWg==
    DB_PORT: MzMwNg==
    DB_SSL: dHJ1ZQ==
    DB_SSL_CA: L3RtcC9yZHMtY2EtMjAxOS1yb290LnBlbQ==
    DB_SSL_MODE: UkVRVUlSRUQ=
    DB_SSL_VERIFY: ZmFsc2U=
    DB_USER: YWRtaW4=
  kind: Secret
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"v1","data":{"DB_HOST":"cHJvZHVjdGlvbi1hdXJvcmEtc2VydmVybGVzcy5jbHVzdGVyLWNwbWFnd2tpMmt2OC5ldS1jZW50cmFsLTEucmRzLmFtYXpvbmF3cy5jb20=","DB_NAME":"YXJjaGl0cmF2ZQ==","DB_PASSWORD":"JkJaellfPEFLKD1hKlVoWg==","DB_PORT":"MzMwNg==","DB_SSL":"dHJ1ZQ==","DB_SSL_CA":"L3RtcC9yZHMtY2EtMjAxOS1yb290LnBlbQ==","DB_SSL_MODE":"UkVRVUlSRUQ=","DB_SSL_VERIFY":"ZmFsc2U=","DB_USER":"YWRtaW4="},"kind":"Secret","metadata":{"annotations":{},"creationTimestamp":null,"name":"auto-tenant-1752314374-db-secret","namespace":"tenant-auto-tenant-1752314374"}}
    creationTimestamp: "2025-07-12T10:00:20Z"
    name: auto-tenant-1752314374-db-secret
    namespace: tenant-auto-tenant-1752314374
    resourceVersion: "24744768"
    uid: 5d8d854e-e5be-45c8-9ca7-62b9aedb4daa
  type: Opaque
kind: List
metadata:
  resourceVersion: ""
