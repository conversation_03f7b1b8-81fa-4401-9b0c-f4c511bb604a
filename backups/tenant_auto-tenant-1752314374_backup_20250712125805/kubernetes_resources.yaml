apiVersion: v1
items:
- apiVersion: v1
  kind: Service
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"v1","kind":"Service","metadata":{"annotations":{},"labels":{"app":"auto-tenant-1752314374-rabbitmq-mgmt","component":"rabbitmq-mgmt","tenant":"auto-tenant-1752314374"},"name":"auto-tenant-1752314374-rabbitmq-mgmt-service","namespace":"tenant-auto-tenant-1752314374"},"spec":{"ports":[{"port":15672,"protocol":"TCP","targetPort":15672}],"selector":{"app":"auto-tenant-1752314374-rabbitmq-mgmt","component":"rabbitmq-mgmt","tenant":"auto-tenant-1752314374"},"type":"ClusterIP"}}
    creationTimestamp: "2025-07-12T10:00:23Z"
    labels:
      app: auto-tenant-1752314374-rabbitmq-mgmt
      component: rabbitmq-mgmt
      tenant: auto-tenant-1752314374
    name: auto-tenant-1752314374-rabbitmq-mgmt-service
    namespace: tenant-auto-tenant-1752314374
    resourceVersion: "24744806"
    uid: d283968e-94c4-45a4-be45-12ed43dde445
  spec:
    clusterIP: **************
    clusterIPs:
    - **************
    internalTrafficPolicy: Cluster
    ipFamilies:
    - IPv4
    ipFamilyPolicy: SingleStack
    ports:
    - port: 15672
      protocol: TCP
      targetPort: 15672
    selector:
      app: auto-tenant-1752314374-rabbitmq-mgmt
      component: rabbitmq-mgmt
      tenant: auto-tenant-1752314374
    sessionAffinity: None
    type: ClusterIP
  status:
    loadBalancer: {}
- apiVersion: v1
  kind: Service
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"v1","kind":"Service","metadata":{"annotations":{},"labels":{"app":"auto-tenant-1752314374-rabbitmq","component":"rabbitmq","tenant":"auto-tenant-1752314374"},"name":"auto-tenant-1752314374-rabbitmq-service","namespace":"tenant-auto-tenant-1752314374"},"spec":{"ports":[{"port":5672,"protocol":"TCP","targetPort":5672}],"selector":{"app":"auto-tenant-1752314374-rabbitmq","component":"rabbitmq","tenant":"auto-tenant-1752314374"},"type":"ClusterIP"}}
    creationTimestamp: "2025-07-12T10:00:22Z"
    labels:
      app: auto-tenant-1752314374-rabbitmq
      component: rabbitmq
      tenant: auto-tenant-1752314374
    name: auto-tenant-1752314374-rabbitmq-service
    namespace: tenant-auto-tenant-1752314374
    resourceVersion: "24744795"
    uid: dfeae6d2-6d3c-4e64-bb3f-f2052b845fe4
  spec:
    clusterIP: *************
    clusterIPs:
    - *************
    internalTrafficPolicy: Cluster
    ipFamilies:
    - IPv4
    ipFamilyPolicy: SingleStack
    ports:
    - port: 5672
      protocol: TCP
      targetPort: 5672
    selector:
      app: auto-tenant-1752314374-rabbitmq
      component: rabbitmq
      tenant: auto-tenant-1752314374
    sessionAffinity: None
    type: ClusterIP
  status:
    loadBalancer: {}
kind: List
metadata:
  resourceVersion: ""
