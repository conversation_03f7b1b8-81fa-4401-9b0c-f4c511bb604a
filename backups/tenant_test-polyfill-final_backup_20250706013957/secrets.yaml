apiVersion: v1
items:
- apiVersion: v1
  data:
    DB_HOST: cHJvZHVjdGlvbi1hdXJvcmEtc2VydmVybGVzcy5jbHVzdGVyLWNwbWFnd2tpMmt2OC5ldS1jZW50cmFsLTEucmRzLmFtYXpvbmF3cy5jb20=
    DB_NAME: YXJjaGl0cmF2ZQ==
    DB_PASSWORD: JkJaellfPEFLKD1hKlVoWg==
    DB_PORT: MzMwNg==
    DB_SSL_CA: L3RtcC9yZHMtY2EtMjAxOS1yb290LnBlbQ==
    DB_SSL_MODE: UkVRVUlSRUQ=
    DB_USER: YWRtaW4=
  kind: Secret
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"v1","data":{"DB_HOST":"cHJvZHVjdGlvbi1hdXJvcmEtc2VydmVybGVzcy5jbHVzdGVyLWNwbWFnd2tpMmt2OC5ldS1jZW50cmFsLTEucmRzLmFtYXpvbmF3cy5jb20=","DB_NAME":"YXJjaGl0cmF2ZQ==","DB_PASSWORD":"JkJaellfPEFLKD1hKlVoWg==","DB_PORT":"MzMwNg==","DB_SSL_CA":"L3RtcC9yZHMtY2EtMjAxOS1yb290LnBlbQ==","DB_SSL_MODE":"UkVRVUlSRUQ=","DB_USER":"YWRtaW4="},"kind":"Secret","metadata":{"annotations":{},"creationTimestamp":null,"name":"test-polyfill-final-secret","namespace":"tenant-test-polyfill-final"}}
    creationTimestamp: "2025-07-05T14:20:14Z"
    name: test-polyfill-final-secret
    namespace: tenant-test-polyfill-final
    resourceVersion: "20784099"
    uid: 5c751242-f134-458f-b320-06ed6d172bb3
  type: Opaque
kind: List
metadata:
  resourceVersion: ""
