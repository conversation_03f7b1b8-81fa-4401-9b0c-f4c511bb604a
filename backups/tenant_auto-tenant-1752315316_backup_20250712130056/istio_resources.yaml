apiVersion: v1
items:
- apiVersion: networking.istio.io/v1beta1
  kind: VirtualService
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"networking.istio.io/v1beta1","kind":"VirtualService","metadata":{"annotations":{},"labels":{"managed-by":"tenant-onboarding","tenant":"auto-tenant-1752315316"},"name":"tenant-auto-tenant-1752315316-vs","namespace":"tenant-auto-tenant-1752315316"},"spec":{"gateways":["istio-system/tenant-gateway"],"hosts":["auto-tenant-1752315316.architrave-assets.com"],"http":[{"headers":{"request":{"set":{"X-Forwarded-Proto":"https"}}},"match":[{"uri":{"prefix":"/api"}}],"retries":{"attempts":3,"perTryTimeout":"10s","retryOn":"gateway-error,connect-failure,refused-stream"},"route":[{"destination":{"host":"auto-tenant-1752315316-backend-service","port":{"number":8080}}}],"timeout":"30s"},{"headers":{"request":{"set":{"X-Forwarded-Proto":"https"}}},"retries":{"attempts":2,"perTryTimeout":"5s","retryOn":"gateway-error,connect-failure,refused-stream"},"route":[{"destination":{"host":"auto-tenant-1752315316-frontend-service","port":{"number":80}}}],"timeout":"15s"}]}}
    creationTimestamp: "2025-07-12T10:19:11Z"
    generation: 1
    labels:
      managed-by: tenant-onboarding
      tenant: auto-tenant-1752315316
    name: tenant-auto-tenant-1752315316-vs
    namespace: tenant-auto-tenant-1752315316
    resourceVersion: "24752151"
    uid: 9c3a7b8d-a79a-4e49-bb32-0e8677867e25
  spec:
    gateways:
    - istio-system/tenant-gateway
    hosts:
    - auto-tenant-1752315316.architrave-assets.com
    http:
    - headers:
        request:
          set:
            X-Forwarded-Proto: https
      match:
      - uri:
          prefix: /api
      retries:
        attempts: 3
        perTryTimeout: 10s
        retryOn: gateway-error,connect-failure,refused-stream
      route:
      - destination:
          host: auto-tenant-1752315316-backend-service
          port:
            number: 8080
      timeout: 30s
    - headers:
        request:
          set:
            X-Forwarded-Proto: https
      retries:
        attempts: 2
        perTryTimeout: 5s
        retryOn: gateway-error,connect-failure,refused-stream
      route:
      - destination:
          host: auto-tenant-1752315316-frontend-service
          port:
            number: 80
      timeout: 15s
kind: List
metadata:
  resourceVersion: ""
