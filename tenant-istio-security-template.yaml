apiVersion: security.istio.io/v1beta1
kind: AuthorizationPolicy
metadata:
  name: {{TENANT_ID}}-access-control
  namespace: tenant-{{TENANT_ID}}
  labels:
    app.kubernetes.io/managed-by: advanced-tenant-onboard
    tenant: {{TENANT_ID}}
spec:
  # Apply to all workloads in this namespace
  selector: {}
  
  rules:
  # Allow traffic from Istio Gateway ONLY for this tenant's domain
  - from:
    - source:
        namespaces: ["istio-system"]
    to:
    - operation:
        hosts: ["{{TENANT_ID}}.architrave-assets.com"]
    when:
    - key: request.headers[host]
      values: ["{{TENANT_ID}}.architrave-assets.com"]
  
  # Allow intra-namespace communication
  - from:
    - source:
        namespaces: ["tenant-{{TENANT_ID}}"]
  
  # Deny everything else (implicit)
---
apiVersion: security.istio.io/v1beta1
kind: PeerAuthentication
metadata:
  name: {{TENANT_ID}}-mtls
  namespace: tenant-{{TENANT_ID}}
  labels:
    app.kubernetes.io/managed-by: advanced-tenant-onboard
    tenant: {{TENANT_ID}}
spec:
  # Require mTLS for all workloads in this namespace
  mtls:
    mode: STRICT
---
apiVersion: networking.istio.io/v1beta1
kind: DestinationRule
metadata:
  name: {{TENANT_ID}}-mtls-destination
  namespace: tenant-{{TENANT_ID}}
  labels:
    app.kubernetes.io/managed-by: advanced-tenant-onboard
    tenant: {{TENANT_ID}}
spec:
  host: "*.tenant-{{TENANT_ID}}.svc.cluster.local"
  trafficPolicy:
    tls:
      mode: ISTIO_MUTUAL  # Enforce mTLS
---
apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: {{TENANT_ID}}-rate-limiting
  namespace: tenant-{{TENANT_ID}}
  labels:
    app.kubernetes.io/managed-by: advanced-tenant-onboard
    tenant: {{TENANT_ID}}
spec:
  hosts:
  - {{TENANT_ID}}.architrave-assets.com
  gateways:
  - istio-system/tenant-gateway
  http:
  # Rate limiting per tenant
  - match:
    - headers:
        host:
          exact: {{TENANT_ID}}.architrave-assets.com
    route:
    - destination:
        host: {{TENANT_ID}}-frontend-service.tenant-{{TENANT_ID}}.svc.cluster.local
        port:
          number: 80
    fault:
      delay:
        percentage:
          value: 0.1  # 0.1% of requests get delayed (circuit breaker)
        fixedDelay: 5s
    timeout: 30s
    retries:
      attempts: 3
      perTryTimeout: 10s
