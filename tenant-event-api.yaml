apiVersion: v1
kind: ConfigMap
metadata:
  name: tenant-event-api
  namespace: tenant-management
  labels:
    app: tenant-event-api
    component: backend
data:
  app.py: |
    #!/usr/bin/env python3
    """
    Tenant Event Management API
    
    This API provides comprehensive tenant event tracking and management
    for 100+ tenants with automated storage and retrieval capabilities.
    """
    
    import os
    import json
    import logging
    from datetime import datetime, timedelta
    from typing import List, Dict, Optional
    
    import mysql.connector
    from flask import Flask, request, jsonify, Response
    from flask_cors import CORS
    import redis
    
    # Configure logging
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)
    
    app = Flask(__name__)
    CORS(app)
    
    # Database configuration
    DB_CONFIG = {
        'host': os.getenv('DB_HOST', 'tenant-event-db'),
        'port': int(os.getenv('DB_PORT', '3306')),
        'user': os.getenv('DB_USER', 'tenant_admin'),
        'password': os.getenv('DB_PASSWORD', 'secure_password_123'),
        'database': os.getenv('DB_NAME', 'tenant_management'),
        'charset': 'utf8mb4',
        'autocommit': True
    }
    
    # Redis configuration for caching
    REDIS_CONFIG = {
        'host': os.getenv('REDIS_HOST', 'redis'),
        'port': int(os.getenv('REDIS_PORT', '6379')),
        'db': 0,
        'decode_responses': True
    }
    
    def get_db_connection():
        """Get database connection with retry logic."""
        try:
            conn = mysql.connector.connect(**DB_CONFIG)
            return conn
        except Exception as e:
            logger.error(f"Database connection failed: {e}")
            raise
    
    def get_redis_connection():
        """Get Redis connection for caching."""
        try:
            return redis.Redis(**REDIS_CONFIG)
        except Exception as e:
            logger.warning(f"Redis connection failed: {e}")
            return None
    
    @app.route('/health', methods=['GET'])
    def health_check():
        """Health check endpoint."""
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            cursor.execute("SELECT 1")
            cursor.fetchone()
            cursor.close()
            conn.close()
            
            return jsonify({
                'status': 'healthy',
                'timestamp': datetime.utcnow().isoformat(),
                'database': 'connected'
            })
        except Exception as e:
            return jsonify({
                'status': 'unhealthy',
                'timestamp': datetime.utcnow().isoformat(),
                'error': str(e)
            }), 500
    
    @app.route('/api/tenants', methods=['GET'])
    def get_tenants():
        """Get all tenants with optional filtering."""
        try:
            conn = get_db_connection()
            cursor = conn.cursor(dictionary=True)
            
            # Build query with filters
            where_conditions = []
            params = []
            
            status = request.args.get('status')
            tier = request.args.get('tier')
            search = request.args.get('search')
            limit = int(request.args.get('limit', 100))
            offset = int(request.args.get('offset', 0))
            
            if status:
                where_conditions.append("status = %s")
                params.append(status)
            
            if tier:
                where_conditions.append("tier = %s")
                params.append(tier)
            
            if search:
                where_conditions.append("(name LIKE %s OR display_name LIKE %s OR id LIKE %s)")
                search_param = f"%{search}%"
                params.extend([search_param, search_param, search_param])
            
            where_clause = " WHERE " + " AND ".join(where_conditions) if where_conditions else ""
            
            # Get total count
            count_query = f"SELECT COUNT(*) as total FROM tenants{where_clause}"
            cursor.execute(count_query, params)
            total_count = cursor.fetchone()['total']
            
            # Get tenants with pagination
            query = f"""
                SELECT t.*, 
                       tc.hourly_cost, tc.daily_cost, tc.monthly_cost, tc.yearly_cost,
                       COUNT(DISTINCT te.id) as total_events,
                       MAX(te.initiated_at) as last_event_at
                FROM tenants t
                LEFT JOIN tenant_current_costs tc ON t.id = tc.tenant_id
                LEFT JOIN tenant_events te ON t.id = te.tenant_id
                {where_clause}
                GROUP BY t.id
                ORDER BY t.created_at DESC
                LIMIT %s OFFSET %s
            """
            params.extend([limit, offset])
            
            cursor.execute(query, params)
            tenants = cursor.fetchall()
            
            # Get resource information for each tenant
            for tenant in tenants:
                cursor.execute("""
                    SELECT resource_type, allocated_amount, used_amount, unit
                    FROM tenant_resources 
                    WHERE tenant_id = %s
                """, (tenant['id'],))
                resources = cursor.fetchall()
                tenant['resources'] = {r['resource_type']: r for r in resources}
                
                # Get latest performance metrics
                cursor.execute("""
                    SELECT metric_name, metric_value, metric_unit
                    FROM tenant_performance 
                    WHERE tenant_id = %s 
                    AND recorded_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
                    ORDER BY recorded_at DESC
                """, (tenant['id'],))
                metrics = cursor.fetchall()
                tenant['performance'] = {m['metric_name']: m for m in metrics}
            
            cursor.close()
            conn.close()
            
            return jsonify({
                'tenants': tenants,
                'pagination': {
                    'total': total_count,
                    'limit': limit,
                    'offset': offset,
                    'has_more': offset + limit < total_count
                }
            })
            
        except Exception as e:
            logger.error(f"Error fetching tenants: {e}")
            return jsonify({'error': str(e)}), 500
    
    @app.route('/api/tenants/<tenant_id>/events', methods=['GET'])
    def get_tenant_events(tenant_id):
        """Get events for a specific tenant."""
        try:
            conn = get_db_connection()
            cursor = conn.cursor(dictionary=True)
            
            limit = int(request.args.get('limit', 50))
            offset = int(request.args.get('offset', 0))
            event_type = request.args.get('event_type')
            
            where_conditions = ["tenant_id = %s"]
            params = [tenant_id]
            
            if event_type:
                where_conditions.append("event_type = %s")
                params.append(event_type)
            
            where_clause = " AND ".join(where_conditions)
            
            query = f"""
                SELECT * FROM tenant_events 
                WHERE {where_clause}
                ORDER BY initiated_at DESC
                LIMIT %s OFFSET %s
            """
            params.extend([limit, offset])
            
            cursor.execute(query, params)
            events = cursor.fetchall()
            
            # Convert datetime objects to ISO format
            for event in events:
                if event['initiated_at']:
                    event['initiated_at'] = event['initiated_at'].isoformat()
                if event['completed_at']:
                    event['completed_at'] = event['completed_at'].isoformat()
            
            cursor.close()
            conn.close()
            
            return jsonify({'events': events})
            
        except Exception as e:
            logger.error(f"Error fetching tenant events: {e}")
            return jsonify({'error': str(e)}), 500
    
    @app.route('/api/tenants/<tenant_id>/events', methods=['POST'])
    def create_tenant_event(tenant_id):
        """Create a new tenant event."""
        try:
            data = request.get_json()
            
            if not data or 'event_type' not in data:
                return jsonify({'error': 'event_type is required'}), 400
            
            conn = get_db_connection()
            cursor = conn.cursor()
            
            # Create the event
            cursor.callproc('CreateTenantEvent', [
                tenant_id,
                data['event_type'],
                json.dumps(data.get('event_data', {})),
                data.get('initiated_by', 'system')
            ])
            
            # Get the created event ID
            cursor.execute("SELECT LAST_INSERT_ID() as event_id")
            event_id = cursor.fetchone()[0]
            
            cursor.close()
            conn.close()
            
            return jsonify({
                'event_id': event_id,
                'message': 'Event created successfully'
            }), 201
            
        except Exception as e:
            logger.error(f"Error creating tenant event: {e}")
            return jsonify({'error': str(e)}), 500
    
    @app.route('/api/events/<int:event_id>/complete', methods=['PUT'])
    def complete_event(event_id):
        """Mark an event as completed."""
        try:
            data = request.get_json()
            
            if not data or 'status' not in data:
                return jsonify({'error': 'status is required'}), 400
            
            conn = get_db_connection()
            cursor = conn.cursor()
            
            cursor.callproc('CompleteEvent', [
                event_id,
                data['status'],
                data.get('error_message')
            ])
            
            cursor.close()
            conn.close()
            
            return jsonify({'message': 'Event completed successfully'})
            
        except Exception as e:
            logger.error(f"Error completing event: {e}")
            return jsonify({'error': str(e)}), 500
    
    @app.route('/api/tenants/<tenant_id>/resources', methods=['PUT'])
    def update_tenant_resources(tenant_id):
        """Update tenant resource allocation and usage."""
        try:
            data = request.get_json()
            
            if not data or 'resources' not in data:
                return jsonify({'error': 'resources data is required'}), 400
            
            conn = get_db_connection()
            cursor = conn.cursor()
            
            for resource_type, resource_data in data['resources'].items():
                cursor.callproc('UpdateTenantResources', [
                    tenant_id,
                    resource_type,
                    resource_data.get('allocated', 0),
                    resource_data.get('used', 0),
                    resource_data.get('unit', '')
                ])
            
            cursor.close()
            conn.close()
            
            return jsonify({'message': 'Resources updated successfully'})
            
        except Exception as e:
            logger.error(f"Error updating tenant resources: {e}")
            return jsonify({'error': str(e)}), 500
    
    @app.route('/api/stats', methods=['GET'])
    def get_stats():
        """Get overall tenant management statistics."""
        try:
            conn = get_db_connection()
            cursor = conn.cursor(dictionary=True)
            
            # Get tenant counts by status
            cursor.execute("""
                SELECT status, COUNT(*) as count 
                FROM tenants 
                GROUP BY status
            """)
            status_counts = {row['status']: row['count'] for row in cursor.fetchall()}
            
            # Get tenant counts by tier
            cursor.execute("""
                SELECT tier, COUNT(*) as count 
                FROM tenants 
                GROUP BY tier
            """)
            tier_counts = {row['tier']: row['count'] for row in cursor.fetchall()}
            
            # Get recent events
            cursor.execute("""
                SELECT event_type, COUNT(*) as count 
                FROM tenant_events 
                WHERE initiated_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
                GROUP BY event_type
            """)
            recent_events = {row['event_type']: row['count'] for row in cursor.fetchall()}
            
            # Get total costs
            cursor.execute("""
                SELECT 
                    SUM(hourly_cost) as total_hourly,
                    SUM(daily_cost) as total_daily,
                    SUM(monthly_cost) as total_monthly,
                    SUM(yearly_cost) as total_yearly
                FROM tenant_current_costs
            """)
            costs = cursor.fetchone()
            
            cursor.close()
            conn.close()
            
            return jsonify({
                'status_counts': status_counts,
                'tier_counts': tier_counts,
                'recent_events': recent_events,
                'total_costs': costs,
                'timestamp': datetime.utcnow().isoformat()
            })
            
        except Exception as e:
            logger.error(f"Error fetching stats: {e}")
            return jsonify({'error': str(e)}), 500
    
    if __name__ == '__main__':
        app.run(host='0.0.0.0', port=5000, debug=False)
  
  requirements.txt: |
    Flask==2.3.3
    Flask-CORS==4.0.0
    mysql-connector-python==8.1.0
    redis==4.6.0
    gunicorn==21.2.0
  
  Dockerfile: |
    FROM python:3.11-slim
    
    WORKDIR /app
    
    COPY requirements.txt .
    RUN pip install --no-cache-dir -r requirements.txt
    
    COPY app.py .
    
    EXPOSE 5000
    
    CMD ["gunicorn", "--bind", "0.0.0.0:5000", "--workers", "4", "app:app"]
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: tenant-event-api
  namespace: tenant-management
  labels:
    app: tenant-event-api
    component: backend
spec:
  replicas: 3
  selector:
    matchLabels:
      app: tenant-event-api
  template:
    metadata:
      labels:
        app: tenant-event-api
        component: backend
    spec:
      containers:
      - name: api
        image: python:3.11-slim
        command: ["/bin/bash"]
        args:
          - -c
          - |
            cd /app
            pip install --no-cache-dir -r requirements.txt
            exec gunicorn --bind 0.0.0.0:5000 --workers 4 app:app
        ports:
        - containerPort: 5000
          name: http
        env:
        - name: DB_HOST
          value: "tenant-event-db"
        - name: DB_PORT
          value: "3306"
        - name: DB_USER
          valueFrom:
            secretKeyRef:
              name: tenant-event-db-credentials
              key: username
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: tenant-event-db-credentials
              key: password
        - name: DB_NAME
          valueFrom:
            secretKeyRef:
              name: tenant-event-db-credentials
              key: database
        volumeMounts:
        - name: api-code
          mountPath: /app
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 5000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 5000
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: api-code
        configMap:
          name: tenant-event-api
---
apiVersion: v1
kind: Service
metadata:
  name: tenant-event-api
  namespace: tenant-management
  labels:
    app: tenant-event-api
spec:
  selector:
    app: tenant-event-api
  ports:
  - port: 80
    targetPort: 5000
    name: http
  type: ClusterIP
