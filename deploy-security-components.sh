#!/bin/bash

# Exit on error
set -e

echo "🚀 Deploying security components..."

# Create necessary namespaces
kubectl create namespace cert-manager --dry-run=client -o yaml | kubectl apply -f -
kubectl create namespace istio-system --dry-run=client -o yaml | kubectl apply -f -

# Install cert-manager
echo "📜 Installing cert-manager..."
kubectl apply -f https://github.com/cert-manager/cert-manager/releases/download/v1.12.0/cert-manager.yaml

# Wait for cert-manager to be ready
echo "⏳ Waiting for cert-manager to be ready..."
kubectl wait --for=condition=ready pod -l app.kubernetes.io/instance=cert-manager -n cert-manager --timeout=300s

# Install Istio
echo "🛡️ Installing Istio..."
istioctl install -y --set profile=demo

# Apply OPA configuration
echo "🔒 Applying OPA policies..."
kubectl apply -f security/opa/policy-enforcement.yaml

# Apply cert-manager configuration
echo "🔐 Applying cert-manager configuration..."
kubectl apply -f security/cert-manager/cert-manager-config.yaml

# Apply mTLS configuration
echo "🔑 Applying mTLS configuration..."
kubectl apply -f security/mtls/istio-mtls-config.yaml

# Verify installations
echo "✅ Verifying installations..."

# Check cert-manager
kubectl get pods -n cert-manager

# Check Istio
kubectl get pods -n istio-system

# Check OPA
kubectl get pods -n kube-system -l app=opa

echo "🎉 Security components deployment completed!"
echo "Next steps:"
echo "1. Configure DNS records for your domains"
echo "2. Update your ingress configurations to use the new certificates"
echo "3. Test mTLS communication between services"
echo "4. Verify OPA policies are working as expected" 