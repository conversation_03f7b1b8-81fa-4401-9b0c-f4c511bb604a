apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: tenant-istio-validation-test-vs
  namespace: tenant-istio-validation-test
  labels:
    app: istio-validation-test
    managed-by: advanced-tenant-onboard
spec:
  hosts:
  - "istio-validation-test.architrave-assets.com"
  gateways:
  - istio-system/tenant-gateway
  http:
  - match:
    - uri:
        prefix: "/api"
    route:
    - destination:
        host: istio-validation-test-backend
        port:
          number: 8080
    retries:
      attempts: 3
      perTryTimeout: 2s
      retryOn: "gateway-error,connect-failure,refused-stream"
    timeout: 30s
  - match:
    - uri:
        prefix: "/"
    route:
    - destination:
        host: istio-validation-test-frontend
        port:
          number: 80
    retries:
      attempts: 3
      perTryTimeout: 2s
      retryOn: "gateway-error,connect-failure,refused-stream"
    timeout: 30s
