apiVersion: batch/v1
kind: CronJob
metadata:
  name: tenant-backup
  namespace: tenant-test-tenant-maintenance
spec:
  schedule: "0 2 * * *"  # Daily at 2 AM
  jobTemplate:
    spec:
      template:
        spec:
          serviceAccountName: backend-irsa-sa
          containers:
          - name: backup
            image: mysql:8.0
            command:
            - /bin/bash
            - -c
            - |
              set -e
              echo "Starting backup for tenant: test-tenant-maintenance"
              
              # Get database credentials from environment
              DB_HOST="$DB_HOST"
              DB_USER="$DB_USER"
              DB_PASSWORD="$DB_PASSWORD"
              DB_NAME="$DB_NAME"
              
              # Create backup filename with timestamp
              BACKUP_FILE="backup-$(date +%Y%m%d-%H%M%S).sql"
              
              # Create database backup
              mysqldump -h "$DB_HOST" -u "$DB_USER" -p"$DB_PASSWORD" \
                --single-transaction --routines --triggers \
                "$DB_NAME" > "/tmp/$BACKUP_FILE"
              
              echo "Database backup completed: $BACKUP_FILE"
              
              # Upload to S3 (if AWS CLI is available)
              if command -v aws &> /dev/null; then
                aws s3 cp "/tmp/$BACKUP_FILE" "s3://architravetestdb/backups/test-tenant-maintenance/$BACKUP_FILE" --region eu-central-1
                echo "Backup uploaded to S3: s3://architravetestdb/backups/test-tenant-maintenance/$BACKUP_FILE"
              else
                echo "AWS CLI not available, backup saved locally: /tmp/$BACKUP_FILE"
              fi
              
              # Clean up local backup file
              rm -f "/tmp/$BACKUP_FILE"
              
              echo "Backup process completed successfully"
            env:
            - name: DB_HOST
              valueFrom:
                secretKeyRef:
                  name: test-tenant-maintenance-db-secret
                  key: DB_HOST
            - name: DB_USER
              valueFrom:
                secretKeyRef:
                  name: test-tenant-maintenance-db-secret
                  key: DB_USER
            - name: DB_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: test-tenant-maintenance-db-secret
                  key: DB_PASSWORD
            - name: DB_NAME
              valueFrom:
                secretKeyRef:
                  name: test-tenant-maintenance-db-secret
                  key: DB_NAME
          restartPolicy: OnFailure 