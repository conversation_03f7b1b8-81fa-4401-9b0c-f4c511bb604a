apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: testautogo16-alb-ingress
  namespace: tenant-testautogo16
  annotations:
    # ALB Configuration
    kubernetes.io/ingress.class: alb
    alb.ingress.kubernetes.io/scheme: internet-facing
    alb.ingress.kubernetes.io/target-type: ip
    alb.ingress.kubernetes.io/load-balancer-name: testautogo16-alb
    
    # SSL Certificate Configuration
    alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:eu-central-1:************:certificate/8d306c2f-62e2-42f4-a941-562ce65cda32
    alb.ingress.kubernetes.io/ssl-policy: ELBSecurityPolicy-TLS-1-2-2017-01
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTP": 80}, {"HTTPS": 443}]'
    alb.ingress.kubernetes.io/ssl-redirect: '443'
    
    # Health Check Configuration
    alb.ingress.kubernetes.io/healthcheck-path: /health
    alb.ingress.kubernetes.io/healthcheck-interval-seconds: '30'
    alb.ingress.kubernetes.io/healthcheck-timeout-seconds: '5'
    alb.ingress.kubernetes.io/healthy-threshold-count: '2'
    alb.ingress.kubernetes.io/unhealthy-threshold-count: '3'
    
    # Security Configuration
    alb.ingress.kubernetes.io/wafv2-acl-arn: arn:aws:wafv2:eu-central-1:************:regional/webacl/production-alb-waf/7a925f13-88c5-4af3-8cda-71ad6bf31fad
    
    # Load Balancer Attributes
    alb.ingress.kubernetes.io/load-balancer-attributes: idle_timeout.timeout_seconds=60,routing.http2.enabled=true
    
    # Tags
    alb.ingress.kubernetes.io/tags: Environment=production,Tenant=testautogo16,ManagedBy=advanced-tenant-onboard
spec:
  rules:
  - host: testautogo16.architrave-assets.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: testautogo16-frontend-service
            port:
              number: 80
      - path: /api
        pathType: Prefix
        backend:
          service:
            name: testautogo16-backend-service
            port:
              number: 8080
  tls:
  - hosts:
    - testautogo16.architrave-assets.com
    secretName: testautogo16-tls-secret
