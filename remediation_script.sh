#!/bin/bash
# 4-Phase Remediation Script for External Networking Architecture Fix
# This script fixes the Istio vs ALB architecture mismatch

set -e

echo "🔧 4-PHASE REMEDIATION: EXTERNAL NETWORKING ARCHITECTURE FIX"
echo "============================================================="
echo ""

# PHASE 1: INFRASTRUCTURE ASSESSMENT AND CLEANUP
echo "🔍 PHASE 1: INFRASTRUCTURE ASSESSMENT AND CLEANUP"
echo "================================================"
echo ""

echo "Step 1.1: Checking Istio Gateway status..."
kubectl get gateway tenant-gateway -n istio-system -o wide || echo "❌ tenant-gateway not found"
echo ""

echo "Step 1.2: Verifying Istio ingress gateway service..."
kubectl get svc istio-ingressgateway -n istio-system -o wide || echo "❌ istio-ingressgateway service not found"
echo ""

echo "Step 1.3: Checking for existing Istio gateways..."
kubectl get gateway --all-namespaces
echo ""

echo "Step 1.4: Checking for conflicting Kubernetes Ingress resources..."
kubectl get ingress --all-namespaces || echo "No Ingress resources found"
echo ""

echo "Step 1.5: Checking AWS Load Balancer Controller installation..."
kubectl get deployment aws-load-balancer-controller -n kube-system || echo "AWS Load Balancer Controller not installed"
echo ""

# PHASE 2: IDENTIFY EXISTING TENANTS
echo "🔍 PHASE 2: IDENTIFY EXISTING TENANTS"
echo "====================================="
echo ""

echo "Step 2.1: Identifying existing tenant namespaces..."
TENANT_NAMESPACES=$(kubectl get namespaces | grep "tenant-" | awk '{print $1}' || echo "")
if [ -z "$TENANT_NAMESPACES" ]; then
    echo "❌ No tenant namespaces found"
else
    echo "✅ Found tenant namespaces:"
    echo "$TENANT_NAMESPACES"
fi
echo ""

echo "Step 2.2: Checking existing VirtualService resources..."
kubectl get virtualservice --all-namespaces || echo "No VirtualService resources found"
echo ""

# PHASE 3: CHECK ISTIO INSTALLATION
echo "🔍 PHASE 3: CHECK ISTIO INSTALLATION"
echo "===================================="
echo ""

echo "Step 3.1: Checking Istio system namespace..."
kubectl get namespace istio-system || echo "❌ istio-system namespace not found"
echo ""

echo "Step 3.2: Checking Istio components..."
kubectl get pods -n istio-system || echo "❌ No Istio pods found"
echo ""

echo "Step 3.3: Checking for Istio ingress gateway external IP..."
EXTERNAL_IP=$(kubectl get svc istio-ingressgateway -n istio-system -o jsonpath='{.status.loadBalancer.ingress[0].hostname}' 2>/dev/null || echo "")
if [ -z "$EXTERNAL_IP" ]; then
    EXTERNAL_IP=$(kubectl get svc istio-ingressgateway -n istio-system -o jsonpath='{.status.loadBalancer.ingress[0].ip}' 2>/dev/null || echo "")
fi

if [ -z "$EXTERNAL_IP" ]; then
    echo "❌ No external IP/hostname found for Istio ingress gateway"
else
    echo "✅ Istio ingress gateway external endpoint: $EXTERNAL_IP"
fi
echo ""

# PHASE 4: SUMMARY AND RECOMMENDATIONS
echo "📋 PHASE 4: SUMMARY AND RECOMMENDATIONS"
echo "======================================="
echo ""

echo "Infrastructure Assessment Complete!"
echo ""
echo "Next steps based on findings:"
echo "1. If Istio is not installed, install Istio service mesh"
echo "2. Create tenant-gateway in istio-system namespace"
echo "3. Create VirtualService resources for each tenant"
echo "4. Update advanced_tenant_onboard.go script"
echo ""

echo "🏁 REMEDIATION ASSESSMENT COMPLETE"
echo "=================================="
