<?php

return [
    'service_manager' => [
        'aliases' => [
            'ArchNotifications\Service\NotificationService' => 'ArchNotifications\Service\Notification',
            'NotificationService' => 'ArchNotifications\Service\Notification',
        ],
        'factories' => [
            'ArchNotifications\Service\Notification' => 'ArchNotifications\Service\Factory\NotificationFactory',
        ],
        'invokables' => [
            // Prevent autowiring for this service
        ],
    ],
    'notifications' => [
        'from' => [
            'name' => 'System Administrator',
            'email' => '<EMAIL>',
        ],
        'architraveSupportEmail' => '<EMAIL>',
        'customerSupportEmail' => '<EMAIL>',
        'customer' => 'Default Customer',
        'customerAdmin' => '<EMAIL>',
        'template_map' => [
            // Basic template mappings
        ],
        'sms' => [
            'messagebird' => [
                'api_key' => 'test_key'
            ],
            'logging' => true,
        ],
    ],
    'appHost' => 'https://example.com/',
]; 