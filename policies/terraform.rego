package main

# ========== S3 BUCKET SECURITY POLICIES ==========

# Deny S3 buckets without encryption
deny[msg] {
    input.resource.aws_s3_bucket[name]
    not has_encryption(name)
    msg = sprintf("S3 bucket '%v' does not have encryption configured", [name])
}

has_encryption(bucket_name) {
    input.resource.aws_s3_bucket_server_side_encryption_configuration[_].bucket == bucket_name
}

# Deny S3 buckets without versioning
deny[msg] {
    input.resource.aws_s3_bucket[name]
    not has_versioning(name)
    msg = sprintf("S3 bucket '%v' does not have versioning configured", [name])
}

has_versioning(bucket_name) {
    input.resource.aws_s3_bucket_versioning[_].bucket == bucket_name
}

# Deny S3 buckets without public access block
deny[msg] {
    input.resource.aws_s3_bucket[name]
    not has_public_access_block(name)
    msg = sprintf("S3 bucket '%v' does not have public access block configured", [name])
}

has_public_access_block(bucket_name) {
    input.resource.aws_s3_bucket_public_access_block[_].bucket == bucket_name
}

# Deny S3 buckets with ACL other than private
deny[msg] {
    input.resource.aws_s3_bucket[name]
    input.resource.aws_s3_bucket[name].acl != "private"
    msg = sprintf("S3 bucket '%v' has ACL '%v' instead of 'private'", [name, input.resource.aws_s3_bucket[name].acl])
}

# Deny S3 buckets without logging
deny[msg] {
    input.resource.aws_s3_bucket[name]
    not has_logging(name)
    msg = sprintf("S3 bucket '%v' does not have logging configured", [name])
}

has_logging(bucket_name) {
    input.resource.aws_s3_bucket_logging[_].bucket == bucket_name
}

# ========== RDS SECURITY POLICIES ==========

# Deny RDS instances without encryption
deny[msg] {
    input.resource.aws_db_instance[name]
    not input.resource.aws_db_instance[name].storage_encrypted
    msg = sprintf("RDS instance '%v' does not have encryption configured", [name])
}

# Deny RDS instances without backup
deny[msg] {
    input.resource.aws_db_instance[name]
    input.resource.aws_db_instance[name].backup_retention_period < 7
    msg = sprintf("RDS instance '%v' has backup retention period less than 7 days", [name])
}

# Deny RDS instances without multi-AZ
deny[msg] {
    input.resource.aws_db_instance[name]
    not input.resource.aws_db_instance[name].multi_az
    msg = sprintf("RDS instance '%v' does not have multi-AZ enabled", [name])
}

# Deny RDS instances with public access
deny[msg] {
    input.resource.aws_db_instance[name]
    input.resource.aws_db_instance[name].publicly_accessible
    msg = sprintf("RDS instance '%v' is publicly accessible", [name])
}

# ========== EKS SECURITY POLICIES ==========

# Deny EKS clusters without encryption
deny[msg] {
    input.resource.aws_eks_cluster[name]
    not input.resource.aws_eks_cluster[name].encryption_config
    msg = sprintf("EKS cluster '%v' does not have encryption configured", [name])
}

# Deny EKS clusters with public access
deny[msg] {
    input.resource.aws_eks_cluster[name]
    input.resource.aws_eks_cluster[name].vpc_config.endpoint_public_access
    msg = sprintf("EKS cluster '%v' has public endpoint access enabled", [name])
}

# ========== SECURITY GROUP POLICIES ==========

# Deny security groups with 0.0.0.0/0 ingress for SSH
deny[msg] {
    input.resource.aws_security_group[name]
    sg_rule := input.resource.aws_security_group[name].ingress[_]
    sg_rule.from_port <= 22
    sg_rule.to_port >= 22
    sg_rule.cidr_blocks[_] == "0.0.0.0/0"
    msg = sprintf("Security group '%v' allows SSH access from 0.0.0.0/0", [name])
}

# Deny security groups with 0.0.0.0/0 ingress for RDP
deny[msg] {
    input.resource.aws_security_group[name]
    sg_rule := input.resource.aws_security_group[name].ingress[_]
    sg_rule.from_port <= 3389
    sg_rule.to_port >= 3389
    sg_rule.cidr_blocks[_] == "0.0.0.0/0"
    msg = sprintf("Security group '%v' allows RDP access from 0.0.0.0/0", [name])
}

# Deny security groups with 0.0.0.0/0 ingress for database ports
deny[msg] {
    input.resource.aws_security_group[name]
    sg_rule := input.resource.aws_security_group[name].ingress[_]
    database_ports := [3306, 5432, 1433, 1521, 27017, 6379, 9042]
    port := database_ports[_]
    sg_rule.from_port <= port
    sg_rule.to_port >= port
    sg_rule.cidr_blocks[_] == "0.0.0.0/0"
    msg = sprintf("Security group '%v' allows database port %v access from 0.0.0.0/0", [name, port])
}

# ========== IAM POLICIES ==========

# Deny IAM policies with wildcard actions
deny[msg] {
    input.resource.aws_iam_policy[name]
    policy := json.unmarshal(input.resource.aws_iam_policy[name].policy)
    statement := policy.Statement[_]
    statement.Effect == "Allow"
    action := statement.Action[_]
    contains(action, "*")
    msg = sprintf("IAM policy '%v' contains wildcard action '%v'", [name, action])
}

# Deny IAM policies with wildcard resources
deny[msg] {
    input.resource.aws_iam_policy[name]
    policy := json.unmarshal(input.resource.aws_iam_policy[name].policy)
    statement := policy.Statement[_]
    statement.Effect == "Allow"
    resource := statement.Resource[_]
    resource == "*"
    msg = sprintf("IAM policy '%v' contains wildcard resource '*'", [name])
}

# ========== KMS POLICIES ==========

# Deny KMS keys without rotation
deny[msg] {
    input.resource.aws_kms_key[name]
    not input.resource.aws_kms_key[name].enable_key_rotation
    msg = sprintf("KMS key '%v' does not have rotation enabled", [name])
}

# ========== TAGGING POLICIES ==========

# Ensure all resources have tags
deny[msg] {
    resource_types := {"aws_instance", "aws_s3_bucket", "aws_db_instance", "aws_eks_cluster", "aws_lambda_function", "aws_kms_key", "aws_security_group"}
    resource_type := resource_types[_]
    input.resource[resource_type][name]
    not input.resource[resource_type][name].tags
    msg = sprintf("%v '%v' does not have tags configured", [resource_type, name])
}

# Ensure all resources have environment tag
deny[msg] {
    resource_types := {"aws_instance", "aws_s3_bucket", "aws_db_instance", "aws_eks_cluster", "aws_lambda_function", "aws_kms_key", "aws_security_group"}
    resource_type := resource_types[_]
    input.resource[resource_type][name]
    not input.resource[resource_type][name].tags.Environment
    msg = sprintf("%v '%v' does not have Environment tag", [resource_type, name])
}
