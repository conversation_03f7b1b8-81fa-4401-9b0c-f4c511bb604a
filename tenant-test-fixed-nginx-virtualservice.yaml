apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: tenant-test-fixed-nginx-vs
  namespace: tenant-test-fixed-nginx
  labels:
    managed-by: tenant-onboarding
    tenant: test-fixed-nginx
spec:
  gateways:
  - istio-system/tenant-gateway
  hosts:
  - test-fixed-nginx.architrave-assets.com
  http:
  - headers:
      request:
        set:
          X-Forwarded-Proto: https
    match:
    - uri:
        prefix: /api
    retries:
      attempts: 3
      perTryTimeout: 10s
      retryOn: gateway-error,connect-failure,refused-stream
    route:
    - destination:
        host: test-fixed-nginx-backend-service
        port:
          number: 8080
    timeout: 30s
  - headers:
      request:
        set:
          X-Forwarded-Proto: https
    retries:
      attempts: 2
      perTryTimeout: 5s
      retryOn: gateway-error,connect-failure,refused-stream
    route:
    - destination:
        host: test-fixed-nginx-frontend-service
        port:
          number: 80
    timeout: 15s
