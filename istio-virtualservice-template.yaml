apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: tenant-{{TENANT_ID}}-vs
  namespace: tenant-{{TENANT_ID}}
  labels:
    tenant: {{TENANT_ID}}
    managed-by: tenant-onboarding
spec:
  hosts:
  - "{{TENANT_ID}}.architrave-assets.com"
  gateways:
  - "istio-system/tenant-gateway"
  http:
  # API routes (backend)
  - match:
    - uri:
        prefix: "/api"
    route:
    - destination:
        host: {{TENANT_ID}}-backend
        port:
          number: 8080
    retries:
      attempts: 3
      perTryTimeout: 10s
      retryOn: gateway-error,connect-failure,refused-stream
    timeout: 30s
    headers:
      request:
        set:
          X-Forwarded-Proto: "https"
  # Frontend routes (nginx)
  - route:
    - destination:
        host: {{TENANT_ID}}-frontend
        port:
          number: 80
    retries:
      attempts: 2
      perTryTimeout: 5s
      retryOn: gateway-error,connect-failure,refused-stream
    timeout: 15s
    headers:
      request:
        set:
          X-Forwarded-Proto: "https"
---
# Ensure the tenant-gateway exists in istio-system
apiVersion: networking.istio.io/v1beta1
kind: Gateway
metadata:
  name: tenant-gateway
  namespace: istio-system
  labels:
    app: istio-ingressgateway
    managed-by: tenant-onboarding
spec:
  selector:
    istio: ingressgateway
  servers:
  - port:
      number: 80
      name: http
      protocol: HTTP
    hosts:
    - "*.architrave-assets.com"
    tls:
      httpsRedirect: true
  - port:
      number: 443
      name: https
      protocol: HTTPS
    hosts:
    - "*.architrave-assets.com"
    tls:
      mode: SIMPLE
      credentialName: architrave-assets-wildcard-cert
