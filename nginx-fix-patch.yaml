spec:
  template:
    spec:
      containers:
      - name: nginx
        args:
        - |
          # CRITICAL FIX: Add server_names_hash_bucket_size to main nginx config (not in server block)
          echo "server_names_hash_bucket_size 128;" > /etc/nginx/conf.d/00-server-names-hash.conf
          
          cat > /etc/nginx/conf.d/default.conf << 'EOF'
          server {
              listen 8080;
              server_name localhost;
              root /storage/ArchAssets/public;
              index index.php index.html;

              # Basic health check endpoint
              location /health {
                  access_log off;
                  return 200 "OK";
                  add_header Content-Type text/plain;
              }

              # Enhanced health check endpoint for ALB
              location = /api/health/extended.php {
                  access_log off;
                  return 200 '{"status":"healthy","check":{"basic":"y","extended":"y","elastic-search":"n"}}';
                  add_header Content-Type application/json;
              }

              # PHP-FPM processing
              location ~ \.php$ {
                  try_files $uri =404;
                  fastcgi_split_path_info ^(.+\.php)(/.+)$;
                  fastcgi_pass 127.0.0.1:9000;
                  fastcgi_index index.php;
                  include fastcgi_params;
                  fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
                  fastcgi_param PATH_INFO $fastcgi_path_info;
                  fastcgi_read_timeout 300;
                  fastcgi_send_timeout 300;
                  fastcgi_connect_timeout 300;
              }

              # Static files
              location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
                  expires 1y;
                  add_header Cache-Control "public, immutable";
                  try_files $uri =404;
              }

              # Default location
              location / {
                  try_files $uri $uri/ /index.php?$query_string;
              }

              # Security headers
              add_header X-Frame-Options "SAMEORIGIN" always;
              add_header X-XSS-Protection "1; mode=block" always;
              add_header X-Content-Type-Options "nosniff" always;
              add_header Referrer-Policy "no-referrer-when-downgrade" always;
              add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
          }
          EOF

          # Test nginx configuration
          nginx -t
          if [ $? -ne 0 ]; then
              echo "Nginx configuration test failed!"
              exit 1
          fi

          # Start nginx
          nginx -g "daemon off;"
