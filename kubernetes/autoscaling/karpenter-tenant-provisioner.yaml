apiVersion: karpenter.sh/v1alpha5
kind: Provisioner
metadata:
  name: tenant-provisioner
spec:
  # Limit to tenant namespaces
  requirements:
    - key: "kubernetes.io/arch"
      operator: In
      values: ["amd64"]
    - key: "kubernetes.io/os"
      operator: In
      values: ["linux"]
    - key: "karpenter.sh/capacity-type"
      operator: In
      values: ["spot", "on-demand"]
    - key: "node.kubernetes.io/instance-type"
      operator: In
      values:
        - "t3a.small"
        - "t3.small"
        - "t3a.medium"
        - "t3.medium"
  # Limit the provisioner to tenant namespaces
  labels:
    workload-type: tenant
  # Taints ensure that only tenant workloads will be scheduled on these nodes
  taints:
    - key: workload-type
      value: tenant
      effect: NoSchedule
  # Limit the provisioner to specific namespaces
  limits:
    resources:
      cpu: 20
      memory: 40Gi
  # Configure node expiry
  ttlSecondsAfterEmpty: 30
  ttlSecondsUntilExpired: 2592000 # 30 days
  # Configure consolidation to optimize node usage
  consolidation:
    enabled: true
  # Configure provider-specific settings
  providerRef:
    name: default
