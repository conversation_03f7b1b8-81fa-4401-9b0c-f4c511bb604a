---
# Enhanced Horizontal Pod Autoscaler for high-traffic scenarios
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: tenant-app-enhanced-hpa
  namespace: ${TENANT_NAMESPACE}
  labels:
    app: tenant-app
    tenant: ${TENANT_ID}
    tier: ${TENANT_TIER}
    managed-by: terraform
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: ${TENANT_ID}-app
  minReplicas: ${MIN_REPLICAS}
  maxReplicas: ${MAX_REPLICAS}
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: ${CPU_TARGET_UTILIZATION}
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: ${MEMORY_TARGET_UTILIZATION}
  # Custom metrics for enhanced scaling
  - type: Pods
    pods:
      metric:
        name: php_fpm_active_processes
      target:
        type: AverageValue
        averageValue: "8"
  - type: Pods
    pods:
      metric:
        name: nginx_active_connections
      target:
        type: AverageValue
        averageValue: "100"
  # Database connection pool metrics
  - type: Pods
    pods:
      metric:
        name: mysql_connections_active
      target:
        type: AverageValue
        averageValue: "15"
  behavior:
    scaleDown:
      stabilizationWindowSeconds: ${SCALE_DOWN_STABILIZATION}
      policies:
      - type: Percent
        value: ${SCALE_DOWN_PERCENT}
        periodSeconds: 60
      - type: Pods
        value: ${SCALE_DOWN_PODS}
        periodSeconds: 60
      selectPolicy: Min
    scaleUp:
      stabilizationWindowSeconds: ${SCALE_UP_STABILIZATION}
      policies:
      - type: Percent
        value: ${SCALE_UP_PERCENT}
        periodSeconds: 30
      - type: Pods
        value: ${SCALE_UP_PODS}
        periodSeconds: 30
      selectPolicy: Max

---
# KEDA ScaledObject for advanced scaling based on custom metrics
apiVersion: keda.sh/v1alpha1
kind: ScaledObject
metadata:
  name: tenant-app-keda-scaler
  namespace: ${TENANT_NAMESPACE}
  labels:
    app: tenant-app
    tenant: ${TENANT_ID}
    tier: ${TENANT_TIER}
spec:
  scaleTargetRef:
    name: ${TENANT_ID}-app
  pollingInterval: 15
  cooldownPeriod: 300
  idleReplicaCount: ${IDLE_REPLICAS}
  minReplicaCount: ${MIN_REPLICAS}
  maxReplicaCount: ${MAX_REPLICAS}
  triggers:
  # CPU and Memory triggers
  - type: cpu
    metadata:
      type: Utilization
      value: "${CPU_TARGET_UTILIZATION}"
  - type: memory
    metadata:
      type: Utilization
      value: "${MEMORY_TARGET_UTILIZATION}"
  # HTTP request rate trigger
  - type: prometheus
    metadata:
      serverAddress: http://prometheus-server.monitoring.svc.cluster.local:80
      metricName: nginx_http_requests_rate
      threshold: "${HTTP_REQUESTS_THRESHOLD}"
      query: sum(rate(nginx_http_requests_total{namespace="${TENANT_NAMESPACE}"}[2m]))
  # Database connection pool trigger
  - type: prometheus
    metadata:
      serverAddress: http://prometheus-server.monitoring.svc.cluster.local:80
      metricName: mysql_connections_utilization
      threshold: "0.8"
      query: sum(mysql_global_status_threads_connected{namespace="${TENANT_NAMESPACE}"}) / sum(mysql_global_variables_max_connections{namespace="${TENANT_NAMESPACE}"})
  # Queue length trigger (RabbitMQ)
  - type: rabbitmq
    metadata:
      host: amqp://guest:guest@${TENANT_ID}-rabbitmq.${TENANT_NAMESPACE}.svc.cluster.local:5672/
      queueName: tenant_queue
      queueLength: "10"
  # Response time trigger
  - type: prometheus
    metadata:
      serverAddress: http://prometheus-server.monitoring.svc.cluster.local:80
      metricName: http_response_time_p95
      threshold: "500"
      query: histogram_quantile(0.95, sum(rate(nginx_http_request_duration_seconds_bucket{namespace="${TENANT_NAMESPACE}"}[5m])) by (le)) * 1000

---
# Predictive Horizontal Pod Autoscaler using VPA recommendations
apiVersion: autoscaling.k8s.io/v1
kind: VerticalPodAutoscaler
metadata:
  name: tenant-app-predictive-vpa
  namespace: ${TENANT_NAMESPACE}
  labels:
    app: tenant-app
    tenant: ${TENANT_ID}
    tier: ${TENANT_TIER}
spec:
  targetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: ${TENANT_ID}-app
  updatePolicy:
    updateMode: "Off"  # Only provide recommendations for predictive scaling
  resourcePolicy:
    containerPolicies:
    - containerName: '*'
      minAllowed:
        cpu: ${VPA_MIN_CPU}
        memory: ${VPA_MIN_MEMORY}
      maxAllowed:
        cpu: ${VPA_MAX_CPU}
        memory: ${VPA_MAX_MEMORY}
      controlledResources: ["cpu", "memory"]
      controlledValues: "RequestsAndLimits"

---
# Pod Disruption Budget for high availability
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: tenant-app-pdb
  namespace: ${TENANT_NAMESPACE}
  labels:
    app: tenant-app
    tenant: ${TENANT_ID}
    tier: ${TENANT_TIER}
spec:
  minAvailable: ${PDB_MIN_AVAILABLE}
  selector:
    matchLabels:
      app: tenant-app
      tenant: ${TENANT_ID}

---
# Network Policy for tenant isolation during scaling
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: tenant-scaling-network-policy
  namespace: ${TENANT_NAMESPACE}
  labels:
    tenant: ${TENANT_ID}
    tier: ${TENANT_TIER}
spec:
  podSelector:
    matchLabels:
      app: tenant-app
      tenant: ${TENANT_ID}
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: istio-system
    - namespaceSelector:
        matchLabels:
          name: monitoring
    - podSelector:
        matchLabels:
          app: tenant-app
          tenant: ${TENANT_ID}
    ports:
    - protocol: TCP
      port: 80
    - protocol: TCP
      port: 9000
  egress:
  - to:
    - namespaceSelector:
        matchLabels:
          name: kube-system
  - to: []
    ports:
    - protocol: TCP
      port: 3306  # MySQL
    - protocol: TCP
      port: 5672  # RabbitMQ
    - protocol: TCP
      port: 443   # HTTPS
    - protocol: TCP
      port: 53    # DNS
    - protocol: UDP
      port: 53    # DNS

---
# ServiceMonitor for enhanced metrics collection
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: tenant-app-enhanced-metrics
  namespace: ${TENANT_NAMESPACE}
  labels:
    app: tenant-app
    tenant: ${TENANT_ID}
    tier: ${TENANT_TIER}
spec:
  selector:
    matchLabels:
      app: tenant-app
      tenant: ${TENANT_ID}
  endpoints:
  - port: metrics
    interval: 15s
    path: /metrics
    honorLabels: true
  - port: php-fpm-metrics
    interval: 30s
    path: /status
    honorLabels: true
  namespaceSelector:
    matchNames:
    - ${TENANT_NAMESPACE}
