apiVersion: keda.sh/v1alpha1
kind: ScaledObject
metadata:
  name: newtest2025-backend-scaler
  namespace: tenant-newtest2025
spec:
  scaleTargetRef:
    name: newtest2025-backend
  minReplicaCount: 2
  maxReplicaCount: 10
  pollingInterval: 15
  cooldownPeriod: 300
  triggers:
  - type: prometheus
    metadata:
      serverAddress: http://prometheus-server.monitoring.svc.cluster.local:9090
      metricName: envoy_cluster_upstream_rq_total
      query: sum(rate(envoy_cluster_upstream_rq_total{app="newtest2025-backend"}[2m]))
      threshold: "10"
---
apiVersion: keda.sh/v1alpha1
kind: ScaledObject
metadata:
  name: newtest2025-frontend-scaler
  namespace: tenant-newtest2025
spec:
  scaleTargetRef:
    name: newtest2025-frontend
  minReplicaCount: 1
  maxReplicaCount: 5
  pollingInterval: 15
  cooldownPeriod: 300
  triggers:
  - type: prometheus
    metadata:
      serverAddress: http://prometheus-server.monitoring.svc.cluster.local:9090
      metricName: envoy_cluster_upstream_rq_total
      query: sum(rate(envoy_cluster_upstream_rq_total{app="newtest2025-frontend"}[2m]))
      threshold: "5"
---
apiVersion: keda.sh/v1alpha1
kind: ScaledObject
metadata:
  name: newtest2025-rabbitmq-scaler
  namespace: tenant-newtest2025
spec:
  scaleTargetRef:
    name: newtest2025-rabbitmq
  minReplicaCount: 1
  maxReplicaCount: 5
  pollingInterval: 15
  cooldownPeriod: 300
  triggers:
  - type: prometheus
    metadata:
      serverAddress: http://prometheus-server.monitoring.svc.cluster.local:9090
      metricName: envoy_cluster_upstream_rq_total
      query: sum(rate(envoy_cluster_upstream_rq_total{app="newtest2025-rabbitmq"}[2m]))
      threshold: "2"
---
apiVersion: keda.sh/v1alpha1
kind: ScaledObject
metadata:
  name: newtest2027-backend-scaler
  namespace: tenant-newtest2027
spec:
  scaleTargetRef:
    name: newtest2027-backend
  minReplicaCount: 2
  maxReplicaCount: 10
  pollingInterval: 15
  cooldownPeriod: 300
  triggers:
  - type: prometheus
    metadata:
      serverAddress: http://prometheus-server.monitoring.svc.cluster.local:9090
      metricName: envoy_cluster_upstream_rq_total
      query: sum(rate(envoy_cluster_upstream_rq_total{app="newtest2027-backend"}[2m]))
      threshold: "10"
---
apiVersion: keda.sh/v1alpha1
kind: ScaledObject
metadata:
  name: newtest2027-frontend-scaler
  namespace: tenant-newtest2027
spec:
  scaleTargetRef:
    name: newtest2027-frontend
  minReplicaCount: 1
  maxReplicaCount: 5
  pollingInterval: 15
  cooldownPeriod: 300
  triggers:
  - type: prometheus
    metadata:
      serverAddress: http://prometheus-server.monitoring.svc.cluster.local:9090
      metricName: envoy_cluster_upstream_rq_total
      query: sum(rate(envoy_cluster_upstream_rq_total{app="newtest2027-frontend"}[2m]))
      threshold: "5"
---
apiVersion: keda.sh/v1alpha1
kind: ScaledObject
metadata:
  name: newtest2027-rabbitmq-scaler
  namespace: tenant-newtest2027
spec:
  scaleTargetRef:
    name: newtest2027-rabbitmq
  minReplicaCount: 1
  maxReplicaCount: 5
  pollingInterval: 15
  cooldownPeriod: 300
  triggers:
  - type: prometheus
    metadata:
      serverAddress: http://prometheus-server.monitoring.svc.cluster.local:9090
      metricName: envoy_cluster_upstream_rq_total
      query: sum(rate(envoy_cluster_upstream_rq_total{app="newtest2027-rabbitmq"}[2m]))
      threshold: "2"
---
apiVersion: keda.sh/v1alpha1
kind: ScaledObject
metadata:
  name: newtest2027-redis-scaler
  namespace: tenant-newtest2027
spec:
  scaleTargetRef:
    name: newtest2027-redis
  minReplicaCount: 1
  maxReplicaCount: 3
  pollingInterval: 15
  cooldownPeriod: 300
  triggers:
  - type: prometheus
    metadata:
      serverAddress: http://prometheus-server.monitoring.svc.cluster.local:9090
      metricName: envoy_cluster_upstream_rq_total
      query: sum(rate(envoy_cluster_upstream_rq_total{app="newtest2027-redis"}[2m]))
      threshold: "1"
---
apiVersion: keda.sh/v1alpha1
kind: ScaledObject
metadata:
  name: autotest2028-backend-scaler
  namespace: tenant-autotest2028
spec:
  scaleTargetRef:
    name: autotest2028-backend
  minReplicaCount: 1
  maxReplicaCount: 5
  pollingInterval: 15
  cooldownPeriod: 300
  triggers:
  - type: prometheus
    metadata:
      serverAddress: http://prometheus-server.monitoring.svc.cluster.local:9090
      metricName: envoy_cluster_upstream_rq_total
      query: sum(rate(envoy_cluster_upstream_rq_total{app="autotest2028-backend"}[2m]))
      threshold: "5"
