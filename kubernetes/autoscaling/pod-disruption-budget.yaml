apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: monitoring-pdb
  namespace: monitoring
  labels:
    app: monitoring
    component: prometheus
    managed-by: terraform
spec:
  maxUnavailable: 1
  selector:
    matchLabels:
      app: prometheus
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: security-pdb
  namespace: security
  labels:
    app: security
    component: all-in-one-security
    managed-by: terraform
spec:
  maxUnavailable: 1
  selector:
    matchLabels:
      app: all-in-one-security
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: webapp-pdb
  namespace: default
  labels:
    app: webapp
    managed-by: terraform
spec:
  # Ensure at least 75% of pods are available during disruptions
  minAvailable: 75%
  selector:
    matchLabels:
      app: webapp
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: queue-processor-pdb
  namespace: default
  labels:
    app: queue-processor
    managed-by: terraform
spec:
  # Allow at most 1 pod to be unavailable during disruptions
  maxUnavailable: 1
  selector:
    matchLabels:
      app: queue-processor
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: rabbitmq-pdb
  namespace: default
  labels:
    app: rabbitmq
    managed-by: terraform
spec:
  # For stateful applications, ensure high availability
  minAvailable: 2
  selector:
    matchLabels:
      app: rabbitmq
