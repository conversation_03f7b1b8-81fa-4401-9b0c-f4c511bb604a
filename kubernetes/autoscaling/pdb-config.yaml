apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: tenant-app-pdb
  namespace: tenant-system
  labels:
    app.kubernetes.io/managed-by: terraform
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: tenant-app

---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: tenant-api-pdb
  namespace: tenant-system
  labels:
    app.kubernetes.io/managed-by: terraform
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: tenant-api

---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: tenant-queue-processor-pdb
  namespace: tenant-system
  labels:
    app.kubernetes.io/managed-by: terraform
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: tenant-queue-processor

---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: tenant-database-pdb
  namespace: tenant-system
  labels:
    app.kubernetes.io/managed-by: terraform
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: tenant-database
