---
# Horizontal Pod Autoscaler for tenant pods
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: tenant-app-hpa
  namespace: ${TENANT_NAMESPACE}
  labels:
    app: tenant-app
    tenant: ${TENANT_ID}
    managed-by: terraform
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: ${TENANT_ID}-app
  minReplicas: 1
  maxReplicas: 5
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 75
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 50
        periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 30
      policies:
      - type: Percent
        value: 100
        periodSeconds: 30
      - type: Pods
        value: 2
        periodSeconds: 30
      selectPolicy: Max

---
# Vertical Pod Autoscaler for tenant pods
apiVersion: autoscaling.k8s.io/v1
kind: VerticalPodAutoscaler
metadata:
  name: tenant-app-vpa
  namespace: ${TENANT_NAMESPACE}
  labels:
    app: tenant-app
    tenant: ${TENANT_ID}
    managed-by: terraform
spec:
  targetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: ${TENANT_ID}-app
  updatePolicy:
    updateMode: "Auto"  # Options: "Off", "Initial", "Auto"
  resourcePolicy:
    containerPolicies:
    - containerName: '*'
      minAllowed:
        cpu: 30m
        memory: 48Mi
      maxAllowed:
        cpu: 300m
        memory: 384Mi
      controlledResources: ["cpu", "memory"]

---
# Pod Disruption Budget for tenant pods
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: tenant-app-pdb
  namespace: ${TENANT_NAMESPACE}
  labels:
    app: tenant-app
    tenant: ${TENANT_ID}
    managed-by: terraform
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: ${TENANT_ID}-app
