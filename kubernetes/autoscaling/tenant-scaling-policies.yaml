apiVersion: keda.sh/v1alpha1
kind: ScaledObject
metadata:
  name: tenant-basic-scaling-policy
  namespace: tenant-system
  labels:
    tier: basic
    app.kubernetes.io/managed-by: terraform
spec:
  scaleTargetRef:
    name: tenant-app
  minReplicaCount: 1
  maxReplicaCount: 3
  pollingInterval: 30
  cooldownPeriod: 300
  triggers:
  - type: cpu
    metadata:
      type: Utilization
      value: "80"
  - type: memory
    metadata:
      type: Utilization
      value: "80"
---
apiVersion: keda.sh/v1alpha1
kind: ScaledObject
metadata:
  name: tenant-standard-scaling-policy
  namespace: tenant-system
  labels:
    tier: standard
    app.kubernetes.io/managed-by: terraform
spec:
  scaleTargetRef:
    name: tenant-app
  minReplicaCount: 2
  maxReplicaCount: 5
  pollingInterval: 15
  cooldownPeriod: 300
  triggers:
  - type: cpu
    metadata:
      type: Utilization
      value: "70"
  - type: memory
    metadata:
      type: Utilization
      value: "70"
  - type: prometheus
    metadata:
      serverAddress: http://prometheus-server.monitoring.svc.cluster.local:9090
      metricName: http_requests_per_second
      query: sum(rate(istio_requests_total{destination_service_name="tenant-app"}[2m]))
      threshold: "50"
---
apiVersion: keda.sh/v1alpha1
kind: ScaledObject
metadata:
  name: tenant-premium-scaling-policy
  namespace: tenant-system
  labels:
    tier: premium
    app.kubernetes.io/managed-by: terraform
spec:
  scaleTargetRef:
    name: tenant-app
  minReplicaCount: 3
  maxReplicaCount: 10
  pollingInterval: 10
  cooldownPeriod: 300
  advanced:
    restoreToOriginalReplicaCount: true
    horizontalPodAutoscalerConfig:
      behavior:
        scaleDown:
          stabilizationWindowSeconds: 300
          policies:
          - type: Percent
            value: 50
            periodSeconds: 60
        scaleUp:
          stabilizationWindowSeconds: 0
          policies:
          - type: Percent
            value: 100
            periodSeconds: 60
          - type: Pods
            value: 5
            periodSeconds: 60
          selectPolicy: Max
  triggers:
  - type: cpu
    metadata:
      type: Utilization
      value: "60"
  - type: memory
    metadata:
      type: Utilization
      value: "60"
  - type: prometheus
    metadata:
      serverAddress: http://prometheus-server.monitoring.svc.cluster.local:9090
      metricName: http_requests_per_second
      query: sum(rate(istio_requests_total{destination_service_name="tenant-app"}[2m]))
      threshold: "30"
  - type: prometheus
    metadata:
      serverAddress: http://prometheus-server.monitoring.svc.cluster.local:9090
      metricName: response_time_ms
      query: histogram_quantile(0.95, sum(rate(istio_request_duration_milliseconds_bucket{destination_service_name="tenant-app"}[2m])) by (le))
      threshold: "200"
---
apiVersion: autoscaling.k8s.io/v1
kind: VerticalPodAutoscaler
metadata:
  name: tenant-basic-vpa
  namespace: tenant-system
  labels:
    tier: basic
    app.kubernetes.io/managed-by: terraform
spec:
  targetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: tenant-app
  updatePolicy:
    updateMode: "Auto"
  resourcePolicy:
    containerPolicies:
    - containerName: '*'
      minAllowed:
        cpu: 50m
        memory: 64Mi
      maxAllowed:
        cpu: 200m
        memory: 256Mi
      controlledResources: ["cpu", "memory"]
---
apiVersion: autoscaling.k8s.io/v1
kind: VerticalPodAutoscaler
metadata:
  name: tenant-standard-vpa
  namespace: tenant-system
  labels:
    tier: standard
    app.kubernetes.io/managed-by: terraform
spec:
  targetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: tenant-app
  updatePolicy:
    updateMode: "Auto"
  resourcePolicy:
    containerPolicies:
    - containerName: '*'
      minAllowed:
        cpu: 100m
        memory: 128Mi
      maxAllowed:
        cpu: 500m
        memory: 512Mi
      controlledResources: ["cpu", "memory"]
---
apiVersion: autoscaling.k8s.io/v1
kind: VerticalPodAutoscaler
metadata:
  name: tenant-premium-vpa
  namespace: tenant-system
  labels:
    tier: premium
    app.kubernetes.io/managed-by: terraform
spec:
  targetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: tenant-app
  updatePolicy:
    updateMode: "Auto"
  resourcePolicy:
    containerPolicies:
    - containerName: '*'
      minAllowed:
        cpu: 200m
        memory: 256Mi
      maxAllowed:
        cpu: 1000m
        memory: 1Gi
      controlledResources: ["cpu", "memory"]
