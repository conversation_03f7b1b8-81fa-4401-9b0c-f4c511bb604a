apiVersion: autoscaling.k8s.io/v1
kind: VerticalPodAutoscaler
metadata:
  name: tenant-app-vpa
  namespace: tenant-system
  labels:
    app.kubernetes.io/managed-by: terraform
spec:
  targetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: tenant-app
  updatePolicy:
    updateMode: "Auto"
  resourcePolicy:
    containerPolicies:
    - containerName: '*'
      minAllowed:
        cpu: 50m
        memory: 64Mi
      maxAllowed:
        cpu: 500m
        memory: 512Mi
      controlledResources: ["cpu", "memory"]

---
apiVersion: autoscaling.k8s.io/v1
kind: VerticalPodAutoscaler
metadata:
  name: tenant-api-vpa
  namespace: tenant-system
  labels:
    app.kubernetes.io/managed-by: terraform
spec:
  targetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: tenant-api
  updatePolicy:
    updateMode: "Auto"
  resourcePolicy:
    containerPolicies:
    - containerName: '*'
      minAllowed:
        cpu: 100m
        memory: 128Mi
      maxAllowed:
        cpu: 1000m
        memory: 1Gi
      controlledResources: ["cpu", "memory"]

---
apiVersion: autoscaling.k8s.io/v1
kind: VerticalPodAutoscaler
metadata:
  name: tenant-queue-processor-vpa
  namespace: tenant-system
  labels:
    app.kubernetes.io/managed-by: terraform
spec:
  targetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: tenant-queue-processor
  updatePolicy:
    updateMode: "Auto"
  resourcePolicy:
    containerPolicies:
    - containerName: '*'
      minAllowed:
        cpu: 50m
        memory: 64Mi
      maxAllowed:
        cpu: 500m
        memory: 512Mi
      controlledResources: ["cpu", "memory"]

---
apiVersion: autoscaling.k8s.io/v1
kind: VerticalPodAutoscaler
metadata:
  name: tenant-database-vpa
  namespace: tenant-system
  labels:
    app.kubernetes.io/managed-by: terraform
spec:
  targetRef:
    apiVersion: apps/v1
    kind: StatefulSet
    name: tenant-database
  updatePolicy:
    updateMode: "Off" # Recommendation only mode for database
  resourcePolicy:
    containerPolicies:
    - containerName: '*'
      minAllowed:
        cpu: 100m
        memory: 256Mi
      maxAllowed:
        cpu: 2000m
        memory: 4Gi
      controlledResources: ["cpu", "memory"]
