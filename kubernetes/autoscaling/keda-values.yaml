# KEDA Helm chart values
# This file contains the configuration for KEDA (Kubernetes Event-driven Autoscaling)

# Operator configuration
operator:
  name: keda-operator
  replicaCount: 1
  logLevel: info
  logEncoder: console
  logFormat: text
  resources:
    limits:
      cpu: 100m
      memory: 128Mi
    requests:
      cpu: 50m
      memory: 64Mi

# Metrics server configuration
metricsServer:
  name: keda-metrics-apiserver
  replicaCount: 1
  logLevel: info
  resources:
    limits:
      cpu: 100m
      memory: 128Mi
    requests:
      cpu: 50m
      memory: 64Mi

# Service account configuration
serviceAccount:
  create: true
  name: keda-operator
  annotations: {}

# RBAC configuration
rbac:
  create: true

# Pod security context
podSecurityContext:
  runAsNonRoot: true
  runAsUser: 1001
  fsGroup: 1001

# Security context
securityContext:
  allowPrivilegeEscalation: false
  runAsNonRoot: true
  runAsUser: 1001
  capabilities:
    drop:
      - ALL
  seccompProfile:
    type: RuntimeDefault

# Node selector
nodeSelector: {}

# Tolerations
tolerations: []

# Affinity
affinity: {}

# Webhook configuration
webhookCertificateRegeneration:
  enabled: true

# Image pull secrets
imagePullSecrets: []

# Prometheus integration
prometheus:
  metricServer:
    enabled: true
    port: 8080
    portName: metrics
    path: /metrics
    serviceMonitor:
      enabled: true
      labels:
        release: prometheus-operator
  operator:
    enabled: true
    port: 8080
    portName: metrics
    path: /metrics
    serviceMonitor:
      enabled: true
      labels:
        release: prometheus-operator
