apiVersion: keda.sh/v1alpha1
kind: ScaledObject
metadata:
  name: ${NAME}
  namespace: ${NAMESPACE}
  labels:
    app: ${APP}
    deployedBy: keda
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: ${DEPLOYMENT_NAME}
  minReplicaCount: ${MIN_REPLICAS}
  maxReplicaCount: ${MAX_REPLICAS}
  pollingInterval: ${POLLING_INTERVAL}
  cooldownPeriod: ${COOLDOWN_PERIOD}
  advanced:
    horizontalPodAutoscalerConfig:
      behavior:
        scaleDown:
          stabilizationWindowSeconds: ${SCALE_DOWN_STABILIZATION_WINDOW}
          policies:
          - type: ${SCALE_DOWN_POLICY_TYPE}
            value: ${SCALE_DOWN_POLICY_VALUE}
            periodSeconds: ${SCALE_DOWN_PERIOD_SECONDS}
        scaleUp:
          stabilizationWindowSeconds: ${SCALE_UP_STABILIZATION_WINDOW}
          policies:
          - type: ${SCALE_UP_POLICY_TYPE}
            value: ${SCALE_UP_POLICY_VALUE}
            periodSeconds: ${SCALE_UP_PERIOD_SECONDS}
          selectPolicy: ${SCALE_UP_SELECT_POLICY}
  triggers:
  ${TRIGGERS}
