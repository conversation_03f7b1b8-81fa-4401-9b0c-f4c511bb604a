apiVersion: autoscaling.k8s.io/v1
kind: VerticalPodAutoscaler
metadata:
  name: monitoring-vpa
  namespace: monitoring
  labels:
    app: monitoring
    component: prometheus
    managed-by: terraform
spec:
  targetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: prometheus-server
  updatePolicy:
    updateMode: "Auto"
  resourcePolicy:
    containerPolicies:
    - containerName: '*'
      minAllowed:
        cpu: 50m
        memory: 64Mi
      maxAllowed:
        cpu: 200m
        memory: 256Mi
      controlledResources: ["cpu", "memory"]
---
apiVersion: autoscaling.k8s.io/v1
kind: VerticalPodAutoscaler
metadata:
  name: security-vpa
  namespace: security
  labels:
    app: security
    component: all-in-one-security
    managed-by: terraform
spec:
  targetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: all-in-one-security
  updatePolicy:
    updateMode: "Auto"
  resourcePolicy:
    containerPolicies:
    - containerName: 'trivy'
      minAllowed:
        cpu: 50m
        memory: 64Mi
      maxAllowed:
        cpu: 100m
        memory: 128Mi
      controlledResources: ["cpu", "memory"]
    - containerName: 'falco'
      minAllowed:
        cpu: 50m
        memory: 128Mi
      maxAllowed:
        cpu: 100m
        memory: 256Mi
      controlledResources: ["cpu", "memory"]
    - containerName: 'fluentd'
      minAllowed:
        cpu: 50m
        memory: 64Mi
      maxAllowed:
        cpu: 100m
        memory: 128Mi
      controlledResources: ["cpu", "memory"]
---
apiVersion: autoscaling.k8s.io/v1
kind: VerticalPodAutoscaler
metadata:
  name: webapp-vpa
  namespace: default
  labels:
    app: webapp
    managed-by: terraform
spec:
  targetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: webapp
  updatePolicy:
    # "Auto" mode applies changes automatically
    # "Initial" mode only applies on pod creation
    # "Off" mode only generates recommendations without applying
    updateMode: "Auto"
  resourcePolicy:
    containerPolicies:
    - containerName: '*'
      minAllowed:
        cpu: 100m
        memory: 128Mi
      maxAllowed:
        cpu: 500m
        memory: 512Mi
      controlledResources: ["cpu", "memory"]
---
apiVersion: autoscaling.k8s.io/v1
kind: VerticalPodAutoscaler
metadata:
  name: queue-processor-vpa
  namespace: default
  labels:
    app: queue-processor
    managed-by: terraform
spec:
  targetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: queue-processor
  updatePolicy:
    updateMode: "Auto"
  resourcePolicy:
    containerPolicies:
    - containerName: '*'
      minAllowed:
        cpu: 100m
        memory: 128Mi
      maxAllowed:
        cpu: 1000m
        memory: 1Gi
      controlledResources: ["cpu", "memory"]
---
apiVersion: autoscaling.k8s.io/v1
kind: VerticalPodAutoscaler
metadata:
  name: rabbitmq-vpa
  namespace: default
  labels:
    app: rabbitmq
    managed-by: terraform
spec:
  targetRef:
    apiVersion: apps/v1
    kind: StatefulSet
    name: rabbitmq
  updatePolicy:
    # Use "Off" for stateful applications to avoid disruption
    # Only generate recommendations
    updateMode: "Off"
  resourcePolicy:
    containerPolicies:
    - containerName: '*'
      minAllowed:
        cpu: 200m
        memory: 256Mi
      maxAllowed:
        cpu: 1000m
        memory: 2Gi
      controlledResources: ["cpu", "memory"]
