apiVersion: keda.sh/v1alpha1
kind: ScaledObject
metadata:
  name: tenant-app-scaledobject
  namespace: ${TENANT_NAMESPACE}
  labels:
    app: tenant-app
    tenant: ${TENANT_ID}
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: ${TENANT_ID}-app
  minReplicaCount: 1
  maxReplicaCount: 10
  pollingInterval: 15
  cooldownPeriod: 300
  advanced:
    horizontalPodAutoscalerConfig:
      behavior:
        scaleDown:
          stabilizationWindowSeconds: 300
          policies:
          - type: Percent
            value: 50
            periodSeconds: 60
        scaleUp:
          stabilizationWindowSeconds: 0
          policies:
          - type: Percent
            value: 100
            periodSeconds: 30
          - type: Pods
            value: 2
            periodSeconds: 30
          selectPolicy: Max
  triggers:
  # CPU-based scaling
  - type: cpu
    metricType: Utilization
    metadata:
      value: "70"
  # Memory-based scaling
  - type: memory
    metricType: Utilization
    metadata:
      value: "70"
  # HTTP request-based scaling
  - type: metrics-api
    metadata:
      targetValue: "50"
      url: "http://prometheus-server.monitoring.svc.cluster.local:9090/api/v1/query"
      valueLocation: "data.result[0].value[1]"
      query: "sum(rate(http_requests_total{namespace=\"${TENANT_NAMESPACE}\", pod=~\"${TENANT_ID}-app-.*\"}[2m]))"
  # Queue-based scaling (if applicable)
  - type: aws-sqs-queue
    metadata:
      queueURL: https://sqs.${AWS_REGION}.amazonaws.com/${AWS_ACCOUNT_ID}/${TENANT_ID}-queue
      queueLength: "5"
      awsRegion: ${AWS_REGION}
      identityOwner: pod
