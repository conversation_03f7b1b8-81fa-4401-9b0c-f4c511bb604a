# Copyright Istio Authors
#
#   Licensed under the Apache License, Version 2.0 (the "License");
#   you may not use this file except in compliance with the License.
#   You may obtain a copy of the License at
#
#       http://www.apache.org/licenses/LICENSE-2.0
#
#   Unless required by applicable law or agreed to in writing, software
#   distributed under the License is distributed on an "AS IS" BASIS,
#   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#   See the License for the specific language governing permissions and
#   limitations under the License.

##################################################################################################
# Notsleep service - based on the sleep service but has its own identity and affinity rule
##################################################################################################
apiVersion: v1
kind: ServiceAccount
metadata:
  name: notsleep
---
apiVersion: v1
kind: Service
metadata:
  name: notsleep
  labels:
    app: notsleep
    service: notsleep
spec:
  ports:
  - port: 80
    name: http
  selector:
    app: notsleep
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: notsleep
spec:
  replicas: 1
  selector:
    matchLabels:
      app: notsleep
  template:
    metadata:
      labels:
        app: notsleep
    spec:
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values:
                  - productpage
              topologyKey: kubernetes.io/hostname 
      terminationGracePeriodSeconds: 0
      serviceAccountName: notsleep
      containers:
      - name: notsleep
        image: curlimages/curl
        command: ["/bin/sleep", "3650d"]
        imagePullPolicy: IfNotPresent
        volumeMounts:
        - mountPath: /etc/sleep/tls
          name: secret-volume
      volumes:
      - name: secret-volume
        secret:
          secretName: notsleep-secret
          optional: true
---
