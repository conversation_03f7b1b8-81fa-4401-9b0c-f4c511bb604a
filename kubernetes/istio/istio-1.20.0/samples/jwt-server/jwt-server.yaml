# Copyright Istio Authors
#
#   Licensed under the Apache License, Version 2.0 (the "License");
#   you may not use this file except in compliance with the License.
#   You may obtain a copy of the License at
#
#       http://www.apache.org/licenses/LICENSE-2.0
#
#   Unless required by applicable law or agreed to in writing, software
#   distributed under the License is distributed on an "AS IS" BASIS,
#   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#   See the License for the specific language governing permissions and
#   limitations under the License.

# Example configurations for deploying a jwt-server server separately in the mesh.

apiVersion: v1
kind: Service
metadata:
  name: jwt-server
  labels:
    app: jwt-server
spec:
  ports:
  - name: http
    port: 8000
    targetPort: 8000
  - name: https
    port: 8443
    targetPort: 8443
  selector:
    app: jwt-server
---
apiVersion: v1
kind: Secret
metadata:
  name: jwt-cert-key-secret
# command to generate certificate
# use the generated server.crt, server.key by following https://github.com/istio/istio/blob/master/samples/jwt-server/testdata/README.MD
stringData: 
  server.crt: |
    -----<PERSON><PERSON><PERSON> CERTIFICATE-----
    MIIDjzCCAnegAwIBAgIUfIuuQDfWakIpZ7bZAuuLUWhSm2AwDQYJKoZIhvcNAQEL
    BQAwRjELMAkGA1UEBhMCVVMxCzAJBgNVBAgMAkFaMRMwEQYDVQQKDApBY21lLCBJ
    bmMuMRUwEwYDVQQDDAxBY21lIFJvb3QgQ0EwHhcNMjExMTE3MDQ0NDE2WhcNMzEx
    MTE1MDQ0NDE2WjA/MQswCQYDVQQGEwJVUzELMAkGA1UECAwCQVoxEzARBgNVBAoM
    CkFjbWUsIEluYy4xDjAMBgNVBAMMBSouY29tMIIBIjANBgkqhkiG9w0BAQEFAAOC
    AQ8AMIIBCgKCAQEAv3qsDI+3Fc65EuuPnKG4BN0dLZZy+wFNxruszYRg0foP9kUQ
    rUUv12uu/y2Rguf09y9mXXzGc51kwU5TIhVarYPBIa46MLMBBroF908VX9ng4Q9M
    ta+rU10e9xugRRnCDf1ZMlQJB/7pmnF21vw6gdmRt7vMLKiHQuN9BI+042Z/NiiF
    T7xCDDz+HvhGnn+vDv53h6LPzwNM2zGLSIPaV5xkYs0fYvs5Y2pUGonrra5hGoRq
    JzOZ3SNfKtaQ3AXrf/+kikJGFA/GmzZuhW26Nygl/kYgx7l+g3uTXOz0hN434nF6
    Cc7EyuvD37lAsgw1w48poTnDUijV5Cx6yA8FHQIDAQABo3wwejB4BgNVHREEcTBv
    ggpqd3Qtc2VydmVygglsb2NhbGhvc3SCF2p3dC1zZXJ2ZXIuaXN0aW8tc3lzdGVt
    ghJqd3Qtc2VydmVyLmRlZmF1bHSCKWp3dC1zZXJ2ZXIuaXN0aW8tc3lzdGVtLnN2
    Yy5jbHVzdGVyLmxvY2FsMA0GCSqGSIb3DQEBCwUAA4IBAQC26tlBXaF+chVS3f8w
    Tv1D1lgXgJ/ROozqlSMe5BGDuOgsVtWQeqpMIXxEx8w6fXUF0TaMYxp3sC4D3Ql9
    W+PALf9Zpy+vv6vxoKkrnKiXvOiuYkLJhaVDkzvj6j6yMjxUk5a9ehDZ0gKwXf+m
    Ei35D8xKtPdz/FaB7qgN2mu7V47oFizon7jLLqAvlWIIQ7Pku+XfjraDPtjxUj4u
    5qSrIfSWAeuJSEsSlGPyYJCFvqFNQYW0y8y7fCCQo7FObHfBmpp7kG2BViuLxebW
    zfi4K3gDCpR8lWiNEjm4NamQ07OpCtmLZfaueZH/vSXXVVbs6VCsb6nJqJrGDc5t
    K/xK
    -----END CERTIFICATE-----
  server.key: |
***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: jwt-server
spec:
  replicas: 1
  selector:
    matchLabels:
      app: jwt-server
  template:
    metadata:
      labels:
        app: jwt-server
    spec:
      containers:
      - image: gcr.io/istio-testing/jwt-server:0.7
        imagePullPolicy: IfNotPresent
        name: jwt-server
        volumeMounts:
          - name: certkeysecretresource
            mountPath: "/app/https/secretresources"
            readOnly: true
        args: ["-https", "8443", "-cert", "/app/https/secretresources/server.crt", "-key", "/app/https/secretresources/server.key"]
        ports:
        - containerPort: 8000
        - containerPort: 8443
      volumes:
      - name: certkeysecretresource
        secret:
          secretName: jwt-cert-key-secret
          defaultMode: 0400
---