apiVersion: install.istio.io/v1alpha1
kind: IstioOperator
spec:
  components:
    pilot:
      k8s:
        overlays:
        - kind: Deployment
          name: istiod
          patches:
          - path: spec.template.spec.containers.[name:discovery].args.[30m]
            value: "60m" # OVERRIDDEN
          - path: spec.template.spec.containers.[name:discovery].ports.[containerPort:8080].containerPort
            value: 8090 # OVERRIDDEN
        - kind: Service
          name: istiod
          patches:
          - path: spec.ports.[name:grpc-xds].port
            value: 15099 # OVERRIDDEN
