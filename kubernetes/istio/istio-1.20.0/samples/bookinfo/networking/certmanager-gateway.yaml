apiVersion: networking.istio.io/v1alpha3
kind: Gateway
metadata:
  name: cert-manager-gateway
  namespace: istio-system
spec:
  selector:
    istio: ingressgateway
  servers:
  - port:
      number: 80
      name: http
      protocol: HTTP
    hosts:
    - "*"
---
apiVersion: networking.istio.io/v1alpha3
kind: VirtualService
metadata:
  name: cert-manager
  namespace: istio-system
spec:
  hosts:
  - "*"
  gateways:
  - cert-manager-gateway
  http:
  - match:
    - uri:
        prefix: /.well-known/acme-challenge/
    route:
    - destination:
        host: cert-manager-resolver
        port:
          number: 8089
