# Copyright Istio Authors
#
#   Licensed under the Apache License, Version 2.0 (the "License");
#   you may not use this file except in compliance with the License.
#   You may obtain a copy of the License at
#
#       http://www.apache.org/licenses/LICENSE-2.0
#
#   Unless required by applicable law or agreed to in writing, software
#   distributed under the License is distributed on an "AS IS" BASIS,
#   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#   See the License for the specific language governing permissions and
#   limitations under the License.

##################################################################################################
# Details service v2
##################################################################################################
apiVersion: apps/v1
kind: Deployment
metadata:
  name: details-v2
  labels:
    app: details
    version: v2
spec:
  replicas: 1
  selector:
    matchLabels:
      app: details
      version: v2
  template:
    metadata:
      labels:
        app: details
        version: v2
    spec:
      containers:
      - name: details
        image: docker.io/istio/examples-bookinfo-details-v2:1.18.0
        imagePullPolicy: IfNotPresent
        ports:
        - containerPort: 9080
        env:
        - name: DO_NOT_ENCRYPT
          value: "true"
---
