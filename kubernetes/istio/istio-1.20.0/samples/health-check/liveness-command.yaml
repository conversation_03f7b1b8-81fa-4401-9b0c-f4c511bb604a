# Copyright Istio Authors
#
#   Licensed under the Apache License, Version 2.0 (the "License");
#   you may not use this file except in compliance with the License.
#   You may obtain a copy of the License at
#
#       http://www.apache.org/licenses/LICENSE-2.0
#
#   Unless required by applicable law or agreed to in writing, software
#   distributed under the License is distributed on an "AS IS" BASIS,
#   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#   See the License for the specific language governing permissions and
#   limitations under the License.

##################################################################################################
# Liveness service
##################################################################################################
apiVersion: v1
kind: Service
metadata:
  name: liveness
  labels:
    app: liveness
    service: liveness
spec:
  ports:
  - port: 80
    name: http
  selector:
    app: liveness
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: liveness
spec:
  selector:
    matchLabels:
      app: liveness
  template:
    metadata:
      labels:
        app: liveness
    spec:
      containers:
      - name: liveness
        image: registry.k8s.io/busybox
        args:
        - /bin/sh
        - -c
        - touch /tmp/healthy; sleep 3600
        livenessProbe:
          exec:
            command:
            - cat
            - /tmp/healthy
          initialDelaySeconds: 5
          periodSeconds: 5
