apiVersion: networking.istio.io/v1alpha3
kind: Gateway
metadata:
  name: istiod-gateway
spec:
  selector:
    istio: eastwestgateway
  servers:
    - port:
        name: https-istiod
        number: 15012
        protocol: https
      tls:
        mode: SIMPLE
        # use a valid credential here
        credentialName: "$CREDENTIAL_NAME"
      hosts:
      # use a valid gateway host and domain for istiod
      - "ISTIOD-HOST.DOMAIN"
    - port:
        name: https-istiodwebhook
        number: 15017
        protocol: https
      tls:
        mode: SIMPLE
        # use a valid credential here
        credentialName: "$CREDENTIAL_NAME"
      hosts:
      # use a valid gateway host and domain for istiod
      - "ISTIOD-HOST.DOMAIN"
---
apiVersion: networking.istio.io/v1alpha3
kind: VirtualService
metadata:
  name: istiod-vs
spec:
  hosts:
  # use a valid gateway host and domain for istiod
  - "ISTIOD-HOST.DOMAIN"
  gateways:
  - istiod-gateway
  http:
  - match:
    - port: 15012
    route:
    - destination:
        host: istiod.istio-system.svc.cluster.local
        port:
          number: 15012
  - match:
    - port: 15017
    route:
    - destination:
        host: istiod.istio-system.svc.cluster.local
        port:
          number: 443
---
apiVersion: networking.istio.io/v1alpha3
kind: DestinationRule
metadata:
  name: istiod-dr
spec:
  host: istiod.istio-system.svc.cluster.local
  trafficPolicy:
    portLevelSettings:
    - port:
        number: 15012
      tls:
        mode: SIMPLE
      connectionPool:
        http:
          h2UpgradePolicy: UPGRADE        
    - port:
        number: 443
      tls:
        mode: SIMPLE
