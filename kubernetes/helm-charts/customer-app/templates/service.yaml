apiVersion: v1
kind: Service
metadata:
  name: {{ .Values.customer.id }}-app
  namespace: customer-{{ .Values.customer.id }}
  labels:
    app: {{ .Values.customer.id }}-app
    customer: {{ .Values.customer.id }}
    environment: {{ .Values.customer.environment | lower }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: http
      protocol: TCP
      name: http
  selector:
    app: {{ .Values.customer.id }}-app
