apiVersion: v1
items:
- apiVersion: apps/v1
  kind: Deployment
  metadata:
    annotations:
      deployment.kubernetes.io/revision: "1"
      meta.helm.sh/release-name: goldilocks
      meta.helm.sh/release-namespace: autoscaling
    creationTimestamp: "2025-07-08T07:57:13Z"
    generation: 2
    labels:
      app.kubernetes.io/component: controller
      app.kubernetes.io/instance: goldilocks
      app.kubernetes.io/managed-by: Helm
      app.kubernetes.io/name: goldilocks
      helm.sh/chart: goldilocks-9.0.2
    name: goldilocks-controller
    namespace: autoscaling
    resourceVersion: "24386736"
    uid: aedd3ce0-1b1c-42f1-a261-6bf3edc22b9e
  spec:
    progressDeadlineSeconds: 600
    replicas: 0
    revisionHistoryLimit: 10
    selector:
      matchLabels:
        app.kubernetes.io/component: controller
        app.kubernetes.io/instance: goldilocks
        app.kubernetes.io/name: goldilocks
    strategy:
      rollingUpdate:
        maxSurge: 25%
        maxUnavailable: 25%
      type: RollingUpdate
    template:
      metadata:
        creationTimestamp: null
        labels:
          app.kubernetes.io/component: controller
          app.kubernetes.io/instance: goldilocks
          app.kubernetes.io/name: goldilocks
      spec:
        containers:
        - command:
          - /goldilocks
          - controller
          - -v2
          image: us-docker.pkg.dev/fairwinds-ops/oss/goldilocks:v4.13.0
          imagePullPolicy: Always
          name: goldilocks
          resources:
            limits:
              cpu: 200m
              memory: 500Mi
            requests:
              cpu: 50m
              memory: 200Mi
          securityContext:
            allowPrivilegeEscalation: false
            capabilities:
              drop:
              - ALL
            readOnlyRootFilesystem: true
            runAsNonRoot: true
            runAsUser: 10324
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
        dnsPolicy: ClusterFirst
        restartPolicy: Always
        schedulerName: default-scheduler
        securityContext:
          seccompProfile:
            type: RuntimeDefault
        serviceAccount: goldilocks-controller
        serviceAccountName: goldilocks-controller
        terminationGracePeriodSeconds: 30
  status:
    conditions:
    - lastTransitionTime: "2025-07-08T07:57:13Z"
      lastUpdateTime: "2025-07-08T07:57:20Z"
      message: ReplicaSet "goldilocks-controller-76d6bf556d" has successfully progressed.
      reason: NewReplicaSetAvailable
      status: "True"
      type: Progressing
    - lastTransitionTime: "2025-07-11T09:06:42Z"
      lastUpdateTime: "2025-07-11T09:06:42Z"
      message: Deployment has minimum availability.
      reason: MinimumReplicasAvailable
      status: "True"
      type: Available
    observedGeneration: 2
- apiVersion: apps/v1
  kind: Deployment
  metadata:
    annotations:
      deployment.kubernetes.io/revision: "1"
      meta.helm.sh/release-name: goldilocks
      meta.helm.sh/release-namespace: autoscaling
    creationTimestamp: "2025-07-08T07:57:13Z"
    generation: 2
    labels:
      app.kubernetes.io/component: dashboard
      app.kubernetes.io/instance: goldilocks
      app.kubernetes.io/managed-by: Helm
      app.kubernetes.io/name: goldilocks
      helm.sh/chart: goldilocks-9.0.2
    name: goldilocks-dashboard
    namespace: autoscaling
    resourceVersion: "24386756"
    uid: 1a2eb564-e8ad-40c0-b76c-601d1949ab20
  spec:
    progressDeadlineSeconds: 600
    replicas: 0
    revisionHistoryLimit: 10
    selector:
      matchLabels:
        app.kubernetes.io/component: dashboard
        app.kubernetes.io/instance: goldilocks
        app.kubernetes.io/name: goldilocks
    strategy:
      rollingUpdate:
        maxSurge: 25%
        maxUnavailable: 25%
      type: RollingUpdate
    template:
      metadata:
        creationTimestamp: null
        labels:
          app.kubernetes.io/component: dashboard
          app.kubernetes.io/instance: goldilocks
          app.kubernetes.io/name: goldilocks
      spec:
        containers:
        - command:
          - /goldilocks
          - dashboard
          - --exclude-containers=linkerd-proxy,istio-proxy
          - -v2
          image: us-docker.pkg.dev/fairwinds-ops/oss/goldilocks:v4.13.0
          imagePullPolicy: Always
          livenessProbe:
            failureThreshold: 3
            httpGet:
              path: /health
              port: http
              scheme: HTTP
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 1
          name: goldilocks
          ports:
          - containerPort: 8080
            name: http
            protocol: TCP
          readinessProbe:
            failureThreshold: 3
            httpGet:
              path: /health
              port: http
              scheme: HTTP
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 1
          resources:
            limits:
              cpu: 200m
              memory: 500Mi
            requests:
              cpu: 50m
              memory: 200Mi
          securityContext:
            allowPrivilegeEscalation: false
            capabilities:
              drop:
              - ALL
            readOnlyRootFilesystem: true
            runAsNonRoot: true
            runAsUser: 10324
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
        dnsPolicy: ClusterFirst
        restartPolicy: Always
        schedulerName: default-scheduler
        securityContext:
          seccompProfile:
            type: RuntimeDefault
        serviceAccount: goldilocks-dashboard
        serviceAccountName: goldilocks-dashboard
        terminationGracePeriodSeconds: 30
  status:
    conditions:
    - lastTransitionTime: "2025-07-08T07:57:13Z"
      lastUpdateTime: "2025-07-08T07:57:18Z"
      message: ReplicaSet "goldilocks-dashboard-6f485ffd98" has successfully progressed.
      reason: NewReplicaSetAvailable
      status: "True"
      type: Progressing
    - lastTransitionTime: "2025-07-09T15:36:32Z"
      lastUpdateTime: "2025-07-09T15:36:32Z"
      message: Deployment has minimum availability.
      reason: MinimumReplicasAvailable
      status: "True"
      type: Available
    observedGeneration: 2
- apiVersion: apps/v1
  kind: Deployment
  metadata:
    annotations:
      deployment.kubernetes.io/revision: "1"
      meta.helm.sh/release-name: vpa
      meta.helm.sh/release-namespace: autoscaling
    creationTimestamp: "2025-07-08T07:54:05Z"
    generation: 2
    labels:
      app.kubernetes.io/component: admission-controller
      app.kubernetes.io/instance: vpa
      app.kubernetes.io/managed-by: Helm
      app.kubernetes.io/name: vpa
      app.kubernetes.io/version: 1.0.0
      helm.sh/chart: vpa-4.7.2
    name: vpa-admission-controller
    namespace: autoscaling
    resourceVersion: "24386714"
    uid: 2612446e-9f39-4291-a54f-805494920a00
  spec:
    progressDeadlineSeconds: 600
    replicas: 0
    revisionHistoryLimit: 10
    selector:
      matchLabels:
        app.kubernetes.io/component: admission-controller
        app.kubernetes.io/instance: vpa
        app.kubernetes.io/name: vpa
    strategy:
      rollingUpdate:
        maxSurge: 25%
        maxUnavailable: 25%
      type: RollingUpdate
    template:
      metadata:
        annotations:
          prometheus.io/port: "8080"
          prometheus.io/scrape: "true"
        creationTimestamp: null
        labels:
          app.kubernetes.io/component: admission-controller
          app.kubernetes.io/instance: vpa
          app.kubernetes.io/name: vpa
      spec:
        containers:
        - args:
          - --register-webhook=false
          - --webhook-service=vpa-webhook
          - --client-ca-file=/etc/tls-certs/ca
          - --tls-cert-file=/etc/tls-certs/cert
          - --tls-private-key=/etc/tls-certs/key
          env:
          - name: NAMESPACE
            valueFrom:
              fieldRef:
                apiVersion: v1
                fieldPath: metadata.namespace
          image: registry.k8s.io/autoscaling/vpa-admission-controller:1.0.0
          imagePullPolicy: Always
          livenessProbe:
            failureThreshold: 6
            httpGet:
              path: /health-check
              port: metrics
              scheme: HTTP
            periodSeconds: 5
            successThreshold: 1
            timeoutSeconds: 3
          name: vpa
          ports:
          - containerPort: 8000
            name: http
            protocol: TCP
          - containerPort: 8944
            name: metrics
            protocol: TCP
          readinessProbe:
            failureThreshold: 120
            httpGet:
              path: /health-check
              port: metrics
              scheme: HTTP
            periodSeconds: 5
            successThreshold: 1
            timeoutSeconds: 3
          resources:
            limits:
              cpu: 200m
              memory: 500Mi
            requests:
              cpu: 50m
              memory: 200Mi
          securityContext:
            allowPrivilegeEscalation: false
            capabilities:
              drop:
              - ALL
            readOnlyRootFilesystem: true
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /etc/tls-certs
            name: tls-certs
            readOnly: true
        dnsPolicy: ClusterFirst
        restartPolicy: Always
        schedulerName: default-scheduler
        securityContext:
          runAsNonRoot: true
          runAsUser: 65534
          seccompProfile:
            type: RuntimeDefault
        serviceAccount: vpa-admission-controller
        serviceAccountName: vpa-admission-controller
        terminationGracePeriodSeconds: 30
        volumes:
        - name: tls-certs
          secret:
            defaultMode: 420
            secretName: vpa-tls-secret
  status:
    conditions:
    - lastTransitionTime: "2025-07-08T07:54:05Z"
      lastUpdateTime: "2025-07-08T07:54:12Z"
      message: ReplicaSet "vpa-admission-controller-76c7d46fb" has successfully progressed.
      reason: NewReplicaSetAvailable
      status: "True"
      type: Progressing
    - lastTransitionTime: "2025-07-09T15:58:53Z"
      lastUpdateTime: "2025-07-09T15:58:53Z"
      message: Deployment has minimum availability.
      reason: MinimumReplicasAvailable
      status: "True"
      type: Available
    observedGeneration: 2
- apiVersion: apps/v1
  kind: Deployment
  metadata:
    annotations:
      deployment.kubernetes.io/revision: "1"
      meta.helm.sh/release-name: vpa
      meta.helm.sh/release-namespace: autoscaling
    creationTimestamp: "2025-07-08T07:54:05Z"
    generation: 2
    labels:
      app.kubernetes.io/component: recommender
      app.kubernetes.io/instance: vpa
      app.kubernetes.io/managed-by: Helm
      app.kubernetes.io/name: vpa
      app.kubernetes.io/version: 1.0.0
      helm.sh/chart: vpa-4.7.2
    name: vpa-recommender
    namespace: autoscaling
    resourceVersion: "24386638"
    uid: 662960ed-2668-4b86-afc2-519ad249ffe7
  spec:
    progressDeadlineSeconds: 600
    replicas: 0
    revisionHistoryLimit: 10
    selector:
      matchLabels:
        app.kubernetes.io/component: recommender
        app.kubernetes.io/instance: vpa
        app.kubernetes.io/name: vpa
    strategy:
      rollingUpdate:
        maxSurge: 25%
        maxUnavailable: 25%
      type: RollingUpdate
    template:
      metadata:
        annotations:
          prometheus.io/port: "8080"
          prometheus.io/scrape: "true"
        creationTimestamp: null
        labels:
          app.kubernetes.io/component: recommender
          app.kubernetes.io/instance: vpa
          app.kubernetes.io/name: vpa
      spec:
        containers:
        - args:
          - --pod-recommendation-min-cpu-millicores=10
          - --pod-recommendation-min-memory-mb=50
          - --v=4
          image: registry.k8s.io/autoscaling/vpa-recommender:1.0.0
          imagePullPolicy: Always
          livenessProbe:
            failureThreshold: 6
            httpGet:
              path: /health-check
              port: metrics
              scheme: HTTP
            periodSeconds: 5
            successThreshold: 1
            timeoutSeconds: 3
          name: vpa
          ports:
          - containerPort: 8942
            name: metrics
            protocol: TCP
          readinessProbe:
            failureThreshold: 120
            httpGet:
              path: /health-check
              port: metrics
              scheme: HTTP
            periodSeconds: 5
            successThreshold: 1
            timeoutSeconds: 3
          resources:
            limits:
              cpu: 200m
              memory: 1000Mi
            requests:
              cpu: 50m
              memory: 500Mi
          securityContext:
            allowPrivilegeEscalation: false
            capabilities:
              drop:
              - ALL
            readOnlyRootFilesystem: true
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
        dnsPolicy: ClusterFirst
        restartPolicy: Always
        schedulerName: default-scheduler
        securityContext:
          runAsNonRoot: true
          runAsUser: 65534
          seccompProfile:
            type: RuntimeDefault
        serviceAccount: vpa-recommender
        serviceAccountName: vpa-recommender
        terminationGracePeriodSeconds: 30
  status:
    conditions:
    - lastTransitionTime: "2025-07-08T07:54:05Z"
      lastUpdateTime: "2025-07-08T07:54:11Z"
      message: ReplicaSet "vpa-recommender-7c5f7869f6" has successfully progressed.
      reason: NewReplicaSetAvailable
      status: "True"
      type: Progressing
    - lastTransitionTime: "2025-07-09T15:58:54Z"
      lastUpdateTime: "2025-07-09T15:58:54Z"
      message: Deployment has minimum availability.
      reason: MinimumReplicasAvailable
      status: "True"
      type: Available
    observedGeneration: 2
- apiVersion: apps/v1
  kind: Deployment
  metadata:
    annotations:
      deployment.kubernetes.io/revision: "1"
      meta.helm.sh/release-name: vpa
      meta.helm.sh/release-namespace: autoscaling
    creationTimestamp: "2025-07-08T07:54:05Z"
    generation: 2
    labels:
      app.kubernetes.io/component: updater
      app.kubernetes.io/instance: vpa
      app.kubernetes.io/managed-by: Helm
      app.kubernetes.io/name: vpa
      app.kubernetes.io/version: 1.0.0
      helm.sh/chart: vpa-4.7.2
    name: vpa-updater
    namespace: autoscaling
    resourceVersion: "24386692"
    uid: 3752e065-6e30-41b5-84b0-35f23d7cfee5
  spec:
    progressDeadlineSeconds: 600
    replicas: 0
    revisionHistoryLimit: 10
    selector:
      matchLabels:
        app.kubernetes.io/component: updater
        app.kubernetes.io/instance: vpa
        app.kubernetes.io/name: vpa
    strategy:
      rollingUpdate:
        maxSurge: 25%
        maxUnavailable: 25%
      type: RollingUpdate
    template:
      metadata:
        annotations:
          prometheus.io/port: "8080"
          prometheus.io/scrape: "true"
        creationTimestamp: null
        labels:
          app.kubernetes.io/component: updater
          app.kubernetes.io/instance: vpa
          app.kubernetes.io/name: vpa
      spec:
        containers:
        - args:
          - --min-replicas=2
          env:
          - name: NAMESPACE
            valueFrom:
              fieldRef:
                apiVersion: v1
                fieldPath: metadata.namespace
          image: registry.k8s.io/autoscaling/vpa-updater:1.0.0
          imagePullPolicy: Always
          livenessProbe:
            failureThreshold: 6
            httpGet:
              path: /health-check
              port: metrics
              scheme: HTTP
            periodSeconds: 5
            successThreshold: 1
            timeoutSeconds: 3
          name: vpa
          ports:
          - containerPort: 8943
            name: metrics
            protocol: TCP
          readinessProbe:
            failureThreshold: 120
            httpGet:
              path: /health-check
              port: metrics
              scheme: HTTP
            periodSeconds: 5
            successThreshold: 1
            timeoutSeconds: 3
          resources:
            limits:
              cpu: 200m
              memory: 1000Mi
            requests:
              cpu: 50m
              memory: 500Mi
          securityContext:
            allowPrivilegeEscalation: false
            capabilities:
              drop:
              - ALL
            readOnlyRootFilesystem: true
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
        dnsPolicy: ClusterFirst
        restartPolicy: Always
        schedulerName: default-scheduler
        securityContext:
          runAsNonRoot: true
          runAsUser: 65534
          seccompProfile:
            type: RuntimeDefault
        serviceAccount: vpa-updater
        serviceAccountName: vpa-updater
        terminationGracePeriodSeconds: 30
  status:
    conditions:
    - lastTransitionTime: "2025-07-08T07:54:05Z"
      lastUpdateTime: "2025-07-08T07:54:10Z"
      message: ReplicaSet "vpa-updater-5c8f7ff886" has successfully progressed.
      reason: NewReplicaSetAvailable
      status: "True"
      type: Progressing
    - lastTransitionTime: "2025-07-09T16:00:28Z"
      lastUpdateTime: "2025-07-09T16:00:28Z"
      message: Deployment has minimum availability.
      reason: MinimumReplicasAvailable
      status: "True"
      type: Available
    observedGeneration: 2
- apiVersion: apps/v1
  kind: Deployment
  metadata:
    annotations:
      deployment.kubernetes.io/revision: "1"
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"apps/v1","kind":"Deployment","metadata":{"annotations":{},"name":"force-scale-up","namespace":"default"},"spec":{"replicas":8,"selector":{"matchLabels":{"app":"force-scale-up"}},"template":{"metadata":{"labels":{"app":"force-scale-up"}},"spec":{"containers":[{"command":["sleep","3600"],"image":"busybox","name":"resource-consumer","resources":{"limits":{"cpu":"2000m","memory":"3Gi"},"requests":{"cpu":"1500m","memory":"2Gi"}}}],"nodeSelector":{"kubernetes.io/arch":"amd64"}}}}}
    creationTimestamp: "2025-07-08T09:37:04Z"
    generation: 1
    name: force-scale-up
    namespace: default
    resourceVersion: "22040094"
    uid: ea8b7b08-47ba-4414-a884-7a4bf0c6f3e0
  spec:
    progressDeadlineSeconds: 600
    replicas: 8
    revisionHistoryLimit: 10
    selector:
      matchLabels:
        app: force-scale-up
    strategy:
      rollingUpdate:
        maxSurge: 25%
        maxUnavailable: 25%
      type: RollingUpdate
    template:
      metadata:
        creationTimestamp: null
        labels:
          app: force-scale-up
      spec:
        containers:
        - command:
          - sleep
          - "3600"
          image: busybox
          imagePullPolicy: Always
          name: resource-consumer
          resources:
            limits:
              cpu: "2"
              memory: 3Gi
            requests:
              cpu: 1500m
              memory: 2Gi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
        dnsPolicy: ClusterFirst
        nodeSelector:
          kubernetes.io/arch: amd64
        restartPolicy: Always
        schedulerName: default-scheduler
        securityContext: {}
        terminationGracePeriodSeconds: 30
  status:
    conditions:
    - lastTransitionTime: "2025-07-08T09:37:04Z"
      lastUpdateTime: "2025-07-08T09:37:04Z"
      message: Deployment does not have minimum availability.
      reason: MinimumReplicasUnavailable
      status: "False"
      type: Available
    - lastTransitionTime: "2025-07-08T09:37:04Z"
      lastUpdateTime: "2025-07-08T09:37:04Z"
      message: 'pods "force-scale-up-857d5bd7db-p6kxt" is forbidden: violates PodSecurity
        "restricted:v1.24": allowPrivilegeEscalation != false (container "resource-consumer"
        must set securityContext.allowPrivilegeEscalation=false), unrestricted capabilities
        (container "resource-consumer" must set securityContext.capabilities.drop=["ALL"]),
        runAsNonRoot != true (pod or container "resource-consumer" must set securityContext.runAsNonRoot=true),
        seccompProfile (pod or container "resource-consumer" must set securityContext.seccompProfile.type
        to "RuntimeDefault" or "Localhost")'
      reason: FailedCreate
      status: "True"
      type: ReplicaFailure
    - lastTransitionTime: "2025-07-08T09:47:05Z"
      lastUpdateTime: "2025-07-08T09:47:05Z"
      message: ReplicaSet "force-scale-up-857d5bd7db" has timed out progressing.
      reason: ProgressDeadlineExceeded
      status: "False"
      type: Progressing
    observedGeneration: 1
    unavailableReplicas: 8
- apiVersion: apps/v1
  kind: Deployment
  metadata:
    annotations:
      deployment.kubernetes.io/revision: "1"
      meta.helm.sh/release-name: gatekeeper
      meta.helm.sh/release-namespace: default
    creationTimestamp: "2025-06-11T08:20:48Z"
    generation: 2
    labels:
      app: gatekeeper
      app.kubernetes.io/instance: gatekeeper
      app.kubernetes.io/managed-by: Helm
      app.kubernetes.io/name: gatekeeper
      app.kubernetes.io/version: 3.19.1
      chart: gatekeeper
      control-plane: audit-controller
      gatekeeper.sh/operation: audit
      gatekeeper.sh/system: "yes"
      helm.sh/chart: gatekeeper-3.19.1
      heritage: Helm
      release: gatekeeper
    name: gatekeeper-audit
    namespace: default
    resourceVersion: "24419016"
    uid: a9303766-6834-411a-bd42-6653867ae0ee
  spec:
    progressDeadlineSeconds: 600
    replicas: 0
    revisionHistoryLimit: 10
    selector:
      matchLabels:
        app: gatekeeper
        chart: gatekeeper
        control-plane: audit-controller
        gatekeeper.sh/operation: audit
        gatekeeper.sh/system: "yes"
        heritage: Helm
        release: gatekeeper
    strategy:
      rollingUpdate:
        maxSurge: 25%
        maxUnavailable: 25%
      type: RollingUpdate
    template:
      metadata:
        creationTimestamp: null
        labels:
          app: gatekeeper
          app.kubernetes.io/instance: gatekeeper
          app.kubernetes.io/managed-by: Helm
          app.kubernetes.io/name: gatekeeper
          app.kubernetes.io/version: 3.19.1
          chart: gatekeeper
          control-plane: audit-controller
          gatekeeper.sh/operation: audit
          gatekeeper.sh/system: "yes"
          helm.sh/chart: gatekeeper-3.19.1
          heritage: Helm
          release: gatekeeper
      spec:
        affinity: {}
        automountServiceAccountToken: true
        containers:
        - args:
          - --audit-interval=60
          - --log-level=INFO
          - --constraint-violations-limit=20
          - --validating-webhook-configuration-name=gatekeeper-validating-webhook-configuration
          - --mutating-webhook-configuration-name=gatekeeper-mutating-webhook-configuration
          - --audit-from-cache=false
          - --audit-chunk-size=500
          - --audit-match-kind-only=false
          - --audit-events-involved-namespace=false
          - --operation=audit
          - --operation=status
          - --operation=generate
          - --operation=mutation-status
          - --logtostderr
          - --health-addr=:9090
          - --prometheus-port=8888
          - --enable-external-data=true
          - --enable-generator-resource-expansion=true
          - --metrics-backend=prometheus
          - --disable-cert-rotation=false
          - --external-data-provider-response-cache-ttl=3m
          - --enable-k8s-native-validation=true
          command:
          - /manager
          env:
          - name: POD_NAMESPACE
            valueFrom:
              fieldRef:
                apiVersion: v1
                fieldPath: metadata.namespace
          - name: POD_NAME
            valueFrom:
              fieldRef:
                apiVersion: v1
                fieldPath: metadata.name
          - name: NAMESPACE
            valueFrom:
              fieldRef:
                apiVersion: v1
                fieldPath: metadata.namespace
          - name: CONTAINER_NAME
            value: manager
          - name: OTEL_RESOURCE_ATTRIBUTES
            value: k8s.pod.name=$(POD_NAME),k8s.namespace.name=$(NAMESPACE),k8s.container.name=$(CONTAINER_NAME)
          image: openpolicyagent/gatekeeper:v3.19.1
          imagePullPolicy: IfNotPresent
          livenessProbe:
            failureThreshold: 3
            httpGet:
              path: /healthz
              port: 9090
              scheme: HTTP
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 1
          name: manager
          ports:
          - containerPort: 8888
            name: metrics
            protocol: TCP
          - containerPort: 9090
            name: healthz
            protocol: TCP
          readinessProbe:
            failureThreshold: 3
            httpGet:
              path: /readyz
              port: 9090
              scheme: HTTP
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 1
          resources:
            limits:
              memory: 512Mi
            requests:
              cpu: 100m
              memory: 512Mi
          securityContext:
            allowPrivilegeEscalation: false
            capabilities:
              drop:
              - ALL
            readOnlyRootFilesystem: true
            runAsGroup: 999
            runAsNonRoot: true
            runAsUser: 1000
            seccompProfile:
              type: RuntimeDefault
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /certs
            name: cert
            readOnly: true
          - mountPath: /tmp/audit
            name: tmp-volume
        dnsPolicy: ClusterFirst
        nodeSelector:
          kubernetes.io/os: linux
        priorityClassName: system-cluster-critical
        restartPolicy: Always
        schedulerName: default-scheduler
        securityContext:
          fsGroup: 999
          supplementalGroups:
          - 999
        serviceAccount: gatekeeper-admin
        serviceAccountName: gatekeeper-admin
        terminationGracePeriodSeconds: 60
        volumes:
        - name: cert
          secret:
            defaultMode: 420
            secretName: gatekeeper-webhook-server-cert
        - emptyDir: {}
          name: tmp-volume
  status:
    conditions:
    - lastTransitionTime: "2025-06-11T08:20:48Z"
      lastUpdateTime: "2025-06-11T08:20:54Z"
      message: ReplicaSet "gatekeeper-audit-58fdd95bc6" has successfully progressed.
      reason: NewReplicaSetAvailable
      status: "True"
      type: Progressing
    - lastTransitionTime: "2025-07-09T13:29:09Z"
      lastUpdateTime: "2025-07-09T13:29:09Z"
      message: Deployment has minimum availability.
      reason: MinimumReplicasAvailable
      status: "True"
      type: Available
    observedGeneration: 2
- apiVersion: apps/v1
  kind: Deployment
  metadata:
    annotations:
      deployment.kubernetes.io/revision: "1"
      meta.helm.sh/release-name: gatekeeper
      meta.helm.sh/release-namespace: default
    creationTimestamp: "2025-06-11T08:20:48Z"
    generation: 2
    labels:
      app: gatekeeper
      app.kubernetes.io/instance: gatekeeper
      app.kubernetes.io/managed-by: Helm
      app.kubernetes.io/name: gatekeeper
      app.kubernetes.io/version: 3.19.1
      chart: gatekeeper
      control-plane: controller-manager
      gatekeeper.sh/operation: webhook
      gatekeeper.sh/system: "yes"
      helm.sh/chart: gatekeeper-3.19.1
      heritage: Helm
      release: gatekeeper
    name: gatekeeper-controller-manager
    namespace: default
    resourceVersion: "24386614"
    uid: 9089ec24-8248-47e1-9d83-eab21b57bb1a
  spec:
    progressDeadlineSeconds: 600
    replicas: 1
    revisionHistoryLimit: 10
    selector:
      matchLabels:
        app: gatekeeper
        chart: gatekeeper
        control-plane: controller-manager
        gatekeeper.sh/operation: webhook
        gatekeeper.sh/system: "yes"
        heritage: Helm
        release: gatekeeper
    strategy:
      rollingUpdate:
        maxSurge: 25%
        maxUnavailable: 25%
      type: RollingUpdate
    template:
      metadata:
        creationTimestamp: null
        labels:
          app: gatekeeper
          app.kubernetes.io/instance: gatekeeper
          app.kubernetes.io/managed-by: Helm
          app.kubernetes.io/name: gatekeeper
          app.kubernetes.io/version: 3.19.1
          chart: gatekeeper
          control-plane: controller-manager
          gatekeeper.sh/operation: webhook
          gatekeeper.sh/system: "yes"
          helm.sh/chart: gatekeeper-3.19.1
          heritage: Helm
          release: gatekeeper
      spec:
        affinity:
          podAntiAffinity:
            preferredDuringSchedulingIgnoredDuringExecution:
            - podAffinityTerm:
                labelSelector:
                  matchExpressions:
                  - key: gatekeeper.sh/operation
                    operator: In
                    values:
                    - webhook
                topologyKey: kubernetes.io/hostname
              weight: 100
        automountServiceAccountToken: true
        containers:
        - args:
          - --port=8443
          - --health-addr=:9090
          - --prometheus-port=8888
          - --logtostderr
          - --log-denies=false
          - --admission-events-involved-namespace=false
          - --log-level=INFO
          - --exempt-namespace=default
          - --operation=webhook
          - --enable-external-data=true
          - --enable-generator-resource-expansion=true
          - --log-mutations=false
          - --mutation-annotations=false
          - --disable-cert-rotation=false
          - --max-serving-threads=-1
          - --tls-min-version=1.3
          - --validating-webhook-configuration-name=gatekeeper-validating-webhook-configuration
          - --mutating-webhook-configuration-name=gatekeeper-mutating-webhook-configuration
          - --external-data-provider-response-cache-ttl=3m
          - --enable-k8s-native-validation=true
          - --metrics-backend=prometheus
          - --operation=mutation-webhook
          - --disable-opa-builtin={http.send}
          command:
          - /manager
          env:
          - name: POD_NAMESPACE
            valueFrom:
              fieldRef:
                apiVersion: v1
                fieldPath: metadata.namespace
          - name: POD_NAME
            valueFrom:
              fieldRef:
                apiVersion: v1
                fieldPath: metadata.name
          - name: NAMESPACE
            valueFrom:
              fieldRef:
                apiVersion: v1
                fieldPath: metadata.namespace
          - name: CONTAINER_NAME
            value: manager
          - name: OTEL_RESOURCE_ATTRIBUTES
            value: k8s.pod.name=$(POD_NAME),k8s.namespace.name=$(NAMESPACE),k8s.container.name=$(CONTAINER_NAME)
          image: openpolicyagent/gatekeeper:v3.19.1
          imagePullPolicy: IfNotPresent
          livenessProbe:
            failureThreshold: 3
            httpGet:
              path: /healthz
              port: 9090
              scheme: HTTP
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 1
          name: manager
          ports:
          - containerPort: 8443
            name: webhook-server
            protocol: TCP
          - containerPort: 8888
            name: metrics
            protocol: TCP
          - containerPort: 9090
            name: healthz
            protocol: TCP
          readinessProbe:
            failureThreshold: 3
            httpGet:
              path: /readyz
              port: 9090
              scheme: HTTP
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 1
          resources:
            limits:
              memory: 512Mi
            requests:
              cpu: 100m
              memory: 512Mi
          securityContext:
            allowPrivilegeEscalation: false
            capabilities:
              drop:
              - ALL
            readOnlyRootFilesystem: true
            runAsGroup: 999
            runAsNonRoot: true
            runAsUser: 1000
            seccompProfile:
              type: RuntimeDefault
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /certs
            name: cert
            readOnly: true
        dnsPolicy: ClusterFirst
        nodeSelector:
          kubernetes.io/os: linux
        priorityClassName: system-cluster-critical
        restartPolicy: Always
        schedulerName: default-scheduler
        securityContext:
          fsGroup: 999
          supplementalGroups:
          - 999
        serviceAccount: gatekeeper-admin
        serviceAccountName: gatekeeper-admin
        terminationGracePeriodSeconds: 60
        volumes:
        - name: cert
          secret:
            defaultMode: 420
            secretName: gatekeeper-webhook-server-cert
  status:
    availableReplicas: 1
    conditions:
    - lastTransitionTime: "2025-06-11T08:20:48Z"
      lastUpdateTime: "2025-06-11T08:20:56Z"
      message: ReplicaSet "gatekeeper-controller-manager-788ddc8977" has successfully
        progressed.
      reason: NewReplicaSetAvailable
      status: "True"
      type: Progressing
    - lastTransitionTime: "2025-07-09T15:36:34Z"
      lastUpdateTime: "2025-07-09T15:36:34Z"
      message: Deployment has minimum availability.
      reason: MinimumReplicasAvailable
      status: "True"
      type: Available
    observedGeneration: 2
    readyReplicas: 1
    replicas: 1
    updatedReplicas: 1
- apiVersion: apps/v1
  kind: Deployment
  metadata:
    annotations:
      deployment.kubernetes.io/revision: "1"
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"apps/v1","kind":"Deployment","metadata":{"annotations":{},"name":"resource-pressure-trigger","namespace":"default"},"spec":{"replicas":3,"selector":{"matchLabels":{"app":"resource-pressure-trigger"}},"template":{"metadata":{"labels":{"app":"resource-pressure-trigger"}},"spec":{"containers":[{"command":["sleep","300"],"image":"busybox","name":"resource-consumer","resources":{"limits":{"cpu":"1000m","memory":"1Gi"},"requests":{"cpu":"500m","memory":"512Mi"}}}]}}}}
    creationTimestamp: "2025-07-08T08:38:42Z"
    generation: 1
    name: resource-pressure-trigger
    namespace: default
    resourceVersion: "22018723"
    uid: 8e23234e-e70b-450c-bceb-031f65728bef
  spec:
    progressDeadlineSeconds: 600
    replicas: 3
    revisionHistoryLimit: 10
    selector:
      matchLabels:
        app: resource-pressure-trigger
    strategy:
      rollingUpdate:
        maxSurge: 25%
        maxUnavailable: 25%
      type: RollingUpdate
    template:
      metadata:
        creationTimestamp: null
        labels:
          app: resource-pressure-trigger
      spec:
        containers:
        - command:
          - sleep
          - "300"
          image: busybox
          imagePullPolicy: Always
          name: resource-consumer
          resources:
            limits:
              cpu: "1"
              memory: 1Gi
            requests:
              cpu: 500m
              memory: 512Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
        dnsPolicy: ClusterFirst
        restartPolicy: Always
        schedulerName: default-scheduler
        securityContext: {}
        terminationGracePeriodSeconds: 30
  status:
    conditions:
    - lastTransitionTime: "2025-07-08T08:38:42Z"
      lastUpdateTime: "2025-07-08T08:38:42Z"
      message: Deployment does not have minimum availability.
      reason: MinimumReplicasUnavailable
      status: "False"
      type: Available
    - lastTransitionTime: "2025-07-08T08:38:42Z"
      lastUpdateTime: "2025-07-08T08:38:42Z"
      message: 'pods "resource-pressure-trigger-5996fbd744-f8gcl" is forbidden: violates
        PodSecurity "restricted:v1.24": allowPrivilegeEscalation != false (container
        "resource-consumer" must set securityContext.allowPrivilegeEscalation=false),
        unrestricted capabilities (container "resource-consumer" must set securityContext.capabilities.drop=["ALL"]),
        runAsNonRoot != true (pod or container "resource-consumer" must set securityContext.runAsNonRoot=true),
        seccompProfile (pod or container "resource-consumer" must set securityContext.seccompProfile.type
        to "RuntimeDefault" or "Localhost")'
      reason: FailedCreate
      status: "True"
      type: ReplicaFailure
    - lastTransitionTime: "2025-07-08T08:48:43Z"
      lastUpdateTime: "2025-07-08T08:48:43Z"
      message: ReplicaSet "resource-pressure-trigger-5996fbd744" has timed out progressing.
      reason: ProgressDeadlineExceeded
      status: "False"
      type: Progressing
    observedGeneration: 1
    unavailableReplicas: 3
- apiVersion: apps/v1
  kind: Deployment
  metadata:
    annotations:
      deployment.kubernetes.io/revision: "1"
    creationTimestamp: "2025-05-19T11:57:30Z"
    generation: 1
    labels:
      app: istio-egressgateway
      install.operator.istio.io/owning-resource: installed-state
      install.operator.istio.io/owning-resource-namespace: istio-system
      istio: egressgateway
      istio.io/rev: default
      operator.istio.io/component: EgressGateways
      operator.istio.io/managed: Reconcile
      operator.istio.io/version: 1.20.0
      release: istio
    name: istio-egressgateway
    namespace: istio-system
    resourceVersion: "22849551"
    uid: 4cd197ea-81c7-4832-87af-39bc6cbb09e2
  spec:
    progressDeadlineSeconds: 600
    replicas: 1
    revisionHistoryLimit: 10
    selector:
      matchLabels:
        app: istio-egressgateway
        istio: egressgateway
    strategy:
      rollingUpdate:
        maxSurge: 100%
        maxUnavailable: 25%
      type: RollingUpdate
    template:
      metadata:
        annotations:
          istio.io/rev: default
          prometheus.io/path: /stats/prometheus
          prometheus.io/port: "15020"
          prometheus.io/scrape: "true"
          sidecar.istio.io/inject: "false"
        creationTimestamp: null
        labels:
          app: istio-egressgateway
          chart: gateways
          heritage: Tiller
          install.operator.istio.io/owning-resource: unknown
          istio: egressgateway
          istio.io/rev: default
          operator.istio.io/component: EgressGateways
          release: istio
          service.istio.io/canonical-name: istio-egressgateway
          service.istio.io/canonical-revision: latest
          sidecar.istio.io/inject: "false"
      spec:
        affinity:
          nodeAffinity: {}
        containers:
        - args:
          - proxy
          - router
          - --domain
          - $(POD_NAMESPACE).svc.cluster.local
          - --proxyLogLevel=warning
          - --proxyComponentLogLevel=misc:error
          - --log_output_level=default:info
          env:
          - name: JWT_POLICY
            value: third-party-jwt
          - name: PILOT_CERT_PROVIDER
            value: istiod
          - name: CA_ADDR
            value: istiod.istio-system.svc:15012
          - name: NODE_NAME
            valueFrom:
              fieldRef:
                apiVersion: v1
                fieldPath: spec.nodeName
          - name: POD_NAME
            valueFrom:
              fieldRef:
                apiVersion: v1
                fieldPath: metadata.name
          - name: POD_NAMESPACE
            valueFrom:
              fieldRef:
                apiVersion: v1
                fieldPath: metadata.namespace
          - name: INSTANCE_IP
            valueFrom:
              fieldRef:
                apiVersion: v1
                fieldPath: status.podIP
          - name: HOST_IP
            valueFrom:
              fieldRef:
                apiVersion: v1
                fieldPath: status.hostIP
          - name: ISTIO_CPU_LIMIT
            valueFrom:
              resourceFieldRef:
                divisor: "0"
                resource: limits.cpu
          - name: SERVICE_ACCOUNT
            valueFrom:
              fieldRef:
                apiVersion: v1
                fieldPath: spec.serviceAccountName
          - name: ISTIO_META_WORKLOAD_NAME
            value: istio-egressgateway
          - name: ISTIO_META_OWNER
            value: kubernetes://apis/apps/v1/namespaces/istio-system/deployments/istio-egressgateway
          - name: ISTIO_META_MESH_ID
            value: cluster.local
          - name: TRUST_DOMAIN
            value: cluster.local
          - name: ISTIO_META_UNPRIVILEGED_POD
            value: "true"
          - name: ISTIO_META_CLUSTER_ID
            value: Kubernetes
          - name: ISTIO_META_NODE_NAME
            valueFrom:
              fieldRef:
                apiVersion: v1
                fieldPath: spec.nodeName
          image: docker.io/istio/proxyv2:1.20.0
          imagePullPolicy: IfNotPresent
          name: istio-proxy
          ports:
          - containerPort: 8080
            protocol: TCP
          - containerPort: 8443
            protocol: TCP
          - containerPort: 15090
            name: http-envoy-prom
            protocol: TCP
          readinessProbe:
            failureThreshold: 30
            httpGet:
              path: /healthz/ready
              port: 15021
              scheme: HTTP
            initialDelaySeconds: 1
            periodSeconds: 2
            successThreshold: 1
            timeoutSeconds: 1
          resources:
            limits:
              cpu: "2"
              memory: 1Gi
            requests:
              cpu: 10m
              memory: 40Mi
          securityContext:
            allowPrivilegeEscalation: false
            capabilities:
              drop:
              - ALL
            privileged: false
            readOnlyRootFilesystem: true
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /var/run/secrets/workload-spiffe-uds
            name: workload-socket
          - mountPath: /var/run/secrets/credential-uds
            name: credential-socket
          - mountPath: /var/run/secrets/workload-spiffe-credentials
            name: workload-certs
          - mountPath: /etc/istio/proxy
            name: istio-envoy
          - mountPath: /etc/istio/config
            name: config-volume
          - mountPath: /var/run/secrets/istio
            name: istiod-ca-cert
          - mountPath: /var/run/secrets/tokens
            name: istio-token
            readOnly: true
          - mountPath: /var/lib/istio/data
            name: istio-data
          - mountPath: /etc/istio/pod
            name: podinfo
          - mountPath: /etc/istio/egressgateway-certs
            name: egressgateway-certs
            readOnly: true
          - mountPath: /etc/istio/egressgateway-ca-certs
            name: egressgateway-ca-certs
            readOnly: true
        dnsPolicy: ClusterFirst
        restartPolicy: Always
        schedulerName: default-scheduler
        securityContext:
          runAsGroup: 1337
          runAsNonRoot: true
          runAsUser: 1337
        serviceAccount: istio-egressgateway-service-account
        serviceAccountName: istio-egressgateway-service-account
        terminationGracePeriodSeconds: 30
        volumes:
        - emptyDir: {}
          name: workload-socket
        - emptyDir: {}
          name: credential-socket
        - emptyDir: {}
          name: workload-certs
        - configMap:
            defaultMode: 420
            name: istio-ca-root-cert
          name: istiod-ca-cert
        - downwardAPI:
            defaultMode: 420
            items:
            - fieldRef:
                apiVersion: v1
                fieldPath: metadata.labels
              path: labels
            - fieldRef:
                apiVersion: v1
                fieldPath: metadata.annotations
              path: annotations
          name: podinfo
        - emptyDir: {}
          name: istio-envoy
        - emptyDir: {}
          name: istio-data
        - name: istio-token
          projected:
            defaultMode: 420
            sources:
            - serviceAccountToken:
                audience: istio-ca
                expirationSeconds: 43200
                path: istio-token
        - configMap:
            defaultMode: 420
            name: istio
            optional: true
          name: config-volume
        - name: egressgateway-certs
          secret:
            defaultMode: 420
            optional: true
            secretName: istio-egressgateway-certs
        - name: egressgateway-ca-certs
          secret:
            defaultMode: 420
            optional: true
            secretName: istio-egressgateway-ca-certs
  status:
    availableReplicas: 1
    conditions:
    - lastTransitionTime: "2025-05-19T11:57:30Z"
      lastUpdateTime: "2025-05-19T11:57:37Z"
      message: ReplicaSet "istio-egressgateway-867df65cc5" has successfully progressed.
      reason: NewReplicaSetAvailable
      status: "True"
      type: Progressing
    - lastTransitionTime: "2025-07-09T16:02:45Z"
      lastUpdateTime: "2025-07-09T16:02:45Z"
      message: Deployment has minimum availability.
      reason: MinimumReplicasAvailable
      status: "True"
      type: Available
    observedGeneration: 1
    readyReplicas: 1
    replicas: 1
    updatedReplicas: 1
- apiVersion: apps/v1
  kind: Deployment
  metadata:
    annotations:
      deployment.kubernetes.io/revision: "1"
    creationTimestamp: "2025-05-19T11:57:30Z"
    generation: 1
    labels:
      app: istio-ingressgateway
      install.operator.istio.io/owning-resource: installed-state
      install.operator.istio.io/owning-resource-namespace: istio-system
      istio: ingressgateway
      istio.io/rev: default
      operator.istio.io/component: IngressGateways
      operator.istio.io/managed: Reconcile
      operator.istio.io/version: 1.20.0
      release: istio
    name: istio-ingressgateway
    namespace: istio-system
    resourceVersion: "24276189"
    uid: 8b870048-3ea4-452d-bc1d-2497404a147c
  spec:
    progressDeadlineSeconds: 600
    replicas: 1
    revisionHistoryLimit: 10
    selector:
      matchLabels:
        app: istio-ingressgateway
        istio: ingressgateway
    strategy:
      rollingUpdate:
        maxSurge: 100%
        maxUnavailable: 25%
      type: RollingUpdate
    template:
      metadata:
        annotations:
          istio.io/rev: default
          prometheus.io/path: /stats/prometheus
          prometheus.io/port: "15020"
          prometheus.io/scrape: "true"
          sidecar.istio.io/inject: "false"
        creationTimestamp: null
        labels:
          app: istio-ingressgateway
          chart: gateways
          heritage: Tiller
          install.operator.istio.io/owning-resource: unknown
          istio: ingressgateway
          istio.io/rev: default
          operator.istio.io/component: IngressGateways
          release: istio
          service.istio.io/canonical-name: istio-ingressgateway
          service.istio.io/canonical-revision: latest
          sidecar.istio.io/inject: "false"
      spec:
        affinity:
          nodeAffinity: {}
        containers:
        - args:
          - proxy
          - router
          - --domain
          - $(POD_NAMESPACE).svc.cluster.local
          - --proxyLogLevel=warning
          - --proxyComponentLogLevel=misc:error
          - --log_output_level=default:info
          env:
          - name: JWT_POLICY
            value: third-party-jwt
          - name: PILOT_CERT_PROVIDER
            value: istiod
          - name: CA_ADDR
            value: istiod.istio-system.svc:15012
          - name: NODE_NAME
            valueFrom:
              fieldRef:
                apiVersion: v1
                fieldPath: spec.nodeName
          - name: POD_NAME
            valueFrom:
              fieldRef:
                apiVersion: v1
                fieldPath: metadata.name
          - name: POD_NAMESPACE
            valueFrom:
              fieldRef:
                apiVersion: v1
                fieldPath: metadata.namespace
          - name: INSTANCE_IP
            valueFrom:
              fieldRef:
                apiVersion: v1
                fieldPath: status.podIP
          - name: HOST_IP
            valueFrom:
              fieldRef:
                apiVersion: v1
                fieldPath: status.hostIP
          - name: ISTIO_CPU_LIMIT
            valueFrom:
              resourceFieldRef:
                divisor: "0"
                resource: limits.cpu
          - name: SERVICE_ACCOUNT
            valueFrom:
              fieldRef:
                apiVersion: v1
                fieldPath: spec.serviceAccountName
          - name: ISTIO_META_WORKLOAD_NAME
            value: istio-ingressgateway
          - name: ISTIO_META_OWNER
            value: kubernetes://apis/apps/v1/namespaces/istio-system/deployments/istio-ingressgateway
          - name: ISTIO_META_MESH_ID
            value: cluster.local
          - name: TRUST_DOMAIN
            value: cluster.local
          - name: ISTIO_META_UNPRIVILEGED_POD
            value: "true"
          - name: ISTIO_META_CLUSTER_ID
            value: Kubernetes
          - name: ISTIO_META_NODE_NAME
            valueFrom:
              fieldRef:
                apiVersion: v1
                fieldPath: spec.nodeName
          image: docker.io/istio/proxyv2:1.20.0
          imagePullPolicy: IfNotPresent
          name: istio-proxy
          ports:
          - containerPort: 15021
            protocol: TCP
          - containerPort: 8080
            protocol: TCP
          - containerPort: 8443
            protocol: TCP
          - containerPort: 31400
            protocol: TCP
          - containerPort: 15443
            protocol: TCP
          - containerPort: 15090
            name: http-envoy-prom
            protocol: TCP
          readinessProbe:
            failureThreshold: 30
            httpGet:
              path: /healthz/ready
              port: 15021
              scheme: HTTP
            initialDelaySeconds: 1
            periodSeconds: 2
            successThreshold: 1
            timeoutSeconds: 1
          resources:
            limits:
              cpu: "2"
              memory: 1Gi
            requests:
              cpu: 10m
              memory: 40Mi
          securityContext:
            allowPrivilegeEscalation: false
            capabilities:
              drop:
              - ALL
            privileged: false
            readOnlyRootFilesystem: true
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /var/run/secrets/workload-spiffe-uds
            name: workload-socket
          - mountPath: /var/run/secrets/credential-uds
            name: credential-socket
          - mountPath: /var/run/secrets/workload-spiffe-credentials
            name: workload-certs
          - mountPath: /etc/istio/proxy
            name: istio-envoy
          - mountPath: /etc/istio/config
            name: config-volume
          - mountPath: /var/run/secrets/istio
            name: istiod-ca-cert
          - mountPath: /var/run/secrets/tokens
            name: istio-token
            readOnly: true
          - mountPath: /var/lib/istio/data
            name: istio-data
          - mountPath: /etc/istio/pod
            name: podinfo
          - mountPath: /etc/istio/ingressgateway-certs
            name: ingressgateway-certs
            readOnly: true
          - mountPath: /etc/istio/ingressgateway-ca-certs
            name: ingressgateway-ca-certs
            readOnly: true
        dnsPolicy: ClusterFirst
        restartPolicy: Always
        schedulerName: default-scheduler
        securityContext:
          runAsGroup: 1337
          runAsNonRoot: true
          runAsUser: 1337
        serviceAccount: istio-ingressgateway-service-account
        serviceAccountName: istio-ingressgateway-service-account
        terminationGracePeriodSeconds: 30
        volumes:
        - emptyDir: {}
          name: workload-socket
        - emptyDir: {}
          name: credential-socket
        - emptyDir: {}
          name: workload-certs
        - configMap:
            defaultMode: 420
            name: istio-ca-root-cert
          name: istiod-ca-cert
        - downwardAPI:
            defaultMode: 420
            items:
            - fieldRef:
                apiVersion: v1
                fieldPath: metadata.labels
              path: labels
            - fieldRef:
                apiVersion: v1
                fieldPath: metadata.annotations
              path: annotations
          name: podinfo
        - emptyDir: {}
          name: istio-envoy
        - emptyDir: {}
          name: istio-data
        - name: istio-token
          projected:
            defaultMode: 420
            sources:
            - serviceAccountToken:
                audience: istio-ca
                expirationSeconds: 43200
                path: istio-token
        - configMap:
            defaultMode: 420
            name: istio
            optional: true
          name: config-volume
        - name: ingressgateway-certs
          secret:
            defaultMode: 420
            optional: true
            secretName: istio-ingressgateway-certs
        - name: ingressgateway-ca-certs
          secret:
            defaultMode: 420
            optional: true
            secretName: istio-ingressgateway-ca-certs
  status:
    availableReplicas: 1
    conditions:
    - lastTransitionTime: "2025-05-19T11:57:30Z"
      lastUpdateTime: "2025-05-19T11:57:37Z"
      message: ReplicaSet "istio-ingressgateway-5ccc8677f7" has successfully progressed.
      reason: NewReplicaSetAvailable
      status: "True"
      type: Progressing
    - lastTransitionTime: "2025-07-11T15:36:45Z"
      lastUpdateTime: "2025-07-11T15:36:45Z"
      message: Deployment has minimum availability.
      reason: MinimumReplicasAvailable
      status: "True"
      type: Available
    observedGeneration: 1
    readyReplicas: 1
    replicas: 1
    updatedReplicas: 1
- apiVersion: apps/v1
  kind: Deployment
  metadata:
    annotations:
      deployment.kubernetes.io/revision: "1"
    creationTimestamp: "2025-05-19T11:57:17Z"
    generation: 1
    labels:
      app: istiod
      install.operator.istio.io/owning-resource: installed-state
      install.operator.istio.io/owning-resource-namespace: istio-system
      istio: pilot
      istio.io/rev: default
      operator.istio.io/component: Pilot
      operator.istio.io/managed: Reconcile
      operator.istio.io/version: 1.20.0
      release: istio
    name: istiod
    namespace: istio-system
    resourceVersion: "24276050"
    uid: 67baa68f-4568-4a94-b310-aad79276eebb
  spec:
    progressDeadlineSeconds: 600
    replicas: 1
    revisionHistoryLimit: 10
    selector:
      matchLabels:
        istio: pilot
    strategy:
      rollingUpdate:
        maxSurge: 100%
        maxUnavailable: 25%
      type: RollingUpdate
    template:
      metadata:
        annotations:
          ambient.istio.io/redirection: disabled
          prometheus.io/port: "15014"
          prometheus.io/scrape: "true"
          sidecar.istio.io/inject: "false"
        creationTimestamp: null
        labels:
          app: istiod
          install.operator.istio.io/owning-resource: unknown
          istio: pilot
          istio.io/rev: default
          operator.istio.io/component: Pilot
          sidecar.istio.io/inject: "false"
      spec:
        containers:
        - args:
          - discovery
          - --monitoringAddr=:15014
          - --log_output_level=default:info
          - --domain
          - cluster.local
          - --keepaliveMaxServerConnectionAge
          - 30m
          env:
          - name: REVISION
            value: default
          - name: JWT_POLICY
            value: third-party-jwt
          - name: PILOT_CERT_PROVIDER
            value: istiod
          - name: POD_NAME
            valueFrom:
              fieldRef:
                apiVersion: v1
                fieldPath: metadata.name
          - name: POD_NAMESPACE
            valueFrom:
              fieldRef:
                apiVersion: v1
                fieldPath: metadata.namespace
          - name: SERVICE_ACCOUNT
            valueFrom:
              fieldRef:
                apiVersion: v1
                fieldPath: spec.serviceAccountName
          - name: KUBECONFIG
            value: /var/run/secrets/remote/config
          - name: PILOT_TRACE_SAMPLING
            value: "100"
          - name: PILOT_ENABLE_ANALYSIS
            value: "false"
          - name: CLUSTER_ID
            value: Kubernetes
          - name: GOMEMLIMIT
            valueFrom:
              resourceFieldRef:
                divisor: "0"
                resource: limits.memory
          - name: GOMAXPROCS
            valueFrom:
              resourceFieldRef:
                divisor: "0"
                resource: limits.cpu
          - name: PLATFORM
          image: docker.io/istio/pilot:1.20.0
          imagePullPolicy: IfNotPresent
          name: discovery
          ports:
          - containerPort: 8080
            protocol: TCP
          - containerPort: 15010
            protocol: TCP
          - containerPort: 15017
            protocol: TCP
          readinessProbe:
            failureThreshold: 3
            httpGet:
              path: /ready
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 1
            periodSeconds: 3
            successThreshold: 1
            timeoutSeconds: 5
          resources:
            requests:
              cpu: 10m
              memory: 100Mi
          securityContext:
            allowPrivilegeEscalation: false
            capabilities:
              drop:
              - ALL
            readOnlyRootFilesystem: true
            runAsNonRoot: true
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /var/run/secrets/tokens
            name: istio-token
            readOnly: true
          - mountPath: /var/run/secrets/istio-dns
            name: local-certs
          - mountPath: /etc/cacerts
            name: cacerts
            readOnly: true
          - mountPath: /var/run/secrets/remote
            name: istio-kubeconfig
            readOnly: true
          - mountPath: /var/run/secrets/istiod/tls
            name: istio-csr-dns-cert
            readOnly: true
          - mountPath: /var/run/secrets/istiod/ca
            name: istio-csr-ca-configmap
            readOnly: true
        dnsPolicy: ClusterFirst
        restartPolicy: Always
        schedulerName: default-scheduler
        securityContext: {}
        serviceAccount: istiod
        serviceAccountName: istiod
        terminationGracePeriodSeconds: 30
        volumes:
        - emptyDir:
            medium: Memory
          name: local-certs
        - name: istio-token
          projected:
            defaultMode: 420
            sources:
            - serviceAccountToken:
                audience: istio-ca
                expirationSeconds: 43200
                path: istio-token
        - name: cacerts
          secret:
            defaultMode: 420
            optional: true
            secretName: cacerts
        - name: istio-kubeconfig
          secret:
            defaultMode: 420
            optional: true
            secretName: istio-kubeconfig
        - name: istio-csr-dns-cert
          secret:
            defaultMode: 420
            optional: true
            secretName: istiod-tls
        - configMap:
            defaultMode: 420
            name: istio-ca-root-cert
            optional: true
          name: istio-csr-ca-configmap
  status:
    availableReplicas: 1
    conditions:
    - lastTransitionTime: "2025-05-19T11:57:18Z"
      lastUpdateTime: "2025-05-19T11:57:28Z"
      message: ReplicaSet "istiod-86db48bc64" has successfully progressed.
      reason: NewReplicaSetAvailable
      status: "True"
      type: Progressing
    - lastTransitionTime: "2025-07-11T15:36:29Z"
      lastUpdateTime: "2025-07-11T15:36:29Z"
      message: Deployment has minimum availability.
      reason: MinimumReplicasAvailable
      status: "True"
      type: Available
    observedGeneration: 1
    readyReplicas: 1
    replicas: 1
    updatedReplicas: 1
- apiVersion: apps/v1
  kind: Deployment
  metadata:
    annotations:
      deployment.kubernetes.io/revision: "1"
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"apps/v1","kind":"Deployment","metadata":{"annotations":{},"labels":{"app":"kiali","app.kubernetes.io/instance":"kiali","app.kubernetes.io/managed-by":"Helm","app.kubernetes.io/name":"kiali","app.kubernetes.io/part-of":"kiali","app.kubernetes.io/version":"v1.76.0","helm.sh/chart":"kiali-server-1.76.0","version":"v1.76.0"},"name":"kiali","namespace":"istio-system"},"spec":{"replicas":1,"selector":{"matchLabels":{"app.kubernetes.io/instance":"kiali","app.kubernetes.io/name":"kiali"}},"strategy":{"rollingUpdate":{"maxSurge":1,"maxUnavailable":1},"type":"RollingUpdate"},"template":{"metadata":{"annotations":{"checksum/config":"aebd819b94172ef9b148702b7bb438ac35bd1eb284bbb9b13769d8576374fbda","kiali.io/dashboards":"go,kiali","prometheus.io/port":"9090","prometheus.io/scrape":"true"},"labels":{"app":"kiali","app.kubernetes.io/instance":"kiali","app.kubernetes.io/managed-by":"Helm","app.kubernetes.io/name":"kiali","app.kubernetes.io/part-of":"kiali","app.kubernetes.io/version":"v1.76.0","helm.sh/chart":"kiali-server-1.76.0","sidecar.istio.io/inject":"false","version":"v1.76.0"},"name":"kiali"},"spec":{"containers":[{"command":["/opt/kiali/kiali","-config","/kiali-configuration/config.yaml"],"env":[{"name":"ACTIVE_NAMESPACE","valueFrom":{"fieldRef":{"fieldPath":"metadata.namespace"}}},{"name":"LOG_LEVEL","value":"info"},{"name":"LOG_FORMAT","value":"text"},{"name":"LOG_TIME_FIELD_FORMAT","value":"2006-01-02T15:04:05Z07:00"},{"name":"LOG_SAMPLER_RATE","value":"1"}],"image":"quay.io/kiali/kiali:v1.76","imagePullPolicy":"Always","livenessProbe":{"httpGet":{"path":"/kiali/healthz","port":"api-port","scheme":"HTTP"},"initialDelaySeconds":5,"periodSeconds":30},"name":"kiali","ports":[{"containerPort":20001,"name":"api-port"},{"containerPort":9090,"name":"http-metrics"}],"readinessProbe":{"httpGet":{"path":"/kiali/healthz","port":"api-port","scheme":"HTTP"},"initialDelaySeconds":5,"periodSeconds":30},"resources":{"limits":{"memory":"1Gi"},"requests":{"cpu":"10m","memory":"64Mi"}},"securityContext":{"allowPrivilegeEscalation":false,"capabilities":{"drop":["ALL"]},"privileged":false,"readOnlyRootFilesystem":true,"runAsNonRoot":true},"volumeMounts":[{"mountPath":"/kiali-configuration","name":"kiali-configuration"},{"mountPath":"/kiali-cert","name":"kiali-cert"},{"mountPath":"/kiali-secret","name":"kiali-secret"},{"mountPath":"/kiali-cabundle","name":"kiali-cabundle"}]}],"serviceAccountName":"kiali","volumes":[{"configMap":{"name":"kiali"},"name":"kiali-configuration"},{"name":"kiali-cert","secret":{"optional":true,"secretName":"istio.kiali-service-account"}},{"name":"kiali-secret","secret":{"optional":true,"secretName":"kiali"}},{"configMap":{"name":"kiali-cabundle","optional":true},"name":"kiali-cabundle"}]}}}}
    creationTimestamp: "2025-05-19T11:58:27Z"
    generation: 2
    labels:
      app: kiali
      app.kubernetes.io/instance: kiali
      app.kubernetes.io/managed-by: Helm
      app.kubernetes.io/name: kiali
      app.kubernetes.io/part-of: kiali
      app.kubernetes.io/version: v1.76.0
      helm.sh/chart: kiali-server-1.76.0
      version: v1.76.0
    name: kiali
    namespace: istio-system
    resourceVersion: "********"
    uid: 7aa48a19-1e15-4afe-9d73-1c9ccdbb155c
  spec:
    progressDeadlineSeconds: 600
    replicas: 0
    revisionHistoryLimit: 10
    selector:
      matchLabels:
        app.kubernetes.io/instance: kiali
        app.kubernetes.io/name: kiali
    strategy:
      rollingUpdate:
        maxSurge: 1
        maxUnavailable: 1
      type: RollingUpdate
    template:
      metadata:
        annotations:
          checksum/config: aebd819b94172ef9b148702b7bb438ac35bd1eb284bbb9b13769d8576374fbda
          kiali.io/dashboards: go,kiali
          prometheus.io/port: "9090"
          prometheus.io/scrape: "true"
        creationTimestamp: null
        labels:
          app: kiali
          app.kubernetes.io/instance: kiali
          app.kubernetes.io/managed-by: Helm
          app.kubernetes.io/name: kiali
          app.kubernetes.io/part-of: kiali
          app.kubernetes.io/version: v1.76.0
          helm.sh/chart: kiali-server-1.76.0
          sidecar.istio.io/inject: "false"
          version: v1.76.0
        name: kiali
      spec:
        containers:
        - command:
          - /opt/kiali/kiali
          - -config
          - /kiali-configuration/config.yaml
          env:
          - name: ACTIVE_NAMESPACE
            valueFrom:
              fieldRef:
                apiVersion: v1
                fieldPath: metadata.namespace
          - name: LOG_LEVEL
            value: info
          - name: LOG_FORMAT
            value: text
          - name: LOG_TIME_FIELD_FORMAT
            value: 2006-01-02T15:04:05Z07:00
          - name: LOG_SAMPLER_RATE
            value: "1"
          image: quay.io/kiali/kiali:v1.76
          imagePullPolicy: Always
          livenessProbe:
            failureThreshold: 3
            httpGet:
              path: /kiali/healthz
              port: api-port
              scheme: HTTP
            initialDelaySeconds: 5
            periodSeconds: 30
            successThreshold: 1
            timeoutSeconds: 1
          name: kiali
          ports:
          - containerPort: 20001
            name: api-port
            protocol: TCP
          - containerPort: 9090
            name: http-metrics
            protocol: TCP
          readinessProbe:
            failureThreshold: 3
            httpGet:
              path: /kiali/healthz
              port: api-port
              scheme: HTTP
            initialDelaySeconds: 5
            periodSeconds: 30
            successThreshold: 1
            timeoutSeconds: 1
          resources:
            limits:
              memory: 1Gi
            requests:
              cpu: 10m
              memory: 64Mi
          securityContext:
            allowPrivilegeEscalation: false
            capabilities:
              drop:
              - ALL
            privileged: false
            readOnlyRootFilesystem: true
            runAsNonRoot: true
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /kiali-configuration
            name: kiali-configuration
          - mountPath: /kiali-cert
            name: kiali-cert
          - mountPath: /kiali-secret
            name: kiali-secret
          - mountPath: /kiali-cabundle
            name: kiali-cabundle
        dnsPolicy: ClusterFirst
        restartPolicy: Always
        schedulerName: default-scheduler
        securityContext: {}
        serviceAccount: kiali
        serviceAccountName: kiali
        terminationGracePeriodSeconds: 30
        volumes:
        - configMap:
            defaultMode: 420
            name: kiali
          name: kiali-configuration
        - name: kiali-cert
          secret:
            defaultMode: 420
            optional: true
            secretName: istio.kiali-service-account
        - name: kiali-secret
          secret:
            defaultMode: 420
            optional: true
            secretName: kiali
        - configMap:
            defaultMode: 420
            name: kiali-cabundle
            optional: true
          name: kiali-cabundle
  status:
    conditions:
    - lastTransitionTime: "2025-05-19T11:58:27Z"
      lastUpdateTime: "2025-05-19T11:58:27Z"
      message: Deployment has minimum availability.
      reason: MinimumReplicasAvailable
      status: "True"
      type: Available
    - lastTransitionTime: "2025-05-19T11:58:27Z"
      lastUpdateTime: "2025-05-19T11:59:06Z"
      message: ReplicaSet "kiali-fc474f545" has successfully progressed.
      reason: NewReplicaSetAvailable
      status: "True"
      type: Progressing
    observedGeneration: 2
- apiVersion: apps/v1
  kind: Deployment
  metadata:
    annotations:
      deployment.kubernetes.io/revision: "1"
      meta.helm.sh/release-name: karpenter
      meta.helm.sh/release-namespace: karpenter
    creationTimestamp: "2025-07-09T14:29:42Z"
    generation: 2
    labels:
      app.kubernetes.io/instance: karpenter
      app.kubernetes.io/managed-by: Helm
      app.kubernetes.io/name: karpenter
      app.kubernetes.io/version: 0.16.3
      helm.sh/chart: karpenter-0.16.3
    name: karpenter
    namespace: karpenter
    resourceVersion: "24386825"
    uid: c910df58-e152-4b9a-965b-298698b4d9ba
  spec:
    progressDeadlineSeconds: 600
    replicas: 1
    revisionHistoryLimit: 10
    selector:
      matchLabels:
        app.kubernetes.io/instance: karpenter
        app.kubernetes.io/name: karpenter
    strategy:
      rollingUpdate:
        maxSurge: 25%
        maxUnavailable: 1
      type: RollingUpdate
    template:
      metadata:
        creationTimestamp: null
        labels:
          app.kubernetes.io/instance: karpenter
          app.kubernetes.io/name: karpenter
      spec:
        affinity:
          nodeAffinity:
            requiredDuringSchedulingIgnoredDuringExecution:
              nodeSelectorTerms:
              - matchExpressions:
                - key: karpenter.sh/provisioner-name
                  operator: DoesNotExist
        containers:
        - env:
          - name: CLUSTER_NAME
            value: production-wks
          - name: CLUSTER_ENDPOINT
            value: https://********************************.gr7.eu-central-1.eks.amazonaws.com
          - name: KARPENTER_SERVICE
            value: karpenter
          - name: SYSTEM_NAMESPACE
            valueFrom:
              fieldRef:
                apiVersion: v1
                fieldPath: metadata.namespace
          - name: AWS_DEFAULT_INSTANCE_PROFILE
            value: production-karpenter-node-profile
          - name: MEMORY_LIMIT
            valueFrom:
              resourceFieldRef:
                containerName: controller
                divisor: "0"
                resource: limits.memory
          image: public.ecr.aws/karpenter/controller:v0.16.3@sha256:68db4f092cf9cc83f5ef9e2fbc5407c2cb682e81f64dfaa700a7602ede38b1cf
          imagePullPolicy: IfNotPresent
          livenessProbe:
            failureThreshold: 3
            httpGet:
              path: /healthz
              port: http
              scheme: HTTP
            initialDelaySeconds: 30
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 30
          name: controller
          ports:
          - containerPort: 8080
            name: http-metrics
            protocol: TCP
          - containerPort: 8081
            name: http
            protocol: TCP
          readinessProbe:
            failureThreshold: 3
            httpGet:
              path: /readyz
              port: http
              scheme: HTTP
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 30
          resources:
            limits:
              cpu: "1"
              memory: 1Gi
            requests:
              cpu: "1"
              memory: 1Gi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
        - args:
          - -port=8443
          env:
          - name: CLUSTER_NAME
            value: production-wks
          - name: KUBERNETES_MIN_VERSION
            value: 1.19.0-0
          - name: CLUSTER_ENDPOINT
            value: https://********************************.gr7.eu-central-1.eks.amazonaws.com
          - name: KARPENTER_SERVICE
            value: karpenter
          - name: SYSTEM_NAMESPACE
            valueFrom:
              fieldRef:
                apiVersion: v1
                fieldPath: metadata.namespace
          - name: MEMORY_LIMIT
            valueFrom:
              resourceFieldRef:
                containerName: webhook
                divisor: "0"
                resource: limits.memory
          - name: AWS_DEFAULT_INSTANCE_PROFILE
            value: production-karpenter-node-profile
          image: public.ecr.aws/karpenter/webhook:v0.16.3@sha256:96a2d9b06d6bc5127801f358f74b1cf2d289b423a2e9ba40c573c0b14b17dafa
          imagePullPolicy: IfNotPresent
          livenessProbe:
            failureThreshold: 3
            httpGet:
              path: /
              port: https-webhook
              scheme: HTTPS
            initialDelaySeconds: 30
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 1
          name: webhook
          ports:
          - containerPort: 8443
            name: https-webhook
            protocol: TCP
          readinessProbe:
            failureThreshold: 3
            httpGet:
              path: /
              port: https-webhook
              scheme: HTTPS
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 1
          resources:
            limits:
              cpu: 200m
              memory: 100Mi
            requests:
              cpu: 200m
              memory: 100Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
        dnsPolicy: Default
        nodeSelector:
          kubernetes.io/os: linux
        priorityClassName: system-cluster-critical
        restartPolicy: Always
        schedulerName: default-scheduler
        securityContext:
          fsGroup: 1000
        serviceAccount: karpenter
        serviceAccountName: karpenter
        terminationGracePeriodSeconds: 30
        tolerations:
        - key: CriticalAddonsOnly
          operator: Exists
        topologySpreadConstraints:
        - labelSelector:
            matchLabels:
              app.kubernetes.io/instance: karpenter
              app.kubernetes.io/name: karpenter
          maxSkew: 1
          topologyKey: topology.kubernetes.io/zone
          whenUnsatisfiable: ScheduleAnyway
  status:
    availableReplicas: 1
    conditions:
    - lastTransitionTime: "2025-07-09T14:30:01Z"
      lastUpdateTime: "2025-07-09T14:30:01Z"
      message: Deployment has minimum availability.
      reason: MinimumReplicasAvailable
      status: "True"
      type: Available
    - lastTransitionTime: "2025-07-09T14:29:42Z"
      lastUpdateTime: "2025-07-09T14:30:01Z"
      message: ReplicaSet "karpenter-796944d6dd" has successfully progressed.
      reason: NewReplicaSetAvailable
      status: "True"
      type: Progressing
    observedGeneration: 2
    readyReplicas: 1
    replicas: 1
    updatedReplicas: 1
- apiVersion: apps/v1
  kind: Deployment
  metadata:
    annotations:
      deployment.kubernetes.io/revision: "1"
      meta.helm.sh/release-name: keda
      meta.helm.sh/release-namespace: keda-system
    creationTimestamp: "2025-05-27T10:12:52Z"
    generation: 1
    labels:
      app: keda-admission-webhooks
      app.kubernetes.io/component: operator
      app.kubernetes.io/instance: keda
      app.kubernetes.io/managed-by: Helm
      app.kubernetes.io/name: keda-admission-webhooks
      app.kubernetes.io/part-of: keda-operator
      app.kubernetes.io/version: 2.17.1
      helm.sh/chart: keda-2.17.1
      name: keda-admission-webhooks
    name: keda-admission-webhooks
    namespace: keda-system
    resourceVersion: "24419043"
    uid: b8654569-87a6-44ae-a6a0-8f09e53d3f2f
  spec:
    progressDeadlineSeconds: 600
    replicas: 1
    revisionHistoryLimit: 10
    selector:
      matchLabels:
        app: keda-admission-webhooks
    strategy:
      rollingUpdate:
        maxSurge: 25%
        maxUnavailable: 25%
      type: RollingUpdate
    template:
      metadata:
        creationTimestamp: null
        labels:
          app: keda-admission-webhooks
          app.kubernetes.io/component: operator
          app.kubernetes.io/instance: keda
          app.kubernetes.io/managed-by: Helm
          app.kubernetes.io/name: keda-admission-webhooks
          app.kubernetes.io/part-of: keda-operator
          app.kubernetes.io/version: 2.17.1
          helm.sh/chart: keda-2.17.1
          name: keda-admission-webhooks
      spec:
        automountServiceAccountToken: true
        containers:
        - args:
          - --zap-log-level=info
          - --zap-encoder=console
          - --zap-time-encoding=rfc3339
          - --cert-dir=/certs
          - --health-probe-bind-address=:8081
          - --metrics-bind-address=:8080
          command:
          - /keda-admission-webhooks
          env:
          - name: WATCH_NAMESPACE
          - name: POD_NAME
            valueFrom:
              fieldRef:
                apiVersion: v1
                fieldPath: metadata.name
          - name: POD_NAMESPACE
            valueFrom:
              fieldRef:
                apiVersion: v1
                fieldPath: metadata.namespace
          image: ghcr.io/kedacore/keda-admission-webhooks:2.17.1
          imagePullPolicy: Always
          livenessProbe:
            failureThreshold: 3
            httpGet:
              path: /healthz
              port: 8081
              scheme: HTTP
            initialDelaySeconds: 25
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 1
          name: keda-admission-webhooks
          ports:
          - containerPort: 9443
            name: http
            protocol: TCP
          readinessProbe:
            failureThreshold: 3
            httpGet:
              path: /readyz
              port: 8081
              scheme: HTTP
            initialDelaySeconds: 20
            periodSeconds: 3
            successThreshold: 1
            timeoutSeconds: 1
          resources:
            limits:
              cpu: "1"
              memory: 1000Mi
            requests:
              cpu: 100m
              memory: 100Mi
          securityContext:
            allowPrivilegeEscalation: false
            capabilities:
              drop:
              - ALL
            readOnlyRootFilesystem: true
            seccompProfile:
              type: RuntimeDefault
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /certs
            name: certificates
            readOnly: true
        dnsPolicy: ClusterFirst
        enableServiceLinks: true
        nodeSelector:
          kubernetes.io/os: linux
        restartPolicy: Always
        schedulerName: default-scheduler
        securityContext:
          runAsNonRoot: true
        serviceAccount: keda-webhook
        serviceAccountName: keda-webhook
        terminationGracePeriodSeconds: 30
        volumes:
        - name: certificates
          secret:
            defaultMode: 420
            secretName: kedaorg-certs
  status:
    availableReplicas: 1
    conditions:
    - lastTransitionTime: "2025-05-27T10:12:53Z"
      lastUpdateTime: "2025-05-27T10:13:27Z"
      message: ReplicaSet "keda-admission-webhooks-8bf696987" has successfully progressed.
      reason: NewReplicaSetAvailable
      status: "True"
      type: Progressing
    - lastTransitionTime: "2025-07-11T19:43:26Z"
      lastUpdateTime: "2025-07-11T19:43:26Z"
      message: Deployment has minimum availability.
      reason: MinimumReplicasAvailable
      status: "True"
      type: Available
    observedGeneration: 1
    readyReplicas: 1
    replicas: 1
    updatedReplicas: 1
- apiVersion: apps/v1
  kind: Deployment
  metadata:
    annotations:
      deployment.kubernetes.io/revision: "1"
      meta.helm.sh/release-name: keda
      meta.helm.sh/release-namespace: keda-system
    creationTimestamp: "2025-05-27T10:12:52Z"
    generation: 1
    labels:
      app: keda-operator
      app.kubernetes.io/component: operator
      app.kubernetes.io/instance: keda
      app.kubernetes.io/managed-by: Helm
      app.kubernetes.io/name: keda-operator
      app.kubernetes.io/part-of: keda-operator
      app.kubernetes.io/version: 2.17.1
      helm.sh/chart: keda-2.17.1
      name: keda-operator
    name: keda-operator
    namespace: keda-system
    resourceVersion: "24419034"
    uid: 9985a7ef-4617-4823-899f-5a1ebc2ab390
  spec:
    progressDeadlineSeconds: 600
    replicas: 1
    revisionHistoryLimit: 10
    selector:
      matchLabels:
        app: keda-operator
    strategy:
      rollingUpdate:
        maxSurge: 25%
        maxUnavailable: 25%
      type: RollingUpdate
    template:
      metadata:
        creationTimestamp: null
        labels:
          app: keda-operator
          app.kubernetes.io/component: operator
          app.kubernetes.io/instance: keda
          app.kubernetes.io/managed-by: Helm
          app.kubernetes.io/name: keda-operator
          app.kubernetes.io/part-of: keda-operator
          app.kubernetes.io/version: 2.17.1
          helm.sh/chart: keda-2.17.1
          name: keda-operator
      spec:
        automountServiceAccountToken: true
        containers:
        - args:
          - --leader-elect
          - --disable-compression=true
          - --zap-log-level=info
          - --zap-encoder=console
          - --zap-time-encoding=rfc3339
          - --enable-webhook-patching=true
          - --cert-dir=/certs
          - --enable-cert-rotation=true
          - --cert-secret-name=kedaorg-certs
          - --operator-service-name=keda-operator
          - --metrics-server-service-name=keda-operator-metrics-apiserver
          - --webhooks-service-name=keda-admission-webhooks
          - --k8s-cluster-name=kubernetes-default
          - --k8s-cluster-domain=cluster.local
          - --enable-prometheus-metrics=false
          command:
          - /keda
          env:
          - name: WATCH_NAMESPACE
          - name: POD_NAME
            valueFrom:
              fieldRef:
                apiVersion: v1
                fieldPath: metadata.name
          - name: POD_NAMESPACE
            valueFrom:
              fieldRef:
                apiVersion: v1
                fieldPath: metadata.namespace
          - name: OPERATOR_NAME
            value: keda-operator
          - name: KEDA_HTTP_DEFAULT_TIMEOUT
            value: "3000"
          - name: KEDA_HTTP_MIN_TLS_VERSION
            value: TLS12
          image: ghcr.io/kedacore/keda:2.17.1
          imagePullPolicy: Always
          livenessProbe:
            failureThreshold: 3
            httpGet:
              path: /healthz
              port: 8081
              scheme: HTTP
            initialDelaySeconds: 25
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 1
          name: keda-operator
          ports:
          - containerPort: 9666
            name: metricsservice
            protocol: TCP
          readinessProbe:
            failureThreshold: 3
            httpGet:
              path: /readyz
              port: 8081
              scheme: HTTP
            initialDelaySeconds: 20
            periodSeconds: 3
            successThreshold: 1
            timeoutSeconds: 1
          resources:
            limits:
              cpu: "1"
              memory: 1000Mi
            requests:
              cpu: 100m
              memory: 100Mi
          securityContext:
            allowPrivilegeEscalation: false
            capabilities:
              drop:
              - ALL
            readOnlyRootFilesystem: true
            seccompProfile:
              type: RuntimeDefault
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /certs
            name: certificates
            readOnly: true
        dnsPolicy: ClusterFirst
        enableServiceLinks: true
        nodeSelector:
          kubernetes.io/os: linux
        restartPolicy: Always
        schedulerName: default-scheduler
        securityContext:
          runAsNonRoot: true
        serviceAccount: keda-operator
        serviceAccountName: keda-operator
        terminationGracePeriodSeconds: 30
        volumes:
        - name: certificates
          secret:
            defaultMode: 420
            optional: true
            secretName: kedaorg-certs
  status:
    availableReplicas: 1
    conditions:
    - lastTransitionTime: "2025-05-27T10:12:53Z"
      lastUpdateTime: "2025-05-27T10:13:22Z"
      message: ReplicaSet "keda-operator-7cb85c7c47" has successfully progressed.
      reason: NewReplicaSetAvailable
      status: "True"
      type: Progressing
    - lastTransitionTime: "2025-07-11T19:43:30Z"
      lastUpdateTime: "2025-07-11T19:43:30Z"
      message: Deployment has minimum availability.
      reason: MinimumReplicasAvailable
      status: "True"
      type: Available
    observedGeneration: 1
    readyReplicas: 1
    replicas: 1
    updatedReplicas: 1
- apiVersion: apps/v1
  kind: Deployment
  metadata:
    annotations:
      deployment.kubernetes.io/revision: "1"
      meta.helm.sh/release-name: keda
      meta.helm.sh/release-namespace: keda-system
    creationTimestamp: "2025-05-27T10:12:52Z"
    generation: 1
    labels:
      app: keda-operator-metrics-apiserver
      app.kubernetes.io/component: operator
      app.kubernetes.io/instance: keda
      app.kubernetes.io/managed-by: Helm
      app.kubernetes.io/name: keda-operator-metrics-apiserver
      app.kubernetes.io/part-of: keda-operator
      app.kubernetes.io/version: 2.17.1
      helm.sh/chart: keda-2.17.1
    name: keda-operator-metrics-apiserver
    namespace: keda-system
    resourceVersion: "24419040"
    uid: bad68499-7f15-4ef8-bc37-1171026ea8da
  spec:
    progressDeadlineSeconds: 600
    replicas: 1
    revisionHistoryLimit: 10
    selector:
      matchLabels:
        app: keda-operator-metrics-apiserver
    strategy:
      rollingUpdate:
        maxSurge: 25%
        maxUnavailable: 25%
      type: RollingUpdate
    template:
      metadata:
        creationTimestamp: null
        labels:
          app: keda-operator-metrics-apiserver
          app.kubernetes.io/component: operator
          app.kubernetes.io/instance: keda
          app.kubernetes.io/managed-by: Helm
          app.kubernetes.io/name: keda-operator-metrics-apiserver
          app.kubernetes.io/part-of: keda-operator
          app.kubernetes.io/version: 2.17.1
          helm.sh/chart: keda-2.17.1
      spec:
        automountServiceAccountToken: true
        containers:
        - args:
          - --port=8080
          - --secure-port=6443
          - --logtostderr=true
          - --stderrthreshold=ERROR
          - --disable-compression=true
          - --metrics-service-address=keda-operator.keda-system.svc.cluster.local:9666
          - --client-ca-file=/certs/ca.crt
          - --tls-cert-file=/certs/tls.crt
          - --tls-private-key-file=/certs/tls.key
          - --cert-dir=/certs
          - --v=0
          command:
          - /keda-adapter
          env:
          - name: WATCH_NAMESPACE
          - name: POD_NAMESPACE
            valueFrom:
              fieldRef:
                apiVersion: v1
                fieldPath: metadata.namespace
          - name: KEDA_HTTP_DEFAULT_TIMEOUT
            value: "3000"
          - name: KEDA_HTTP_MIN_TLS_VERSION
            value: TLS12
          image: ghcr.io/kedacore/keda-metrics-apiserver:2.17.1
          imagePullPolicy: Always
          livenessProbe:
            failureThreshold: 3
            httpGet:
              path: /healthz
              port: 6443
              scheme: HTTPS
            initialDelaySeconds: 5
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 1
          name: keda-operator-metrics-apiserver
          ports:
          - containerPort: 6443
            name: https
            protocol: TCP
          - containerPort: 8080
            name: metrics
            protocol: TCP
          readinessProbe:
            failureThreshold: 3
            httpGet:
              path: /readyz
              port: 6443
              scheme: HTTPS
            initialDelaySeconds: 5
            periodSeconds: 3
            successThreshold: 1
            timeoutSeconds: 1
          resources:
            limits:
              cpu: "1"
              memory: 1000Mi
            requests:
              cpu: 100m
              memory: 100Mi
          securityContext:
            allowPrivilegeEscalation: false
            capabilities:
              drop:
              - ALL
            readOnlyRootFilesystem: true
            seccompProfile:
              type: RuntimeDefault
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /certs
            name: certificates
            readOnly: true
        dnsPolicy: ClusterFirst
        enableServiceLinks: true
        nodeSelector:
          kubernetes.io/os: linux
        restartPolicy: Always
        schedulerName: default-scheduler
        securityContext:
          runAsNonRoot: true
        serviceAccount: keda-metrics-server
        serviceAccountName: keda-metrics-server
        terminationGracePeriodSeconds: 30
        volumes:
        - name: certificates
          secret:
            defaultMode: 420
            secretName: kedaorg-certs
  status:
    availableReplicas: 1
    conditions:
    - lastTransitionTime: "2025-05-27T10:12:53Z"
      lastUpdateTime: "2025-05-27T10:13:16Z"
      message: ReplicaSet "keda-operator-metrics-apiserver-7cd6969b85" has successfully
        progressed.
      reason: NewReplicaSetAvailable
      status: "True"
      type: Progressing
    - lastTransitionTime: "2025-07-11T19:43:15Z"
      lastUpdateTime: "2025-07-11T19:43:15Z"
      message: Deployment has minimum availability.
      reason: MinimumReplicasAvailable
      status: "True"
      type: Available
    observedGeneration: 1
    readyReplicas: 1
    replicas: 1
    updatedReplicas: 1
- apiVersion: apps/v1
  kind: Deployment
  metadata:
    annotations:
      deployment.kubernetes.io/revision: "1"
      meta.helm.sh/release-name: aws-load-balancer-controller
      meta.helm.sh/release-namespace: kube-system
    creationTimestamp: "2025-07-03T22:15:14Z"
    generation: 2
    labels:
      app.kubernetes.io/instance: aws-load-balancer-controller
      app.kubernetes.io/managed-by: Helm
      app.kubernetes.io/name: aws-load-balancer-controller
      app.kubernetes.io/version: v2.13.3
      helm.sh/chart: aws-load-balancer-controller-1.13.3
    name: aws-load-balancer-controller
    namespace: kube-system
    resourceVersion: "24386798"
    uid: 164eda21-036c-468d-abad-1236507c5495
  spec:
    progressDeadlineSeconds: 600
    replicas: 1
    revisionHistoryLimit: 10
    selector:
      matchLabels:
        app.kubernetes.io/instance: aws-load-balancer-controller
        app.kubernetes.io/name: aws-load-balancer-controller
    strategy:
      rollingUpdate:
        maxSurge: 25%
        maxUnavailable: 25%
      type: RollingUpdate
    template:
      metadata:
        annotations:
          prometheus.io/port: "8080"
          prometheus.io/scrape: "true"
        creationTimestamp: null
        labels:
          app.kubernetes.io/instance: aws-load-balancer-controller
          app.kubernetes.io/name: aws-load-balancer-controller
      spec:
        affinity:
          podAntiAffinity:
            preferredDuringSchedulingIgnoredDuringExecution:
            - podAffinityTerm:
                labelSelector:
                  matchExpressions:
                  - key: app.kubernetes.io/name
                    operator: In
                    values:
                    - aws-load-balancer-controller
                topologyKey: kubernetes.io/hostname
              weight: 100
        containers:
        - args:
          - --cluster-name=production-wks
          - --ingress-class=alb
          - --aws-region=eu-central-1
          - --aws-vpc-id=vpc-0e937ae9134a95ede
          - --enable-shield=false
          - --enable-waf=false
          - --enable-wafv2=false
          image: public.ecr.aws/eks/aws-load-balancer-controller:v2.13.3
          imagePullPolicy: IfNotPresent
          livenessProbe:
            failureThreshold: 2
            httpGet:
              path: /healthz
              port: 61779
              scheme: HTTP
            initialDelaySeconds: 30
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 10
          name: aws-load-balancer-controller
          ports:
          - containerPort: 9443
            name: webhook-server
            protocol: TCP
          - containerPort: 8080
            name: metrics-server
            protocol: TCP
          readinessProbe:
            failureThreshold: 2
            httpGet:
              path: /readyz
              port: 61779
              scheme: HTTP
            initialDelaySeconds: 10
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 10
          resources: {}
          securityContext:
            allowPrivilegeEscalation: false
            readOnlyRootFilesystem: true
            runAsNonRoot: true
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /tmp/k8s-webhook-server/serving-certs
            name: cert
            readOnly: true
        dnsPolicy: ClusterFirst
        priorityClassName: system-cluster-critical
        restartPolicy: Always
        schedulerName: default-scheduler
        securityContext:
          fsGroup: 65534
        serviceAccount: aws-load-balancer-controller
        serviceAccountName: aws-load-balancer-controller
        terminationGracePeriodSeconds: 10
        volumes:
        - name: cert
          secret:
            defaultMode: 420
            secretName: aws-load-balancer-tls
  status:
    availableReplicas: 1
    conditions:
    - lastTransitionTime: "2025-07-03T22:15:14Z"
      lastUpdateTime: "2025-07-03T22:15:31Z"
      message: ReplicaSet "aws-load-balancer-controller-7b998cf565" has successfully
        progressed.
      reason: NewReplicaSetAvailable
      status: "True"
      type: Progressing
    - lastTransitionTime: "2025-07-09T15:36:19Z"
      lastUpdateTime: "2025-07-09T15:36:19Z"
      message: Deployment has minimum availability.
      reason: MinimumReplicasAvailable
      status: "True"
      type: Available
    observedGeneration: 2
    readyReplicas: 1
    replicas: 1
    updatedReplicas: 1
- apiVersion: apps/v1
  kind: Deployment
  metadata:
    annotations:
      deployment.kubernetes.io/revision: "12"
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"apps/v1","kind":"Deployment","metadata":{"annotations":{},"labels":{"app.kubernetes.io/instance":"cluster-autoscaler","app.kubernetes.io/managed-by":"Helm","app.kubernetes.io/name":"aws-cluster-autoscaler","helm.sh/chart":"cluster-autoscaler-9.47.0"},"name":"cluster-autoscaler-aws-cluster-autoscaler","namespace":"kube-system"},"spec":{"replicas":1,"selector":{"matchLabels":{"app.kubernetes.io/instance":"cluster-autoscaler","app.kubernetes.io/name":"aws-cluster-autoscaler"}},"template":{"metadata":{"labels":{"app.kubernetes.io/instance":"cluster-autoscaler","app.kubernetes.io/name":"aws-cluster-autoscaler"}},"spec":{"containers":[{"command":["./cluster-autoscaler","--cloud-provider=aws","--namespace=kube-system","--node-group-auto-discovery=asg:tag=k8s.io/cluster-autoscaler/enabled,k8s.io/cluster-autoscaler/production-wks","--balance-similar-node-groups=true","--logtostderr=true","--stderrthreshold=info","--v=4","--scale-down-utilization-threshold=0.3","--scale-down-delay-after-add=3m","--scale-down-unneeded-time=2m","--scale-down-delay-after-delete=10s","--scale-down-delay-after-failure=3m","--skip-nodes-with-local-storage=false","--skip-nodes-with-system-pods=false","--new-pod-scale-up-delay=0s","--expander=least-waste","--cores-total=10:80","--memory-total=40000:320000","--max-nodes-total=20"],"env":[{"name":"POD_NAMESPACE","valueFrom":{"fieldRef":{"fieldPath":"metadata.namespace"}}},{"name":"SERVICE_ACCOUNT","valueFrom":{"fieldRef":{"fieldPath":"spec.serviceAccountName"}}},{"name":"AWS_REGION","value":"eu-central-1"}],"image":"registry.k8s.io/autoscaling/cluster-autoscaler:v1.33.0","livenessProbe":{"httpGet":{"path":"/health-check","port":8085},"periodSeconds":10},"name":"aws-cluster-autoscaler","ports":[{"containerPort":8085,"protocol":"TCP"}],"resources":{"limits":{"cpu":"100m","memory":"300Mi"},"requests":{"cpu":"100m","memory":"300Mi"}}}],"priorityClassName":"system-cluster-critical","serviceAccountName":"cluster-autoscaler-aws-cluster-autoscaler"}}}}
      meta.helm.sh/release-name: cluster-autoscaler
      meta.helm.sh/release-namespace: kube-system
    creationTimestamp: "2025-07-08T15:43:08Z"
    generation: 12
    labels:
      app.kubernetes.io/instance: cluster-autoscaler
      app.kubernetes.io/managed-by: Helm
      app.kubernetes.io/name: aws-cluster-autoscaler
      helm.sh/chart: cluster-autoscaler-9.47.0
    name: cluster-autoscaler-aws-cluster-autoscaler
    namespace: kube-system
    resourceVersion: "********"
    uid: 625ccb68-3866-44e1-960e-11954fd31fb2
  spec:
    progressDeadlineSeconds: 600
    replicas: 1
    revisionHistoryLimit: 10
    selector:
      matchLabels:
        app.kubernetes.io/instance: cluster-autoscaler
        app.kubernetes.io/name: aws-cluster-autoscaler
    strategy:
      rollingUpdate:
        maxSurge: 25%
        maxUnavailable: 25%
      type: RollingUpdate
    template:
      metadata:
        annotations:
          kubectl.kubernetes.io/restartedAt: "2025-07-09T17:58:48+02:00"
        creationTimestamp: null
        labels:
          app.kubernetes.io/instance: cluster-autoscaler
          app.kubernetes.io/name: aws-cluster-autoscaler
      spec:
        containers:
        - args:
          - --v=4
          - --stderrthreshold=info
          - --cloud-provider=aws
          - --skip-nodes-with-local-storage=false
          - --expander=least-waste
          - --node-group-auto-discovery=asg:tag=k8s.io/cluster-autoscaler/enabled,k8s.io/cluster-autoscaler/production-wks
          - --balance-similar-node-groups
          - --skip-nodes-with-system-pods=false
          - --scale-down-delay-after-add=10m
          - --scale-down-unneeded-time=10m
          command:
          - ./cluster-autoscaler
          - --cloud-provider=aws
          - --namespace=kube-system
          - --node-group-auto-discovery=asg:tag=k8s.io/cluster-autoscaler/enabled,k8s.io/cluster-autoscaler/production-wks
          - --balance-similar-node-groups=true
          - --logtostderr=true
          - --stderrthreshold=info
          - --v=4
          - --scale-down-utilization-threshold=0.3
          - --scale-down-delay-after-add=3m
          - --scale-down-unneeded-time=2m
          - --scale-down-delay-after-delete=10s
          - --scale-down-delay-after-failure=3m
          - --skip-nodes-with-local-storage=false
          - --skip-nodes-with-system-pods=false
          - --new-pod-scale-up-delay=0s
          - --expander=least-waste
          - --cores-total=10:80
          - --memory-total=40000:320000
          - --max-nodes-total=20
          env:
          - name: POD_NAMESPACE
            valueFrom:
              fieldRef:
                apiVersion: v1
                fieldPath: metadata.namespace
          - name: SERVICE_ACCOUNT
            valueFrom:
              fieldRef:
                apiVersion: v1
                fieldPath: spec.serviceAccountName
          - name: AWS_REGION
            value: eu-central-1
          image: registry.k8s.io/autoscaling/cluster-autoscaler:v1.33.0
          imagePullPolicy: IfNotPresent
          livenessProbe:
            failureThreshold: 3
            httpGet:
              path: /health-check
              port: 8085
              scheme: HTTP
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 1
          name: aws-cluster-autoscaler
          ports:
          - containerPort: 8085
            protocol: TCP
          resources:
            limits:
              cpu: 100m
              memory: 300Mi
            requests:
              cpu: 100m
              memory: 300Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
        dnsPolicy: ClusterFirst
        priorityClassName: system-cluster-critical
        restartPolicy: Always
        schedulerName: default-scheduler
        securityContext: {}
        serviceAccount: cluster-autoscaler-aws-cluster-autoscaler
        serviceAccountName: cluster-autoscaler-aws-cluster-autoscaler
        terminationGracePeriodSeconds: 30
  status:
    availableReplicas: 1
    conditions:
    - lastTransitionTime: "2025-07-09T15:58:51Z"
      lastUpdateTime: "2025-07-09T15:58:51Z"
      message: Deployment has minimum availability.
      reason: MinimumReplicasAvailable
      status: "True"
      type: Available
    - lastTransitionTime: "2025-07-08T15:43:08Z"
      lastUpdateTime: "2025-07-09T15:58:51Z"
      message: ReplicaSet "cluster-autoscaler-aws-cluster-autoscaler-7cc6dcd89f" has
        successfully progressed.
      reason: NewReplicaSetAvailable
      status: "True"
      type: Progressing
    observedGeneration: 12
    readyReplicas: 1
    replicas: 1
    updatedReplicas: 1
- apiVersion: apps/v1
  kind: Deployment
  metadata:
    annotations:
      deployment.kubernetes.io/revision: "1"
    creationTimestamp: "2025-05-19T11:14:42Z"
    generation: 1
    labels:
      eks.amazonaws.com/component: coredns
      k8s-app: kube-dns
      kubernetes.io/name: CoreDNS
    name: coredns
    namespace: kube-system
    resourceVersion: "22835345"
    uid: e72a1d93-6cd5-4ebc-b837-8780d543c08b
  spec:
    progressDeadlineSeconds: 600
    replicas: 2
    revisionHistoryLimit: 10
    selector:
      matchLabels:
        eks.amazonaws.com/component: coredns
        k8s-app: kube-dns
    strategy:
      rollingUpdate:
        maxSurge: 25%
        maxUnavailable: 1
      type: RollingUpdate
    template:
      metadata:
        creationTimestamp: null
        labels:
          eks.amazonaws.com/component: coredns
          k8s-app: kube-dns
      spec:
        affinity:
          nodeAffinity:
            requiredDuringSchedulingIgnoredDuringExecution:
              nodeSelectorTerms:
              - matchExpressions:
                - key: kubernetes.io/os
                  operator: In
                  values:
                  - linux
                - key: kubernetes.io/arch
                  operator: In
                  values:
                  - amd64
                  - arm64
          podAntiAffinity:
            preferredDuringSchedulingIgnoredDuringExecution:
            - podAffinityTerm:
                labelSelector:
                  matchExpressions:
                  - key: k8s-app
                    operator: In
                    values:
                    - kube-dns
                topologyKey: kubernetes.io/hostname
              weight: 100
        containers:
        - args:
          - -conf
          - /etc/coredns/Corefile
          image: 602401143452.dkr.ecr.eu-central-1.amazonaws.com/eks/coredns:v1.11.4-eksbuild.2
          imagePullPolicy: IfNotPresent
          livenessProbe:
            failureThreshold: 5
            httpGet:
              path: /health
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 60
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 5
          name: coredns
          ports:
          - containerPort: 53
            name: dns
            protocol: UDP
          - containerPort: 53
            name: dns-tcp
            protocol: TCP
          - containerPort: 9153
            name: metrics
            protocol: TCP
          readinessProbe:
            failureThreshold: 3
            httpGet:
              path: /ready
              port: 8181
              scheme: HTTP
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 1
          resources:
            limits:
              memory: 170Mi
            requests:
              cpu: 100m
              memory: 70Mi
          securityContext:
            allowPrivilegeEscalation: false
            capabilities:
              add:
              - NET_BIND_SERVICE
              drop:
              - ALL
            readOnlyRootFilesystem: true
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /etc/coredns
            name: config-volume
            readOnly: true
        dnsPolicy: Default
        priorityClassName: system-cluster-critical
        restartPolicy: Always
        schedulerName: default-scheduler
        securityContext: {}
        serviceAccount: coredns
        serviceAccountName: coredns
        terminationGracePeriodSeconds: 30
        tolerations:
        - effect: NoSchedule
          key: node-role.kubernetes.io/control-plane
        - key: CriticalAddonsOnly
          operator: Exists
        topologySpreadConstraints:
        - labelSelector:
            matchLabels:
              k8s-app: kube-dns
          maxSkew: 1
          topologyKey: topology.kubernetes.io/zone
          whenUnsatisfiable: ScheduleAnyway
        volumes:
        - configMap:
            defaultMode: 420
            items:
            - key: Corefile
              path: Corefile
            name: coredns
          name: config-volume
  status:
    availableReplicas: 2
    conditions:
    - lastTransitionTime: "2025-05-19T11:14:42Z"
      lastUpdateTime: "2025-05-19T11:23:23Z"
      message: ReplicaSet "coredns-749464bbc5" has successfully progressed.
      reason: NewReplicaSetAvailable
      status: "True"
      type: Progressing
    - lastTransitionTime: "2025-06-29T13:08:16Z"
      lastUpdateTime: "2025-06-29T13:08:16Z"
      message: Deployment has minimum availability.
      reason: MinimumReplicasAvailable
      status: "True"
      type: Available
    observedGeneration: 1
    readyReplicas: 2
    replicas: 2
    updatedReplicas: 2
- apiVersion: apps/v1
  kind: Deployment
  metadata:
    annotations:
      deployment.kubernetes.io/revision: "2"
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"apps/v1","kind":"Deployment","metadata":{"annotations":{},"labels":{"app.kubernetes.io/name":"aws-ebs-csi-driver"},"name":"ebs-csi-controller","namespace":"kube-system"},"spec":{"replicas":2,"revisionHistoryLimit":10,"selector":{"matchLabels":{"app":"ebs-csi-controller","app.kubernetes.io/name":"aws-ebs-csi-driver"}},"strategy":{"rollingUpdate":{"maxUnavailable":1},"type":"RollingUpdate"},"template":{"metadata":{"labels":{"app":"ebs-csi-controller","app.kubernetes.io/name":"aws-ebs-csi-driver"}},"spec":{"affinity":{"nodeAffinity":{"preferredDuringSchedulingIgnoredDuringExecution":[{"preference":{"matchExpressions":[{"key":"eks.amazonaws.com/compute-type","operator":"NotIn","values":["fargate"]}]},"weight":1}]},"podAntiAffinity":{"preferredDuringSchedulingIgnoredDuringExecution":[{"podAffinityTerm":{"labelSelector":{"matchExpressions":[{"key":"app","operator":"In","values":["ebs-csi-controller"]}]},"topologyKey":"kubernetes.io/hostname"},"weight":100}]}},"containers":[{"args":["--endpoint=$(CSI_ENDPOINT)","--batching=true","--logging-format=text","--user-agent-extra=kustomize","--v=2"],"env":[{"name":"CSI_ENDPOINT","value":"unix:///var/lib/csi/sockets/pluginproxy/csi.sock"},{"name":"CSI_NODE_NAME","valueFrom":{"fieldRef":{"fieldPath":"spec.nodeName"}}},{"name":"AWS_ACCESS_KEY_ID","valueFrom":{"secretKeyRef":{"key":"key_id","name":"aws-secret","optional":true}}},{"name":"AWS_SECRET_ACCESS_KEY","valueFrom":{"secretKeyRef":{"key":"access_key","name":"aws-secret","optional":true}}},{"name":"AWS_EC2_ENDPOINT","valueFrom":{"configMapKeyRef":{"key":"endpoint","name":"aws-meta","optional":true}}}],"image":"public.ecr.aws/ebs-csi-driver/aws-ebs-csi-driver:v1.28.0","imagePullPolicy":"IfNotPresent","livenessProbe":{"failureThreshold":5,"httpGet":{"path":"/healthz","port":"healthz"},"initialDelaySeconds":10,"periodSeconds":10,"timeoutSeconds":3},"name":"ebs-plugin","ports":[{"containerPort":9808,"name":"healthz","protocol":"TCP"}],"readinessProbe":{"failureThreshold":5,"httpGet":{"path":"/healthz","port":"healthz"},"initialDelaySeconds":10,"periodSeconds":10,"timeoutSeconds":3},"resources":{"limits":{"memory":"256Mi"},"requests":{"cpu":"10m","memory":"40Mi"}},"securityContext":{"allowPrivilegeEscalation":false,"readOnlyRootFilesystem":true},"volumeMounts":[{"mountPath":"/var/lib/csi/sockets/pluginproxy/","name":"socket-dir"}]},{"args":["--timeout=60s","--csi-address=$(ADDRESS)","--v=2","--feature-gates=Topology=true","--extra-create-metadata","--leader-election=true","--default-fstype=ext4","--kube-api-qps=20","--kube-api-burst=100","--worker-threads=100"],"env":[{"name":"ADDRESS","value":"/var/lib/csi/sockets/pluginproxy/csi.sock"}],"image":"public.ecr.aws/eks-distro/kubernetes-csi/external-provisioner:v4.0.0-eks-1-29-5","imagePullPolicy":"IfNotPresent","name":"csi-provisioner","resources":{"limits":{"memory":"256Mi"},"requests":{"cpu":"10m","memory":"40Mi"}},"securityContext":{"allowPrivilegeEscalation":false,"readOnlyRootFilesystem":true},"volumeMounts":[{"mountPath":"/var/lib/csi/sockets/pluginproxy/","name":"socket-dir"}]},{"args":["--timeout=60s","--csi-address=$(ADDRESS)","--v=2","--leader-election=true","--kube-api-qps=20","--kube-api-burst=100","--worker-threads=100"],"env":[{"name":"ADDRESS","value":"/var/lib/csi/sockets/pluginproxy/csi.sock"}],"image":"public.ecr.aws/eks-distro/kubernetes-csi/external-attacher:v4.5.0-eks-1-29-5","imagePullPolicy":"IfNotPresent","name":"csi-attacher","resources":{"limits":{"memory":"256Mi"},"requests":{"cpu":"10m","memory":"40Mi"}},"securityContext":{"allowPrivilegeEscalation":false,"readOnlyRootFilesystem":true},"volumeMounts":[{"mountPath":"/var/lib/csi/sockets/pluginproxy/","name":"socket-dir"}]},{"args":["--csi-address=$(ADDRESS)","--leader-election=true","--extra-create-metadata","--kube-api-qps=20","--kube-api-burst=100","--worker-threads=100"],"env":[{"name":"ADDRESS","value":"/var/lib/csi/sockets/pluginproxy/csi.sock"}],"image":"public.ecr.aws/eks-distro/kubernetes-csi/external-snapshotter/csi-snapshotter:v7.0.0-eks-1-29-5","imagePullPolicy":"IfNotPresent","name":"csi-snapshotter","resources":{"limits":{"memory":"256Mi"},"requests":{"cpu":"10m","memory":"40Mi"}},"securityContext":{"allowPrivilegeEscalation":false,"readOnlyRootFilesystem":true},"volumeMounts":[{"mountPath":"/var/lib/csi/sockets/pluginproxy/","name":"socket-dir"}]},{"args":["--timeout=60s","--csi-address=$(ADDRESS)","--v=2","--handle-volume-inuse-error=false","--leader-election=true","--kube-api-qps=20","--kube-api-burst=100","--workers=100"],"env":[{"name":"ADDRESS","value":"/var/lib/csi/sockets/pluginproxy/csi.sock"}],"image":"public.ecr.aws/eks-distro/kubernetes-csi/external-resizer:v1.10.0-eks-1-29-5","imagePullPolicy":"IfNotPresent","name":"csi-resizer","resources":{"limits":{"memory":"256Mi"},"requests":{"cpu":"10m","memory":"40Mi"}},"securityContext":{"allowPrivilegeEscalation":false,"readOnlyRootFilesystem":true},"volumeMounts":[{"mountPath":"/var/lib/csi/sockets/pluginproxy/","name":"socket-dir"}]},{"args":["--csi-address=/csi/csi.sock"],"image":"public.ecr.aws/eks-distro/kubernetes-csi/livenessprobe:v2.12.0-eks-1-29-5","imagePullPolicy":"IfNotPresent","name":"liveness-probe","resources":{"limits":{"memory":"256Mi"},"requests":{"cpu":"10m","memory":"40Mi"}},"securityContext":{"allowPrivilegeEscalation":false,"readOnlyRootFilesystem":true},"volumeMounts":[{"mountPath":"/csi","name":"socket-dir"}]}],"nodeSelector":{"kubernetes.io/os":"linux"},"priorityClassName":"system-cluster-critical","securityContext":{"fsGroup":1000,"runAsGroup":1000,"runAsNonRoot":true,"runAsUser":1000},"serviceAccountName":"ebs-csi-controller-sa","tolerations":[{"key":"CriticalAddonsOnly","operator":"Exists"},{"effect":"NoExecute","operator":"Exists","tolerationSeconds":300}],"volumes":[{"emptyDir":{},"name":"socket-dir"}]}}}}
      meta.helm.sh/release-name: aws-ebs-csi-driver
      meta.helm.sh/release-namespace: kube-system
    creationTimestamp: "2025-05-19T11:48:24Z"
    generation: 2
    labels:
      app.kubernetes.io/component: csi-driver
      app.kubernetes.io/instance: aws-ebs-csi-driver
      app.kubernetes.io/managed-by: Helm
      app.kubernetes.io/name: aws-ebs-csi-driver
      app.kubernetes.io/version: 1.43.0
      helm.sh/chart: aws-ebs-csi-driver-2.43.0
    name: ebs-csi-controller
    namespace: kube-system
    resourceVersion: "********"
    uid: 464035ec-e478-492f-887f-84efa5e35e58
  spec:
    progressDeadlineSeconds: 600
    replicas: 2
    revisionHistoryLimit: 10
    selector:
      matchLabels:
        app: ebs-csi-controller
        app.kubernetes.io/instance: aws-ebs-csi-driver
        app.kubernetes.io/name: aws-ebs-csi-driver
    strategy:
      rollingUpdate:
        maxSurge: 25%
        maxUnavailable: 1
      type: RollingUpdate
    template:
      metadata:
        creationTimestamp: null
        labels:
          app: ebs-csi-controller
          app.kubernetes.io/component: csi-driver
          app.kubernetes.io/instance: aws-ebs-csi-driver
          app.kubernetes.io/managed-by: Helm
          app.kubernetes.io/name: aws-ebs-csi-driver
          app.kubernetes.io/version: 1.43.0
          helm.sh/chart: aws-ebs-csi-driver-2.43.0
      spec:
        affinity:
          nodeAffinity:
            preferredDuringSchedulingIgnoredDuringExecution:
            - preference:
                matchExpressions:
                - key: eks.amazonaws.com/compute-type
                  operator: NotIn
                  values:
                  - fargate
              weight: 1
          podAntiAffinity:
            preferredDuringSchedulingIgnoredDuringExecution:
            - podAffinityTerm:
                labelSelector:
                  matchExpressions:
                  - key: app
                    operator: In
                    values:
                    - ebs-csi-controller
                topologyKey: kubernetes.io/hostname
              weight: 100
        containers:
        - args:
          - --endpoint=$(CSI_ENDPOINT)
          - --batching=true
          - --logging-format=text
          - --user-agent-extra=kustomize
          - --v=2
          env:
          - name: CSI_ENDPOINT
            value: unix:///var/lib/csi/sockets/pluginproxy/csi.sock
          - name: CSI_NODE_NAME
            valueFrom:
              fieldRef:
                apiVersion: v1
                fieldPath: spec.nodeName
          - name: AWS_ACCESS_KEY_ID
            valueFrom:
              secretKeyRef:
                key: key_id
                name: aws-secret
                optional: true
          - name: AWS_SECRET_ACCESS_KEY
            valueFrom:
              secretKeyRef:
                key: access_key
                name: aws-secret
                optional: true
          - name: AWS_EC2_ENDPOINT
            valueFrom:
              configMapKeyRef:
                key: endpoint
                name: aws-meta
                optional: true
          image: public.ecr.aws/ebs-csi-driver/aws-ebs-csi-driver:v1.28.0
          imagePullPolicy: IfNotPresent
          livenessProbe:
            failureThreshold: 5
            httpGet:
              path: /healthz
              port: healthz
              scheme: HTTP
            initialDelaySeconds: 10
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 3
          name: ebs-plugin
          ports:
          - containerPort: 9808
            name: healthz
            protocol: TCP
          readinessProbe:
            failureThreshold: 5
            httpGet:
              path: /healthz
              port: healthz
              scheme: HTTP
            initialDelaySeconds: 10
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 3
          resources:
            limits:
              memory: 256Mi
            requests:
              cpu: 10m
              memory: 40Mi
          securityContext:
            allowPrivilegeEscalation: false
            readOnlyRootFilesystem: true
            seccompProfile:
              type: RuntimeDefault
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /var/lib/csi/sockets/pluginproxy/
            name: socket-dir
        - args:
          - --timeout=60s
          - --csi-address=$(ADDRESS)
          - --v=2
          - --feature-gates=Topology=true
          - --extra-create-metadata
          - --leader-election=true
          - --default-fstype=ext4
          - --kube-api-qps=20
          - --kube-api-burst=100
          - --worker-threads=100
          env:
          - name: ADDRESS
            value: /var/lib/csi/sockets/pluginproxy/csi.sock
          image: public.ecr.aws/eks-distro/kubernetes-csi/external-provisioner:v4.0.0-eks-1-29-5
          imagePullPolicy: IfNotPresent
          name: csi-provisioner
          resources:
            limits:
              memory: 256Mi
            requests:
              cpu: 10m
              memory: 40Mi
          securityContext:
            allowPrivilegeEscalation: false
            readOnlyRootFilesystem: true
            seccompProfile:
              type: RuntimeDefault
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /var/lib/csi/sockets/pluginproxy/
            name: socket-dir
        - args:
          - --timeout=60s
          - --csi-address=$(ADDRESS)
          - --v=2
          - --leader-election=true
          - --kube-api-qps=20
          - --kube-api-burst=100
          - --worker-threads=100
          env:
          - name: ADDRESS
            value: /var/lib/csi/sockets/pluginproxy/csi.sock
          image: public.ecr.aws/eks-distro/kubernetes-csi/external-attacher:v4.5.0-eks-1-29-5
          imagePullPolicy: IfNotPresent
          name: csi-attacher
          resources:
            limits:
              memory: 256Mi
            requests:
              cpu: 10m
              memory: 40Mi
          securityContext:
            allowPrivilegeEscalation: false
            readOnlyRootFilesystem: true
            seccompProfile:
              type: RuntimeDefault
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /var/lib/csi/sockets/pluginproxy/
            name: socket-dir
        - args:
          - --csi-address=$(ADDRESS)
          - --leader-election=true
          - --extra-create-metadata
          - --kube-api-qps=20
          - --kube-api-burst=100
          - --worker-threads=100
          env:
          - name: ADDRESS
            value: /var/lib/csi/sockets/pluginproxy/csi.sock
          image: public.ecr.aws/eks-distro/kubernetes-csi/external-snapshotter/csi-snapshotter:v7.0.0-eks-1-29-5
          imagePullPolicy: IfNotPresent
          name: csi-snapshotter
          resources:
            limits:
              memory: 256Mi
            requests:
              cpu: 10m
              memory: 40Mi
          securityContext:
            allowPrivilegeEscalation: false
            readOnlyRootFilesystem: true
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /var/lib/csi/sockets/pluginproxy/
            name: socket-dir
        - args:
          - --timeout=60s
          - --csi-address=$(ADDRESS)
          - --v=2
          - --handle-volume-inuse-error=false
          - --leader-election=true
          - --kube-api-qps=20
          - --kube-api-burst=100
          - --workers=100
          env:
          - name: ADDRESS
            value: /var/lib/csi/sockets/pluginproxy/csi.sock
          image: public.ecr.aws/eks-distro/kubernetes-csi/external-resizer:v1.10.0-eks-1-29-5
          imagePullPolicy: IfNotPresent
          name: csi-resizer
          resources:
            limits:
              memory: 256Mi
            requests:
              cpu: 10m
              memory: 40Mi
          securityContext:
            allowPrivilegeEscalation: false
            readOnlyRootFilesystem: true
            seccompProfile:
              type: RuntimeDefault
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /var/lib/csi/sockets/pluginproxy/
            name: socket-dir
        - args:
          - --csi-address=/csi/csi.sock
          image: public.ecr.aws/eks-distro/kubernetes-csi/livenessprobe:v2.12.0-eks-1-29-5
          imagePullPolicy: IfNotPresent
          name: liveness-probe
          resources:
            limits:
              memory: 256Mi
            requests:
              cpu: 10m
              memory: 40Mi
          securityContext:
            allowPrivilegeEscalation: false
            readOnlyRootFilesystem: true
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /csi
            name: socket-dir
        dnsPolicy: ClusterFirst
        nodeSelector:
          kubernetes.io/os: linux
        priorityClassName: system-cluster-critical
        restartPolicy: Always
        schedulerName: default-scheduler
        securityContext:
          fsGroup: 1000
          runAsGroup: 1000
          runAsNonRoot: true
          runAsUser: 1000
        serviceAccount: ebs-csi-controller-sa
        serviceAccountName: ebs-csi-controller-sa
        terminationGracePeriodSeconds: 30
        tolerations:
        - key: CriticalAddonsOnly
          operator: Exists
        - effect: NoExecute
          operator: Exists
          tolerationSeconds: 300
        volumes:
        - emptyDir: {}
          name: socket-dir
  status:
    availableReplicas: 2
    conditions:
    - lastTransitionTime: "2025-05-19T11:48:24Z"
      lastUpdateTime: "2025-05-19T11:51:05Z"
      message: ReplicaSet "ebs-csi-controller-69944dd65b" has successfully progressed.
      reason: NewReplicaSetAvailable
      status: "True"
      type: Progressing
    - lastTransitionTime: "2025-06-29T13:08:32Z"
      lastUpdateTime: "2025-06-29T13:08:32Z"
      message: Deployment has minimum availability.
      reason: MinimumReplicasAvailable
      status: "True"
      type: Available
    observedGeneration: 2
    readyReplicas: 2
    replicas: 2
    updatedReplicas: 2
- apiVersion: apps/v1
  kind: Deployment
  metadata:
    annotations:
      deployment.kubernetes.io/revision: "1"
      meta.helm.sh/release-name: metrics-server
      meta.helm.sh/release-namespace: kube-system
    creationTimestamp: "2025-07-08T14:49:39Z"
    generation: 1
    labels:
      app.kubernetes.io/instance: metrics-server
      app.kubernetes.io/managed-by: Helm
      app.kubernetes.io/name: metrics-server
      app.kubernetes.io/version: 0.7.2
      helm.sh/chart: metrics-server-3.12.2
    name: metrics-server
    namespace: kube-system
    resourceVersion: "22767369"
    uid: 1129040b-7c0e-45d7-93d2-28c9bb7eaf59
  spec:
    progressDeadlineSeconds: 600
    replicas: 1
    revisionHistoryLimit: 10
    selector:
      matchLabels:
        app.kubernetes.io/instance: metrics-server
        app.kubernetes.io/name: metrics-server
    strategy:
      rollingUpdate:
        maxSurge: 25%
        maxUnavailable: 25%
      type: RollingUpdate
    template:
      metadata:
        creationTimestamp: null
        labels:
          app.kubernetes.io/instance: metrics-server
          app.kubernetes.io/name: metrics-server
      spec:
        containers:
        - args:
          - --secure-port=10250
          - --cert-dir=/tmp
          - --kubelet-preferred-address-types=InternalIP,ExternalIP,Hostname
          - --kubelet-use-node-status-port
          - --metric-resolution=15s
          - --kubelet-insecure-tls
          image: registry.k8s.io/metrics-server/metrics-server:v0.7.2
          imagePullPolicy: IfNotPresent
          livenessProbe:
            failureThreshold: 3
            httpGet:
              path: /livez
              port: https
              scheme: HTTPS
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 1
          name: metrics-server
          ports:
          - containerPort: 10250
            name: https
            protocol: TCP
          readinessProbe:
            failureThreshold: 3
            httpGet:
              path: /readyz
              port: https
              scheme: HTTPS
            initialDelaySeconds: 20
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 1
          resources:
            limits:
              cpu: 100m
              memory: 128Mi
            requests:
              cpu: 50m
              memory: 64Mi
          securityContext:
            allowPrivilegeEscalation: false
            capabilities:
              drop:
              - ALL
            readOnlyRootFilesystem: true
            runAsNonRoot: true
            runAsUser: 1000
            seccompProfile:
              type: RuntimeDefault
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /tmp
            name: tmp
        dnsPolicy: ClusterFirst
        priorityClassName: system-cluster-critical
        restartPolicy: Always
        schedulerName: default-scheduler
        securityContext: {}
        serviceAccount: metrics-server
        serviceAccountName: metrics-server
        terminationGracePeriodSeconds: 30
        volumes:
        - emptyDir: {}
          name: tmp
  status:
    availableReplicas: 1
    conditions:
    - lastTransitionTime: "2025-07-08T14:49:40Z"
      lastUpdateTime: "2025-07-08T14:50:16Z"
      message: ReplicaSet "metrics-server-6f8f489bdd" has successfully progressed.
      reason: NewReplicaSetAvailable
      status: "True"
      type: Progressing
    - lastTransitionTime: "2025-07-09T13:29:29Z"
      lastUpdateTime: "2025-07-09T13:29:29Z"
      message: Deployment has minimum availability.
      reason: MinimumReplicasAvailable
      status: "True"
      type: Available
    observedGeneration: 1
    readyReplicas: 1
    replicas: 1
    updatedReplicas: 1
- apiVersion: apps/v1
  kind: Deployment
  metadata:
    annotations:
      deployment.kubernetes.io/revision: "1"
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"apps/v1","kind":"Deployment","metadata":{"annotations":{},"name":"security-event-processor","namespace":"security-monitoring"},"spec":{"replicas":1,"selector":{"matchLabels":{"app":"security-event-processor"}},"template":{"metadata":{"labels":{"app":"security-event-processor"}},"spec":{"containers":[{"env":[{"name":"PROMETHEUS_URL","value":"http://prometheus:9090"},{"name":"ALERTMANAGER_URL","value":"http://alertmanager:9093"},{"name":"SLACK_WEBHOOK_URL","valueFrom":{"secretKeyRef":{"key":"slack-webhook-url","name":"security-alerts"}}}],"image":"security/event-processor:latest","name":"event-processor","resources":{"limits":{"cpu":"500m","memory":"512Mi"},"requests":{"cpu":"100m","memory":"128Mi"}},"securityContext":{"allowPrivilegeEscalation":false,"capabilities":{"drop":["ALL"]},"readOnlyRootFilesystem":true},"volumeMounts":[{"mountPath":"/tmp","name":"tmp"},{"mountPath":"/app/cache","name":"cache"}]}],"securityContext":{"fsGroup":2000,"runAsNonRoot":true,"runAsUser":1000},"serviceAccountName":"security-monitor","volumes":[{"emptyDir":{},"name":"tmp"},{"emptyDir":{},"name":"cache"}]}}}}
    creationTimestamp: "2025-05-23T21:45:49Z"
    generation: 1
    name: security-event-processor
    namespace: security-monitoring
    resourceVersion: "********"
    uid: 8708a9ec-dca2-4e20-9477-bc6a74864b18
  spec:
    progressDeadlineSeconds: 600
    replicas: 1
    revisionHistoryLimit: 10
    selector:
      matchLabels:
        app: security-event-processor
    strategy:
      rollingUpdate:
        maxSurge: 25%
        maxUnavailable: 25%
      type: RollingUpdate
    template:
      metadata:
        creationTimestamp: null
        labels:
          app: security-event-processor
      spec:
        containers:
        - env:
          - name: PROMETHEUS_URL
            value: http://prometheus:9090
          - name: ALERTMANAGER_URL
            value: http://alertmanager:9093
          - name: SLACK_WEBHOOK_URL
            valueFrom:
              secretKeyRef:
                key: slack-webhook-url
                name: security-alerts
          image: security/event-processor:latest
          imagePullPolicy: Always
          name: event-processor
          resources:
            limits:
              cpu: 500m
              memory: 512Mi
            requests:
              cpu: 100m
              memory: 128Mi
          securityContext:
            allowPrivilegeEscalation: false
            capabilities:
              drop:
              - ALL
            readOnlyRootFilesystem: true
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /tmp
            name: tmp
          - mountPath: /app/cache
            name: cache
        dnsPolicy: ClusterFirst
        restartPolicy: Always
        schedulerName: default-scheduler
        securityContext:
          fsGroup: 2000
          runAsNonRoot: true
          runAsUser: 1000
        serviceAccount: security-monitor
        serviceAccountName: security-monitor
        terminationGracePeriodSeconds: 30
        volumes:
        - emptyDir: {}
          name: tmp
        - emptyDir: {}
          name: cache
  status:
    conditions:
    - lastTransitionTime: "2025-05-23T21:45:49Z"
      lastUpdateTime: "2025-05-23T21:45:49Z"
      message: Deployment does not have minimum availability.
      reason: MinimumReplicasUnavailable
      status: "False"
      type: Available
    - lastTransitionTime: "2025-07-11T19:53:02Z"
      lastUpdateTime: "2025-07-11T19:53:02Z"
      message: ReplicaSet "security-event-processor-747d94886f" has timed out progressing.
      reason: ProgressDeadlineExceeded
      status: "False"
      type: Progressing
    observedGeneration: 1
    replicas: 1
    unavailableReplicas: 1
    updatedReplicas: 1
- apiVersion: apps/v1
  kind: Deployment
  metadata:
    annotations:
      deployment.kubernetes.io/revision: "1"
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"apps/v1","kind":"Deployment","metadata":{"annotations":{},"labels":{"app":"demo-backend","tenant":"demo"},"name":"demo-backend","namespace":"tenant-demo"},"spec":{"replicas":1,"selector":{"matchLabels":{"app":"demo-backend"}},"template":{"metadata":{"labels":{"app":"demo-backend","tenant":"demo"}},"spec":{"containers":[{"env":[{"name":"TENANT_ID","valueFrom":{"configMapKeyRef":{"key":"TENANT_ID","name":"demo-config"}}},{"name":"TENANT_NAME","valueFrom":{"configMapKeyRef":{"key":"TENANT_NAME","name":"demo-config"}}},{"name":"DB_HOST","valueFrom":{"secretKeyRef":{"key":"DB_HOST","name":"demo-secret"}}}],"image":"nginx:alpine","name":"backend","ports":[{"containerPort":80}],"resources":{"limits":{"cpu":"200m","memory":"256Mi"},"requests":{"cpu":"100m","memory":"128Mi"}}}]}}}}
    creationTimestamp: "2025-07-11T17:58:18Z"
    generation: 1
    labels:
      app: demo-backend
      tenant: demo
    name: demo-backend
    namespace: tenant-demo
    resourceVersion: "24341537"
    uid: 92ea07c8-7bfc-487e-b202-d30aab03f783
  spec:
    progressDeadlineSeconds: 600
    replicas: 1
    revisionHistoryLimit: 10
    selector:
      matchLabels:
        app: demo-backend
    strategy:
      rollingUpdate:
        maxSurge: 25%
        maxUnavailable: 25%
      type: RollingUpdate
    template:
      metadata:
        creationTimestamp: null
        labels:
          app: demo-backend
          tenant: demo
      spec:
        containers:
        - env:
          - name: TENANT_ID
            valueFrom:
              configMapKeyRef:
                key: TENANT_ID
                name: demo-config
          - name: TENANT_NAME
            valueFrom:
              configMapKeyRef:
                key: TENANT_NAME
                name: demo-config
          - name: DB_HOST
            valueFrom:
              secretKeyRef:
                key: DB_HOST
                name: demo-secret
          image: nginx:alpine
          imagePullPolicy: IfNotPresent
          name: backend
          ports:
          - containerPort: 80
            protocol: TCP
          resources:
            limits:
              cpu: 200m
              memory: 256Mi
            requests:
              cpu: 100m
              memory: 128Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
        dnsPolicy: ClusterFirst
        restartPolicy: Always
        schedulerName: default-scheduler
        securityContext: {}
        terminationGracePeriodSeconds: 30
  status:
    availableReplicas: 1
    conditions:
    - lastTransitionTime: "2025-07-11T17:58:22Z"
      lastUpdateTime: "2025-07-11T17:58:22Z"
      message: Deployment has minimum availability.
      reason: MinimumReplicasAvailable
      status: "True"
      type: Available
    - lastTransitionTime: "2025-07-11T17:58:18Z"
      lastUpdateTime: "2025-07-11T17:58:22Z"
      message: ReplicaSet "demo-backend-7dc645c985" has successfully progressed.
      reason: NewReplicaSetAvailable
      status: "True"
      type: Progressing
    observedGeneration: 1
    readyReplicas: 1
    replicas: 1
    updatedReplicas: 1
- apiVersion: apps/v1
  kind: Deployment
  metadata:
    annotations:
      deployment.kubernetes.io/revision: "1"
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"apps/v1","kind":"Deployment","metadata":{"annotations":{},"labels":{"app":"optimized-demo-backend","component":"backend","tenant":"optimized-demo"},"name":"optimized-demo-backend","namespace":"tenant-optimized-demo"},"spec":{"replicas":1,"selector":{"matchLabels":{"app":"optimized-demo-backend","component":"backend","tenant":"optimized-demo"}},"template":{"metadata":{"labels":{"app":"optimized-demo-backend","component":"backend","tenant":"optimized-demo"}},"spec":{"containers":[{"env":[{"name":"TENANT_ID","value":"optimized-demo"},{"name":"DB_HOST","value":"production-aurora-serverless.cluster-cpmagwki2kv8.eu-central-1.rds.amazonaws.com"},{"name":"DB_PORT","value":"3306"},{"name":"DB_NAME","value":"architrave"},{"name":"DB_USER","valueFrom":{"secretKeyRef":{"key":"DB_USER","name":"optimized-demo-db-secret"}}},{"name":"DB_PASSWORD","valueFrom":{"secretKeyRef":{"key":"DB_PASSWORD","name":"optimized-demo-db-secret"}}},{"name":"DB_SSL","valueFrom":{"secretKeyRef":{"key":"DB_SSL","name":"optimized-demo-db-secret"}}},{"name":"DB_SSL_CA","valueFrom":{"secretKeyRef":{"key":"DB_SSL_CA","name":"optimized-demo-db-secret"}}},{"name":"DB_SSL_VERIFY","valueFrom":{"secretKeyRef":{"key":"DB_SSL_VERIFY","name":"optimized-demo-db-secret"}}},{"name":"RABBITMQ_URL","value":"amqp://guest:guest@optimized-demo-rabbitmq-service:5672/"},{"name":"ENVIRONMENT","value":"production"},{"name":"LANGUAGE","value":"en"}],"image":"545009857703.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:2.0.56-test","livenessProbe":{"initialDelaySeconds":60,"periodSeconds":30,"tcpSocket":{"port":9000}},"name":"backend","ports":[{"containerPort":9000}],"readinessProbe":{"initialDelaySeconds":30,"periodSeconds":30,"tcpSocket":{"port":9000}},"resources":{"limits":{"cpu":"500m","memory":"512Mi"},"requests":{"cpu":"100m","memory":"256Mi"}},"volumeMounts":[{"mountPath":"/tmp","name":"ssl-certs"},{"mountPath":"/shared-app","name":"shared-app"}]},{"image":"nginx:1.21-alpine","livenessProbe":{"failureThreshold":5,"httpGet":{"httpHeaders":[{"name":"User-Agent","value":"k8s-liveness-probe"}],"path":"/api/health","port":8080},"initialDelaySeconds":90,"periodSeconds":60,"successThreshold":1,"timeoutSeconds":10},"name":"nginx","ports":[{"containerPort":8080}],"readinessProbe":{"failureThreshold":10,"httpGet":{"httpHeaders":[{"name":"User-Agent","value":"k8s-readiness-probe"}],"path":"/api/health","port":8080},"initialDelaySeconds":60,"periodSeconds":30,"successThreshold":1,"timeoutSeconds":5},"resources":{"limits":{"cpu":"200m","memory":"128Mi"},"requests":{"cpu":"50m","memory":"64Mi"}},"volumeMounts":[{"mountPath":"/shared-app","name":"shared-app"},{"mountPath":"/etc/nginx/conf.d","name":"nginx-config"}]}],"initContainers":[{"command":["sh","-c","echo \"Downloading RDS CA certificate...\"\ncurl -o /tmp/rds-ca-2019-root.pem https://s3.amazonaws.com/rds-downloads/rds-ca-2019-root.pem\necho \"SSL certificate downloaded successfully\"\n"],"image":"curlimages/curl:latest","name":"ssl-cert-downloader","volumeMounts":[{"mountPath":"/tmp","name":"ssl-certs"}]},{"command":["sh","-c","echo \"Copying application files from /storage/ArchAssets to shared volume...\"\nif [ -d \"/storage/ArchAssets\" ]; then\n    cp -r /storage/ArchAssets/* /shared-app/ 2\u003e/dev/null || true\n    echo \"Application files copied successfully from /storage/ArchAssets\"\n    echo \"Copied files:\"\n    ls -la /shared-app/ | head -10\nelse\n    echo \"Warning: /storage/ArchAssets directory not found in webapp_dev image\"\n    echo \"Available directories in /storage:\"\n    ls -la /storage/ 2\u003e/dev/null || echo \"No /storage directory\"\n    echo \"Available directories in root:\"\n    ls -la / | grep -E \"(var|storage|app)\" || echo \"No relevant directories found\"\nfi\necho \"Application files copy process completed\"\n"],"image":"545009857703.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:2.0.56-test","name":"app-files-copier","volumeMounts":[{"mountPath":"/shared-app","name":"shared-app"}]},{"command":["sh","-c","echo \"Fixing PHP application config path issues and PHP-FPM compatibility...\"\n\n# Fix the relative path issue in /shared-app/public/api/index.php\nif [ -f \"/shared-app/public/api/index.php\" ]; then\n  echo \"Found API index.php, fixing config path...\"\n\n  # Create backup\n  cp /shared-app/public/api/index.php /shared-app/public/api/index.php.backup\n\n  # Fix the relative path issue by using absolute path\n  sed -i \"s|require 'config/application.config.php'|require __DIR__ . '/../../config/application.config.php'|g\" /shared-app/public/api/index.php\n\n  echo \"Config path fixed in API index.php\"\n  echo \"Checking the fix:\"\n  grep -n \"require.*config.*application.config.php\" /shared-app/public/api/index.php || echo \"Pattern not found after fix\"\nelse\n  echo \"Warning: /shared-app/public/api/index.php not found\"\n  echo \"Available files in /shared-app/public/:\"\n  ls -la /shared-app/public/ 2\u003e/dev/null || echo \"No public directory\"\nfi\n\n# Create PHP-FPM compatibility polyfill for apache_request_headers()\necho \"Creating PHP-FPM compatibility polyfill...\"\ncat \u003e /shared-app/php_fpm_polyfill.php \u003c\u003c 'POLYFILL_EOF'\n\u003c?php\n/**\n * PHP-FPM Compatibility Polyfill for Apache functions\n * This file provides missing Apache functions for PHP-FPM + nginx environments\n *\n * NOTE: Using forced function definition without conditional checks\n * because function_exists() may return false positives in some PHP-FPM environments\n */\n\n/**\n * Polyfill for apache_request_headers() function in PHP-FPM environment\n * Only define if the function doesn't already exist\n */\nif (!function_exists('apache_request_headers')) {\n    function apache_request_headers() {\n        $headers = array();\n\n        // Get all HTTP headers from $_SERVER superglobal\n        foreach ($_SERVER as $key =\u003e $value) {\n            if (strpos($key, 'HTTP_') === 0) {\n                // Convert HTTP_HEADER_NAME to Header-Name format\n                $header = str_replace('_', '-', substr($key, 5));\n                $header = ucwords(strtolower($header), '-');\n                $headers[$header] = $value;\n            }\n        }\n\n        // Add special headers that might not have HTTP_ prefix\n        if (isset($_SERVER['CONTENT_TYPE'])) {\n            $headers['Content-Type'] = $_SERVER['CONTENT_TYPE'];\n        }\n        if (isset($_SERVER['CONTENT_LENGTH'])) {\n            $headers['Content-Length'] = $_SERVER['CONTENT_LENGTH'];\n        }\n\n        return $headers;\n    }\n}\n\n/**\n * Polyfill for apache_response_headers() function in PHP-FPM environment\n * @return array\n */\nfunction apache_response_headers() {\n    $headers = array();\n\n    // Get headers that were set with header() function\n    if (function_exists('headers_list')) {\n        foreach (headers_list() as $header) {\n            $parts = explode(':', $header, 2);\n            if (count($parts) === 2) {\n                $headers[trim($parts[0])] = trim($parts[1]);\n            }\n        }\n    }\n\n    return $headers;\n}\n\n/**\n * Alias for apache_request_headers() - commonly used alternative\n * Only define if the function doesn't already exist\n */\nif (!function_exists('getallheaders')) {\n    function getallheaders() {\n        return apache_request_headers();\n    }\n}\nPOLYFILL_EOF\n\necho \"✅ PHP-FPM polyfill created at /shared-app/php_fpm_polyfill.php\"\n\n# Inject the polyfill into the main bootstrap file\nif [ -f \"/shared-app/bootstrap.php\" ]; then\n  echo \"Injecting PHP-FPM polyfill into bootstrap.php...\"\n\n  # Create backup\n  cp /shared-app/bootstrap.php /shared-app/bootstrap.php.backup\n\n  # Check if the file already starts with \u003c?php\n  if head -1 /shared-app/bootstrap.php | grep -q \"^\u003c?php\"; then\n    # File starts with \u003c?php, inject after the opening tag\n    sed -i '1a require_once __DIR__ . \"/php_fpm_polyfill.php\";' /shared-app/bootstrap.php\n  else\n    # File doesn't start with \u003c?php, add both\n    sed -i '1i\u003c?php require_once __DIR__ . \"/php_fpm_polyfill.php\";' /shared-app/bootstrap.php\n  fi\n\n  echo \"✅ PHP-FPM polyfill injected into bootstrap.php\"\nelse\n  echo \"Warning: /shared-app/bootstrap.php not found\"\nfi\n\n# Also inject into the API index.php as a fallback\nif [ -f \"/shared-app/public/api/index.php\" ]; then\n  echo \"Injecting PHP-FPM polyfill into API index.php as fallback...\"\n\n  # Check if the file already starts with \u003c?php\n  if head -1 /shared-app/public/api/index.php | grep -q \"^\u003c?php\"; then\n    # File starts with \u003c?php, inject after the opening tag\n    sed -i '1a require_once __DIR__ . \"/../../php_fpm_polyfill.php\";' /shared-app/public/api/index.php\n  else\n    # File doesn't start with \u003c?php, add both\n    sed -i '1i\u003c?php require_once __DIR__ . \"/../../php_fpm_polyfill.php\";' /shared-app/public/api/index.php\n  fi\n\n  echo \"✅ PHP-FPM polyfill injected into API index.php\"\nfi\n\n# Verify config file exists\nif [ -f \"/shared-app/config/application.config.php\" ]; then\n  echo \"✅ Config file exists at /shared-app/config/application.config.php\"\nelse\n  echo \"❌ Config file missing at /shared-app/config/application.config.php\"\n  echo \"Available files in /shared-app/config/:\"\n  ls -la /shared-app/config/ 2\u003e/dev/null || echo \"No config directory\"\nfi\n\necho \"✅ PHP config path fix and PHP-FPM compatibility setup completed\"\n"],"image":"545009857703.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:2.0.56-test","name":"php-config-fixer","volumeMounts":[{"mountPath":"/shared-app","name":"shared-app"}]},{"command":["sh","-c","echo \"Validating PHP environment for PHP-FPM + nginx compatibility...\"\n\n# Test if polyfill was loaded correctly\nphp -r \"\nrequire_once '/shared-app/php_fpm_polyfill.php';\n\necho 'Testing PHP-FPM polyfill functions...\\n';\n\n// Test apache_request_headers function\nif (function_exists('apache_request_headers')) {\n    echo '✅ apache_request_headers() function is available\\n';\n} else {\n    echo '❌ apache_request_headers() function is missing\\n';\n    exit(1);\n}\n\n// Test getallheaders function\nif (function_exists('getallheaders')) {\n    echo '✅ getallheaders() function is available\\n';\n} else {\n    echo '❌ getallheaders() function is missing\\n';\n    exit(1);\n}\n\n// Test apache_response_headers function\nif (function_exists('apache_response_headers')) {\n    echo '✅ apache_response_headers() function is available\\n';\n} else {\n    echo '❌ apache_response_headers() function is missing\\n';\n    exit(1);\n}\n\necho '✅ All PHP-FPM polyfill functions are working correctly\\n';\n\"\n\n# Test basic PHP application loading\necho \"Testing PHP application bootstrap loading...\"\nphp -d display_errors=1 -d error_reporting=E_ALL -r \"\ntry {\n    require '/shared-app/bootstrap.php';\n    echo '✅ Bootstrap loaded successfully\\n';\n} catch (Exception \\$e) {\n    echo '❌ Bootstrap loading failed: ' . \\$e-\u003egetMessage() . '\\n';\n    echo 'File: ' . \\$e-\u003egetFile() . '\\n';\n    echo 'Line: ' . \\$e-\u003egetLine() . '\\n';\n    exit(1);\n}\n\"\n\n# Test Laminas application config loading\necho \"Testing Laminas application config loading...\"\nphp -d display_errors=1 -d error_reporting=E_ALL -r \"\ntry {\n    require '/shared-app/bootstrap.php';\n    \\$config = require '/shared-app/config/application.config.php';\n    echo '✅ Application config loaded successfully\\n';\n    echo 'Config modules: ' . count(\\$config['modules']) . '\\n';\n} catch (Exception \\$e) {\n    echo '❌ Application config loading failed: ' . \\$e-\u003egetMessage() . '\\n';\n    echo 'File: ' . \\$e-\u003egetFile() . '\\n';\n    echo 'Line: ' . \\$e-\u003egetLine() . '\\n';\n    exit(1);\n}\n\"\n\necho \"✅ PHP environment validation completed successfully\"\n"],"image":"545009857703.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:2.0.56-test","name":"php-env-validator","volumeMounts":[{"mountPath":"/shared-app","name":"shared-app"}]},{"command":["sh","-c","echo \"Creating HTTP nginx configuration for port 8080...\"\ncat \u003e /nginx-config/default.conf \u003c\u003c 'EOF'\nserver {\n    listen 8080;\n    server_name localhost;\n    root /shared-app/public;\n    index index.php index.html index.htm;\n\n    client_max_body_size 500m;\n\n    # Health check endpoint for Kubernetes probes\n    location = /api/health {\n        access_log off;\n        return 200 \"healthy\\n\";\n        add_header Content-Type text/plain;\n    }\n\n    # Root endpoint - redirect to API\n    location = / {\n        return 302 /api/;\n    }\n\n    location / {\n        try_files $uri $uri/ /api/index.php?$args;\n    }\n\n    location ~ \\.php$ {\n        fastcgi_pass localhost:9000;\n        fastcgi_index index.php;\n        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;\n        fastcgi_param AUTOMATED_TEST 1;\n        include fastcgi_params;\n        fastcgi_read_timeout 310;\n    }\n\n    location ~ /\\.ht {\n        deny all;\n    }\n}\nEOF\necho \"HTTP nginx configuration created successfully\"\n"],"image":"busybox:latest","name":"nginx-config-setup","volumeMounts":[{"mountPath":"/nginx-config","name":"nginx-config"}]}],"volumes":[{"emptyDir":{},"name":"ssl-certs"},{"emptyDir":{},"name":"shared-app"},{"emptyDir":{},"name":"nginx-config"}]}}}}
    creationTimestamp: "2025-07-11T21:09:41Z"
    generation: 1
    labels:
      app: optimized-demo-backend
      component: backend
      tenant: optimized-demo
    name: optimized-demo-backend
    namespace: tenant-optimized-demo
    resourceVersion: "24427252"
    uid: 44738779-3fe1-4f08-8e5b-2528da4c4278
  spec:
    progressDeadlineSeconds: 600
    replicas: 1
    revisionHistoryLimit: 10
    selector:
      matchLabels:
        app: optimized-demo-backend
        component: backend
        tenant: optimized-demo
    strategy:
      rollingUpdate:
        maxSurge: 25%
        maxUnavailable: 25%
      type: RollingUpdate
    template:
      metadata:
        creationTimestamp: null
        labels:
          app: optimized-demo-backend
          component: backend
          tenant: optimized-demo
      spec:
        containers:
        - env:
          - name: TENANT_ID
            value: optimized-demo
          - name: DB_HOST
            value: production-aurora-serverless.cluster-cpmagwki2kv8.eu-central-1.rds.amazonaws.com
          - name: DB_PORT
            value: "3306"
          - name: DB_NAME
            value: architrave
          - name: DB_USER
            valueFrom:
              secretKeyRef:
                key: DB_USER
                name: optimized-demo-db-secret
          - name: DB_PASSWORD
            valueFrom:
              secretKeyRef:
                key: DB_PASSWORD
                name: optimized-demo-db-secret
          - name: DB_SSL
            valueFrom:
              secretKeyRef:
                key: DB_SSL
                name: optimized-demo-db-secret
          - name: DB_SSL_CA
            valueFrom:
              secretKeyRef:
                key: DB_SSL_CA
                name: optimized-demo-db-secret
          - name: DB_SSL_VERIFY
            valueFrom:
              secretKeyRef:
                key: DB_SSL_VERIFY
                name: optimized-demo-db-secret
          - name: RABBITMQ_URL
            value: amqp://guest:guest@optimized-demo-rabbitmq-service:5672/
          - name: ENVIRONMENT
            value: production
          - name: LANGUAGE
            value: en
          image: 545009857703.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:2.0.56-test
          imagePullPolicy: IfNotPresent
          livenessProbe:
            failureThreshold: 3
            initialDelaySeconds: 60
            periodSeconds: 30
            successThreshold: 1
            tcpSocket:
              port: 9000
            timeoutSeconds: 1
          name: backend
          ports:
          - containerPort: 9000
            protocol: TCP
          readinessProbe:
            failureThreshold: 3
            initialDelaySeconds: 30
            periodSeconds: 30
            successThreshold: 1
            tcpSocket:
              port: 9000
            timeoutSeconds: 1
          resources:
            limits:
              cpu: 500m
              memory: 512Mi
            requests:
              cpu: 100m
              memory: 256Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /tmp
            name: ssl-certs
          - mountPath: /shared-app
            name: shared-app
        - image: nginx:1.21-alpine
          imagePullPolicy: IfNotPresent
          livenessProbe:
            failureThreshold: 5
            httpGet:
              httpHeaders:
              - name: User-Agent
                value: k8s-liveness-probe
              path: /api/health
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 90
            periodSeconds: 60
            successThreshold: 1
            timeoutSeconds: 10
          name: nginx
          ports:
          - containerPort: 8080
            protocol: TCP
          readinessProbe:
            failureThreshold: 10
            httpGet:
              httpHeaders:
              - name: User-Agent
                value: k8s-readiness-probe
              path: /api/health
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 60
            periodSeconds: 30
            successThreshold: 1
            timeoutSeconds: 5
          resources:
            limits:
              cpu: 200m
              memory: 128Mi
            requests:
              cpu: 50m
              memory: 64Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
          - mountPath: /etc/nginx/conf.d
            name: nginx-config
        dnsPolicy: ClusterFirst
        initContainers:
        - command:
          - sh
          - -c
          - |
            echo "Downloading RDS CA certificate..."
            curl -o /tmp/rds-ca-2019-root.pem https://s3.amazonaws.com/rds-downloads/rds-ca-2019-root.pem
            echo "SSL certificate downloaded successfully"
          image: curlimages/curl:latest
          imagePullPolicy: Always
          name: ssl-cert-downloader
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /tmp
            name: ssl-certs
        - command:
          - sh
          - -c
          - |
            echo "Copying application files from /storage/ArchAssets to shared volume..."
            if [ -d "/storage/ArchAssets" ]; then
                cp -r /storage/ArchAssets/* /shared-app/ 2>/dev/null || true
                echo "Application files copied successfully from /storage/ArchAssets"
                echo "Copied files:"
                ls -la /shared-app/ | head -10
            else
                echo "Warning: /storage/ArchAssets directory not found in webapp_dev image"
                echo "Available directories in /storage:"
                ls -la /storage/ 2>/dev/null || echo "No /storage directory"
                echo "Available directories in root:"
                ls -la / | grep -E "(var|storage|app)" || echo "No relevant directories found"
            fi
            echo "Application files copy process completed"
          image: 545009857703.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:2.0.56-test
          imagePullPolicy: IfNotPresent
          name: app-files-copier
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
        - command:
          - sh
          - -c
          - |
            echo "Fixing PHP application config path issues and PHP-FPM compatibility..."

            # Fix the relative path issue in /shared-app/public/api/index.php
            if [ -f "/shared-app/public/api/index.php" ]; then
              echo "Found API index.php, fixing config path..."

              # Create backup
              cp /shared-app/public/api/index.php /shared-app/public/api/index.php.backup

              # Fix the relative path issue by using absolute path
              sed -i "s|require 'config/application.config.php'|require __DIR__ . '/../../config/application.config.php'|g" /shared-app/public/api/index.php

              echo "Config path fixed in API index.php"
              echo "Checking the fix:"
              grep -n "require.*config.*application.config.php" /shared-app/public/api/index.php || echo "Pattern not found after fix"
            else
              echo "Warning: /shared-app/public/api/index.php not found"
              echo "Available files in /shared-app/public/:"
              ls -la /shared-app/public/ 2>/dev/null || echo "No public directory"
            fi

            # Create PHP-FPM compatibility polyfill for apache_request_headers()
            echo "Creating PHP-FPM compatibility polyfill..."
            cat > /shared-app/php_fpm_polyfill.php << 'POLYFILL_EOF'
            <?php
            /**
             * PHP-FPM Compatibility Polyfill for Apache functions
             * This file provides missing Apache functions for PHP-FPM + nginx environments
             *
             * NOTE: Using forced function definition without conditional checks
             * because function_exists() may return false positives in some PHP-FPM environments
             */

            /**
             * Polyfill for apache_request_headers() function in PHP-FPM environment
             * Only define if the function doesn't already exist
             */
            if (!function_exists('apache_request_headers')) {
                function apache_request_headers() {
                    $headers = array();

                    // Get all HTTP headers from $_SERVER superglobal
                    foreach ($_SERVER as $key => $value) {
                        if (strpos($key, 'HTTP_') === 0) {
                            // Convert HTTP_HEADER_NAME to Header-Name format
                            $header = str_replace('_', '-', substr($key, 5));
                            $header = ucwords(strtolower($header), '-');
                            $headers[$header] = $value;
                        }
                    }

                    // Add special headers that might not have HTTP_ prefix
                    if (isset($_SERVER['CONTENT_TYPE'])) {
                        $headers['Content-Type'] = $_SERVER['CONTENT_TYPE'];
                    }
                    if (isset($_SERVER['CONTENT_LENGTH'])) {
                        $headers['Content-Length'] = $_SERVER['CONTENT_LENGTH'];
                    }

                    return $headers;
                }
            }

            /**
             * Polyfill for apache_response_headers() function in PHP-FPM environment
             * @return array
             */
            function apache_response_headers() {
                $headers = array();

                // Get headers that were set with header() function
                if (function_exists('headers_list')) {
                    foreach (headers_list() as $header) {
                        $parts = explode(':', $header, 2);
                        if (count($parts) === 2) {
                            $headers[trim($parts[0])] = trim($parts[1]);
                        }
                    }
                }

                return $headers;
            }

            /**
             * Alias for apache_request_headers() - commonly used alternative
             * Only define if the function doesn't already exist
             */
            if (!function_exists('getallheaders')) {
                function getallheaders() {
                    return apache_request_headers();
                }
            }
            POLYFILL_EOF

            echo "✅ PHP-FPM polyfill created at /shared-app/php_fpm_polyfill.php"

            # Inject the polyfill into the main bootstrap file
            if [ -f "/shared-app/bootstrap.php" ]; then
              echo "Injecting PHP-FPM polyfill into bootstrap.php..."

              # Create backup
              cp /shared-app/bootstrap.php /shared-app/bootstrap.php.backup

              # Check if the file already starts with <?php
              if head -1 /shared-app/bootstrap.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/php_fpm_polyfill.php";' /shared-app/bootstrap.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/php_fpm_polyfill.php";' /shared-app/bootstrap.php
              fi

              echo "✅ PHP-FPM polyfill injected into bootstrap.php"
            else
              echo "Warning: /shared-app/bootstrap.php not found"
            fi

            # Also inject into the API index.php as a fallback
            if [ -f "/shared-app/public/api/index.php" ]; then
              echo "Injecting PHP-FPM polyfill into API index.php as fallback..."

              # Check if the file already starts with <?php
              if head -1 /shared-app/public/api/index.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/../../php_fpm_polyfill.php";' /shared-app/public/api/index.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/../../php_fpm_polyfill.php";' /shared-app/public/api/index.php
              fi

              echo "✅ PHP-FPM polyfill injected into API index.php"
            fi

            # Verify config file exists
            if [ -f "/shared-app/config/application.config.php" ]; then
              echo "✅ Config file exists at /shared-app/config/application.config.php"
            else
              echo "❌ Config file missing at /shared-app/config/application.config.php"
              echo "Available files in /shared-app/config/:"
              ls -la /shared-app/config/ 2>/dev/null || echo "No config directory"
            fi

            echo "✅ PHP config path fix and PHP-FPM compatibility setup completed"
          image: 545009857703.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:2.0.56-test
          imagePullPolicy: IfNotPresent
          name: php-config-fixer
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
        - command:
          - sh
          - -c
          - |
            echo "Validating PHP environment for PHP-FPM + nginx compatibility..."

            # Test if polyfill was loaded correctly
            php -r "
            require_once '/shared-app/php_fpm_polyfill.php';

            echo 'Testing PHP-FPM polyfill functions...\n';

            // Test apache_request_headers function
            if (function_exists('apache_request_headers')) {
                echo '✅ apache_request_headers() function is available\n';
            } else {
                echo '❌ apache_request_headers() function is missing\n';
                exit(1);
            }

            // Test getallheaders function
            if (function_exists('getallheaders')) {
                echo '✅ getallheaders() function is available\n';
            } else {
                echo '❌ getallheaders() function is missing\n';
                exit(1);
            }

            // Test apache_response_headers function
            if (function_exists('apache_response_headers')) {
                echo '✅ apache_response_headers() function is available\n';
            } else {
                echo '❌ apache_response_headers() function is missing\n';
                exit(1);
            }

            echo '✅ All PHP-FPM polyfill functions are working correctly\n';
            "

            # Test basic PHP application loading
            echo "Testing PHP application bootstrap loading..."
            php -d display_errors=1 -d error_reporting=E_ALL -r "
            try {
                require '/shared-app/bootstrap.php';
                echo '✅ Bootstrap loaded successfully\n';
            } catch (Exception \$e) {
                echo '❌ Bootstrap loading failed: ' . \$e->getMessage() . '\n';
                echo 'File: ' . \$e->getFile() . '\n';
                echo 'Line: ' . \$e->getLine() . '\n';
                exit(1);
            }
            "

            # Test Laminas application config loading
            echo "Testing Laminas application config loading..."
            php -d display_errors=1 -d error_reporting=E_ALL -r "
            try {
                require '/shared-app/bootstrap.php';
                \$config = require '/shared-app/config/application.config.php';
                echo '✅ Application config loaded successfully\n';
                echo 'Config modules: ' . count(\$config['modules']) . '\n';
            } catch (Exception \$e) {
                echo '❌ Application config loading failed: ' . \$e->getMessage() . '\n';
                echo 'File: ' . \$e->getFile() . '\n';
                echo 'Line: ' . \$e->getLine() . '\n';
                exit(1);
            }
            "

            echo "✅ PHP environment validation completed successfully"
          image: 545009857703.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:2.0.56-test
          imagePullPolicy: IfNotPresent
          name: php-env-validator
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
        - command:
          - sh
          - -c
          - |
            echo "Creating HTTP nginx configuration for port 8080..."
            cat > /nginx-config/default.conf << 'EOF'
            server {
                listen 8080;
                server_name localhost;
                root /shared-app/public;
                index index.php index.html index.htm;

                client_max_body_size 500m;

                # Health check endpoint for Kubernetes probes
                location = /api/health {
                    access_log off;
                    return 200 "healthy\n";
                    add_header Content-Type text/plain;
                }

                # Root endpoint - redirect to API
                location = / {
                    return 302 /api/;
                }

                location / {
                    try_files $uri $uri/ /api/index.php?$args;
                }

                location ~ \.php$ {
                    fastcgi_pass localhost:9000;
                    fastcgi_index index.php;
                    fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
                    fastcgi_param AUTOMATED_TEST 1;
                    include fastcgi_params;
                    fastcgi_read_timeout 310;
                }

                location ~ /\.ht {
                    deny all;
                }
            }
            EOF
            echo "HTTP nginx configuration created successfully"
          image: busybox:latest
          imagePullPolicy: Always
          name: nginx-config-setup
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /nginx-config
            name: nginx-config
        restartPolicy: Always
        schedulerName: default-scheduler
        securityContext: {}
        terminationGracePeriodSeconds: 30
        volumes:
        - emptyDir: {}
          name: ssl-certs
        - emptyDir: {}
          name: shared-app
        - emptyDir: {}
          name: nginx-config
  status:
    conditions:
    - lastTransitionTime: "2025-07-11T21:09:41Z"
      lastUpdateTime: "2025-07-11T21:09:41Z"
      message: Deployment does not have minimum availability.
      reason: MinimumReplicasUnavailable
      status: "False"
      type: Available
    - lastTransitionTime: "2025-07-11T21:09:41Z"
      lastUpdateTime: "2025-07-11T21:09:41Z"
      message: ReplicaSet "optimized-demo-backend-5bb694759f" is progressing.
      reason: ReplicaSetUpdated
      status: "True"
      type: Progressing
    observedGeneration: 1
    replicas: 1
    unavailableReplicas: 1
    updatedReplicas: 1
kind: List
metadata:
  resourceVersion: ""
