apiVersion: v1
items:
- apiVersion: v1
  kind: Node
  metadata:
    annotations:
      alpha.kubernetes.io/provided-node-ip: ***********
      csi.volume.kubernetes.io/nodeid: '{"ebs.csi.aws.com":"i-060fe6014133c3a27","s3.csi.aws.com":"ip-10-0-10-100.eu-central-1.compute.internal"}'
      node.alpha.kubernetes.io/ttl: "0"
      volumes.kubernetes.io/controller-managed-attach-detach: "true"
    creationTimestamp: "2025-07-11T15:21:31Z"
    labels:
      beta.kubernetes.io/arch: amd64
      beta.kubernetes.io/instance-type: t3a.large
      beta.kubernetes.io/os: linux
      eks.amazonaws.com/capacityType: SPOT
      eks.amazonaws.com/nodegroup: production-wks-spot-node-group
      eks.amazonaws.com/nodegroup-image: ami-0b4892d01b0bc996c
      failure-domain.beta.kubernetes.io/region: eu-central-1
      failure-domain.beta.kubernetes.io/zone: eu-central-1a
      k8s.io/cloud-provider-aws: 923b45e46921a8260adf43a035942c66
      kubernetes.io/arch: amd64
      kubernetes.io/hostname: ip-10-0-10-100.eu-central-1.compute.internal
      kubernetes.io/os: linux
      node-type: spot
      node.kubernetes.io/instance-type: t3a.large
      topology.ebs.csi.aws.com/zone: eu-central-1a
      topology.k8s.aws/zone-id: euc1-az2
      topology.kubernetes.io/region: eu-central-1
      topology.kubernetes.io/zone: eu-central-1a
      workload: general
    name: ip-10-0-10-100.eu-central-1.compute.internal
    resourceVersion: "24427088"
    uid: 8cdf8388-de2f-424a-aa2c-f3219ed39320
  spec:
    providerID: aws:///eu-central-1a/i-060fe6014133c3a27
    taints:
    - effect: PreferNoSchedule
      key: DeletionCandidateOfClusterAutoscaler
      value: "**********"
  status:
    addresses:
    - address: ***********
      type: InternalIP
    - address: ip-10-0-10-100.eu-central-1.compute.internal
      type: InternalDNS
    - address: ip-10-0-10-100.eu-central-1.compute.internal
      type: Hostname
    allocatable:
      cpu: 1930m
      ephemeral-storage: "18242267924"
      hugepages-1Gi: "0"
      hugepages-2Mi: "0"
      memory: 7287692Ki
      pods: "35"
    capacity:
      cpu: "2"
      ephemeral-storage: 20959212Ki
      hugepages-1Gi: "0"
      hugepages-2Mi: "0"
      memory: 8045452Ki
      pods: "35"
    conditions:
    - lastHeartbeatTime: "2025-07-11T21:09:19Z"
      lastTransitionTime: "2025-07-11T15:21:30Z"
      message: kubelet has sufficient memory available
      reason: KubeletHasSufficientMemory
      status: "False"
      type: MemoryPressure
    - lastHeartbeatTime: "2025-07-11T21:09:19Z"
      lastTransitionTime: "2025-07-11T15:21:30Z"
      message: kubelet has no disk pressure
      reason: KubeletHasNoDiskPressure
      status: "False"
      type: DiskPressure
    - lastHeartbeatTime: "2025-07-11T21:09:19Z"
      lastTransitionTime: "2025-07-11T15:21:30Z"
      message: kubelet has sufficient PID available
      reason: KubeletHasSufficientPID
      status: "False"
      type: PIDPressure
    - lastHeartbeatTime: "2025-07-11T21:09:19Z"
      lastTransitionTime: "2025-07-11T15:21:48Z"
      message: kubelet is posting ready status
      reason: KubeletReady
      status: "True"
      type: Ready
    daemonEndpoints:
      kubeletEndpoint:
        Port: 10250
    images:
    - names:
      - docker.io/falcosecurity/falco-driver-loader@sha256:2a66353991bbe7199f4715e3c08d5bc0247a71d753913c6ca66b6cad9297fd6f
      - docker.io/falcosecurity/falco-driver-loader:0.41.1
      sizeBytes: 340843905
    - names:
      - docker.io/falcosecurity/falco@sha256:731c5b47e697c56749d97f1fb30399248e1019e6959b2a2db866a17af7af6395
      - docker.io/falcosecurity/falco:0.41.1
      sizeBytes: 80664481
    - names:
      - public.ecr.aws/mountpoint-s3-csi-driver/aws-mountpoint-s3-csi-driver@sha256:d56c640f4346487322d343355c17a040c20f7132bb91f3e292064e726774b81d
      - public.ecr.aws/mountpoint-s3-csi-driver/aws-mountpoint-s3-csi-driver:v1.10.0
      sizeBytes: 65449064
    - names:
      - ************.dkr.ecr.eu-central-1.amazonaws.com/amazon-k8s-cni-init@sha256:a6f314161aafa0ee8a3d7a6d452a6a707d0b21940f3fb3d493071fc283b64281
      - 066635153087.dkr.ecr.il-central-1.amazonaws.com/amazon-k8s-cni-init:v1.19.2
      - 066635153087.dkr.ecr.il-central-1.amazonaws.com/amazon-k8s-cni-init:v1.19.2-eksbuild.1
      - 121268973566.dkr.ecr.ap-southeast-7.amazonaws.com/amazon-k8s-cni-init:v1.19.2
      - 121268973566.dkr.ecr.ap-southeast-7.amazonaws.com/amazon-k8s-cni-init:v1.19.2-eksbuild.1
      sizeBytes: 62982129
    - names:
      - 066635153087.dkr.ecr.il-central-1.amazonaws.com/amazon-k8s-cni-init:v1.20.0-rc1-eksbuild.4
      - 121268973566.dkr.ecr.ap-southeast-7.amazonaws.com/amazon-k8s-cni-init:v1.20.0-rc1-eksbuild.4
      - 151610086707.dkr.ecr.ap-southeast-5.amazonaws.com/amazon-k8s-cni-init:v1.20.0-rc1-eksbuild.4
      - 296578399912.dkr.ecr.ap-southeast-3.amazonaws.com/amazon-k8s-cni-init:v1.20.0-rc1-eksbuild.4
      - 455263428931.dkr.ecr.eu-south-2.amazonaws.com/amazon-k8s-cni-init:v1.20.0-rc1-eksbuild.4
      sizeBytes: 62360666
    - names:
      - 066635153087.dkr.ecr.il-central-1.amazonaws.com/amazon-k8s-cni:v1.20.0-rc1-eksbuild.4
      - 121268973566.dkr.ecr.ap-southeast-7.amazonaws.com/amazon-k8s-cni:v1.20.0-rc1-eksbuild.4
      - 151610086707.dkr.ecr.ap-southeast-5.amazonaws.com/amazon-k8s-cni:v1.20.0-rc1-eksbuild.4
      - 296578399912.dkr.ecr.ap-southeast-3.amazonaws.com/amazon-k8s-cni:v1.20.0-rc1-eksbuild.4
      - 455263428931.dkr.ecr.eu-south-2.amazonaws.com/amazon-k8s-cni:v1.20.0-rc1-eksbuild.4
      sizeBytes: 49616023
    - names:
      - 066635153087.dkr.ecr.il-central-1.amazonaws.com/amazon-k8s-cni:v1.19.2
      - 066635153087.dkr.ecr.il-central-1.amazonaws.com/amazon-k8s-cni:v1.19.2-eksbuild.1
      - 121268973566.dkr.ecr.ap-southeast-7.amazonaws.com/amazon-k8s-cni:v1.19.2
      - 121268973566.dkr.ecr.ap-southeast-7.amazonaws.com/amazon-k8s-cni:v1.19.2-eksbuild.1
      - 151610086707.dkr.ecr.ap-southeast-5.amazonaws.com/amazon-k8s-cni:v1.19.2
      sizeBytes: 48787219
    - names:
      - 066635153087.dkr.ecr.il-central-1.amazonaws.com/eks/kube-proxy:v1.32.3-minimal-eksbuild.7
      - 121268973566.dkr.ecr.ap-southeast-7.amazonaws.com/eks/kube-proxy:v1.32.3-minimal-eksbuild.7
      - 151610086707.dkr.ecr.ap-southeast-5.amazonaws.com/eks/kube-proxy:v1.32.3-minimal-eksbuild.7
      - 296578399912.dkr.ecr.ap-southeast-3.amazonaws.com/eks/kube-proxy:v1.32.3-minimal-eksbuild.7
      - 455263428931.dkr.ecr.eu-south-2.amazonaws.com/eks/kube-proxy:v1.32.3-minimal-eksbuild.7
      sizeBytes: 47544153
    - names:
      - ************.dkr.ecr.eu-central-1.amazonaws.com/amazon/aws-network-policy-agent@sha256:8f53fe281da5074976526a28a13422134a23aeab33199c0947fa61b34086eac3
      - ************.dkr.ecr.eu-central-1.amazonaws.com/amazon/aws-network-policy-agent:v1.1.6-eksbuild.1
      sizeBytes: 40743598
    - names:
      - 066635153087.dkr.ecr.il-central-1.amazonaws.com/eks/kube-proxy:v1.32.0-minimal-eksbuild.2
      - 121268973566.dkr.ecr.ap-southeast-7.amazonaws.com/eks/kube-proxy:v1.32.0-minimal-eksbuild.2
      - 151610086707.dkr.ecr.ap-southeast-5.amazonaws.com/eks/kube-proxy:v1.32.0-minimal-eksbuild.2
      - 296578399912.dkr.ecr.ap-southeast-3.amazonaws.com/eks/kube-proxy:v1.32.0-minimal-eksbuild.2
      - 455263428931.dkr.ecr.eu-south-2.amazonaws.com/eks/kube-proxy:v1.32.0-minimal-eksbuild.2
      sizeBytes: 36604068
    - names:
      - docker.io/falcosecurity/falcoctl@sha256:8e02bfd0c44a954495a5c7f980693f603d47c1bec2e55c86319c55134b9a5b6e
      - docker.io/falcosecurity/falcoctl:0.11.2
      sizeBytes: 31964684
    - names:
      - public.ecr.aws/ebs-csi-driver/aws-ebs-csi-driver@sha256:2aa362f4c40746c4318938b2f3afc279aa799fb341025e1ddbd04a5893619d66
      - public.ecr.aws/ebs-csi-driver/aws-ebs-csi-driver:v1.28.0
      sizeBytes: 27275412
    - names:
      - public.ecr.aws/eks-distro/kubernetes-csi/livenessprobe@sha256:5ef330be579f9ec1c0e9fb84b346ae1cf1c0cffaadf11ed78951319a3b0946ed
      - public.ecr.aws/eks-distro/kubernetes-csi/livenessprobe:v2.12.0-eks-1-29-7
      sizeBytes: 8063647
    - names:
      - public.ecr.aws/eks-distro/kubernetes-csi/livenessprobe@sha256:0228897d2395dc2ab1ccf963f496135620037145f92642f4ca71dab0c5dbf027
      - public.ecr.aws/eks-distro/kubernetes-csi/livenessprobe:v2.12.0-eks-1-29-5
      sizeBytes: 8063540
    - names:
      - public.ecr.aws/eks-distro/kubernetes-csi/node-driver-registrar@sha256:a5264fe089aa754e60dbad6a8eee411c90ee241e5fe68dfad90246b2ceb15fe9
      - public.ecr.aws/eks-distro/kubernetes-csi/node-driver-registrar:v2.10.0-eks-1-29-7
      sizeBytes: 6779878
    - names:
      - public.ecr.aws/eks-distro/kubernetes-csi/node-driver-registrar@sha256:c4a8d8843a331be6a1117e683175e07733fbf149ef8bd4255b75e5fdf1736cff
      - public.ecr.aws/eks-distro/kubernetes-csi/node-driver-registrar:v2.10.0-eks-1-29-5
      sizeBytes: 6779439
    - names:
      - ************.dkr.ecr.eu-central-1.amazonaws.com/eks/pause@sha256:529cf6b1b6e5b76e901abc43aee825badbd93f9c5ee5f1e316d46a83abbce5a2
      - ************.dkr.ecr.eu-central-1.amazonaws.com/eks/pause:3.5
      sizeBytes: 298689
    nodeInfo:
      architecture: amd64
      bootID: 2106e691-5fcd-4dd2-a97e-6610116541df
      containerRuntimeVersion: containerd://1.7.27
      kernelVersion: 5.10.236-228.935.amzn2.x86_64
      kubeProxyVersion: v1.32.3-eks-473151a
      kubeletVersion: v1.32.3-eks-473151a
      machineID: ec235331b18c93112d4af9ddd93930ba
      operatingSystem: linux
      osImage: Amazon Linux 2
      systemUUID: ec235331-b18c-9311-2d4a-f9ddd93930ba
- apiVersion: v1
  kind: Node
  metadata:
    annotations:
      alpha.kubernetes.io/provided-node-ip: ***********
      csi.volume.kubernetes.io/nodeid: '{"ebs.csi.aws.com":"i-081668c317b069d00","s3.csi.aws.com":"ip-10-0-10-178.eu-central-1.compute.internal"}'
      node.alpha.kubernetes.io/ttl: "0"
      volumes.kubernetes.io/controller-managed-attach-detach: "true"
    creationTimestamp: "2025-07-09T14:44:37Z"
    labels:
      beta.kubernetes.io/arch: amd64
      beta.kubernetes.io/instance-type: t3a.large
      beta.kubernetes.io/os: linux
      eks.amazonaws.com/capacityType: ON_DEMAND
      eks.amazonaws.com/nodegroup: production-wks-node-group
      eks.amazonaws.com/nodegroup-image: ami-010ae8fe27713f296
      eks.amazonaws.com/sourceLaunchTemplateId: lt-02f84270ec98bd1c2
      eks.amazonaws.com/sourceLaunchTemplateVersion: "2"
      failure-domain.beta.kubernetes.io/region: eu-central-1
      failure-domain.beta.kubernetes.io/zone: eu-central-1a
      k8s.io/cloud-provider-aws: 923b45e46921a8260adf43a035942c66
      kubernetes.io/arch: amd64
      kubernetes.io/hostname: ip-10-0-10-178.eu-central-1.compute.internal
      kubernetes.io/os: linux
      node-type: on-demand
      node.kubernetes.io/instance-type: t3a.large
      topology.ebs.csi.aws.com/zone: eu-central-1a
      topology.k8s.aws/zone-id: euc1-az2
      topology.kubernetes.io/region: eu-central-1
      topology.kubernetes.io/zone: eu-central-1a
      workload: critical
    name: ip-10-0-10-178.eu-central-1.compute.internal
    resourceVersion: "24427486"
    uid: 5b115931-2dca-4b7d-8760-31529014cc36
  spec:
    providerID: aws:///eu-central-1a/i-081668c317b069d00
  status:
    addresses:
    - address: ***********
      type: InternalIP
    - address: ip-10-0-10-178.eu-central-1.compute.internal
      type: InternalDNS
    - address: ip-10-0-10-178.eu-central-1.compute.internal
      type: Hostname
    allocatable:
      cpu: 1930m
      ephemeral-storage: "**********"
      hugepages-1Gi: "0"
      hugepages-2Mi: "0"
      memory: 7287684Ki
      pods: "35"
    capacity:
      cpu: "2"
      ephemeral-storage: 10473452Ki
      hugepages-1Gi: "0"
      hugepages-2Mi: "0"
      memory: 8045444Ki
      pods: "35"
    conditions:
    - lastHeartbeatTime: "2025-07-11T21:10:08Z"
      lastTransitionTime: "2025-07-09T14:44:36Z"
      message: kubelet has sufficient memory available
      reason: KubeletHasSufficientMemory
      status: "False"
      type: MemoryPressure
    - lastHeartbeatTime: "2025-07-11T21:10:08Z"
      lastTransitionTime: "2025-07-09T15:16:07Z"
      message: kubelet has no disk pressure
      reason: KubeletHasNoDiskPressure
      status: "False"
      type: DiskPressure
    - lastHeartbeatTime: "2025-07-11T21:10:08Z"
      lastTransitionTime: "2025-07-09T14:44:36Z"
      message: kubelet has sufficient PID available
      reason: KubeletHasSufficientPID
      status: "False"
      type: PIDPressure
    - lastHeartbeatTime: "2025-07-11T21:10:08Z"
      lastTransitionTime: "2025-07-09T14:44:54Z"
      message: kubelet is posting ready status
      reason: KubeletReady
      status: "True"
      type: Ready
    daemonEndpoints:
      kubeletEndpoint:
        Port: 10250
    images:
    - names:
      - docker.io/falcosecurity/falco-driver-loader@sha256:2a66353991bbe7199f4715e3c08d5bc0247a71d753913c6ca66b6cad9297fd6f
      - docker.io/falcosecurity/falco-driver-loader:0.41.1
      sizeBytes: 340843905
    - names:
      - 545009857703.dkr.ecr.eu-central-1.amazonaws.com/nginx_dev@sha256:d1cfc0a8648d5525984ff0ef1ff56d5aa8382b75efc4578cf3906fdaa80deea6
      - 545009857703.dkr.ecr.eu-central-1.amazonaws.com/nginx_dev:1.0.7
      sizeBytes: 122920236
    - names:
      - docker.io/istio/proxyv2@sha256:*****************************8cad13a35afddb9139ff795e36237327137
      - docker.io/istio/proxyv2:1.20.0
      sizeBytes: 96408950
    - names:
      - 545009857703.dkr.ecr.eu-central-1.amazonaws.com/rabbitmq_dev@sha256:a40d91656430a34f8ee2dfbac222d974915ef9d448f7e99ecb4118218a8a84c0
      - 545009857703.dkr.ecr.eu-central-1.amazonaws.com/rabbitmq_dev:1.02
      sizeBytes: 84808465
    - names:
      - registry.k8s.io/autoscaling/cluster-autoscaler@sha256:6ef10d108e0e45ecd883e074682330bbd4a3403e767ad56804800f2f4ee816da
      - registry.k8s.io/autoscaling/cluster-autoscaler:v1.33.0
      sizeBytes: 84601149
    - names:
      - docker.io/falcosecurity/falco@sha256:731c5b47e697c56749d97f1fb30399248e1019e6959b2a2db866a17af7af6395
      - docker.io/falcosecurity/falco:0.41.1
      sizeBytes: 80664481
    - names:
      - ghcr.io/kedacore/keda@sha256:e733aa68f44b74de45af658117028aefe0b64b1bf7207be26c7ca8a4cfb4be83
      - ghcr.io/kedacore/keda:2.17.1
      sizeBytes: 80272675
    - names:
      - public.ecr.aws/mountpoint-s3-csi-driver/aws-mountpoint-s3-csi-driver@sha256:d56c640f4346487322d343355c17a040c20f7132bb91f3e292064e726774b81d
      - public.ecr.aws/mountpoint-s3-csi-driver/aws-mountpoint-s3-csi-driver:v1.10.0
      sizeBytes: 65449064
    - names:
      - ************.dkr.ecr.eu-central-1.amazonaws.com/amazon-k8s-cni-init@sha256:a6f314161aafa0ee8a3d7a6d452a6a707d0b21940f3fb3d493071fc283b64281
      - 066635153087.dkr.ecr.il-central-1.amazonaws.com/amazon-k8s-cni-init:v1.19.2
      - 066635153087.dkr.ecr.il-central-1.amazonaws.com/amazon-k8s-cni-init:v1.19.2-eksbuild.1
      - 121268973566.dkr.ecr.ap-southeast-7.amazonaws.com/amazon-k8s-cni-init:v1.19.2
      - 121268973566.dkr.ecr.ap-southeast-7.amazonaws.com/amazon-k8s-cni-init:v1.19.2-eksbuild.1
      sizeBytes: 62982129
    - names:
      - ghcr.io/kedacore/keda-metrics-apiserver@sha256:00ca8aa8a60bf7eac7b47e80ef17b7e9047b74ecdbfc144e15fab0b7773e5fd5
      - ghcr.io/kedacore/keda-metrics-apiserver:2.17.1
      sizeBytes: 58197607
    - names:
      - quay.io/kiali/kiali@sha256:a60413e4dbcbafde07c71fba39e71de954035f98edbd7fb6c7dfca67287b97fc
      - quay.io/kiali/kiali:v1.76
      sizeBytes: 50222987
    - names:
      - 066635153087.dkr.ecr.il-central-1.amazonaws.com/amazon-k8s-cni:v1.19.2
      - 066635153087.dkr.ecr.il-central-1.amazonaws.com/amazon-k8s-cni:v1.19.2-eksbuild.1
      - 121268973566.dkr.ecr.ap-southeast-7.amazonaws.com/amazon-k8s-cni:v1.19.2
      - 121268973566.dkr.ecr.ap-southeast-7.amazonaws.com/amazon-k8s-cni:v1.19.2-eksbuild.1
      - 151610086707.dkr.ecr.ap-southeast-5.amazonaws.com/amazon-k8s-cni:v1.19.2
      sizeBytes: 48787219
    - names:
      - docker.io/openpolicyagent/gatekeeper@sha256:940d47bd80306a9501981fbd0dbbd03a1683001ea31b68264b118798a7414048
      - docker.io/openpolicyagent/gatekeeper:v3.19.1
      sizeBytes: 46143117
    - names:
      - ************.dkr.ecr.eu-central-1.amazonaws.com/amazon/aws-network-policy-agent@sha256:8f53fe281da5074976526a28a13422134a23aeab33199c0947fa61b34086eac3
      - ************.dkr.ecr.eu-central-1.amazonaws.com/amazon/aws-network-policy-agent:v1.1.6-eksbuild.1
      sizeBytes: 40743598
    - names:
      - 066635153087.dkr.ecr.il-central-1.amazonaws.com/eks/kube-proxy:v1.32.0-minimal-eksbuild.2
      - 121268973566.dkr.ecr.ap-southeast-7.amazonaws.com/eks/kube-proxy:v1.32.0-minimal-eksbuild.2
      - 151610086707.dkr.ecr.ap-southeast-5.amazonaws.com/eks/kube-proxy:v1.32.0-minimal-eksbuild.2
      - 296578399912.dkr.ecr.ap-southeast-3.amazonaws.com/eks/kube-proxy:v1.32.0-minimal-eksbuild.2
      - 455263428931.dkr.ecr.eu-south-2.amazonaws.com/eks/kube-proxy:v1.32.0-minimal-eksbuild.2
      sizeBytes: 36604068
    - names:
      - public.ecr.aws/karpenter/controller@sha256:68db4f092cf9cc83f5ef9e2fbc5407c2cb682e81f64dfaa700a7602ede38b1cf
      sizeBytes: 36219978
    - names:
      - public.ecr.aws/karpenter/webhook@sha256:96a2d9b06d6bc5127801f358f74b1cf2d289b423a2e9ba40c573c0b14b17dafa
      sizeBytes: 35746922
    - names:
      - docker.io/falcosecurity/falcoctl@sha256:8e02bfd0c44a954495a5c7f980693f603d47c1bec2e55c86319c55134b9a5b6e
      - docker.io/falcosecurity/falcoctl:0.11.2
      sizeBytes: 31964684
    - names:
      - ghcr.io/kedacore/keda-admission-webhooks@sha256:6f053bf3a9cb64998cdbaca75326166ffac612ab4917a0020d0af998f27319f6
      - ghcr.io/kedacore/keda-admission-webhooks:2.17.1
      sizeBytes: 27867342
    - names:
      - public.ecr.aws/ebs-csi-driver/aws-ebs-csi-driver@sha256:2aa362f4c40746c4318938b2f3afc279aa799fb341025e1ddbd04a5893619d66
      - public.ecr.aws/ebs-csi-driver/aws-ebs-csi-driver:v1.28.0
      sizeBytes: 27275412
    - names:
      - public.ecr.aws/eks/aws-load-balancer-controller@sha256:524a9de32a2190b51594afe7ae8f87eed57a04c6ac278f956dc5c4cd7410ab94
      - public.ecr.aws/eks/aws-load-balancer-controller:v2.13.3
      sizeBytes: 26849039
    - names:
      - registry.k8s.io/autoscaling/vpa-recommender@sha256:bc0bd96faf0e4845afe653a2692cfcb34c47b7a9ec5d0ab330fec5160c21e963
      - registry.k8s.io/autoscaling/vpa-recommender:1.0.0
      sizeBytes: 25089851
    - names:
      - registry.k8s.io/autoscaling/vpa-admission-controller@sha256:4561b814cd7bec4b2280bbb9ecdc5d90373fc74638d005283a0c47ce072c06e7
      - registry.k8s.io/autoscaling/vpa-admission-controller:1.0.0
      sizeBytes: 24711813
    - names:
      - ************.dkr.ecr.eu-central-1.amazonaws.com/eks/coredns@sha256:f184e31683ba315cb284bb6b429d416ecee71126ee1d9035af8d15462064e0b8
      - ************.dkr.ecr.eu-central-1.amazonaws.com/eks/coredns:v1.11.4-eksbuild.2
      sizeBytes: 22037887
    - names:
      - us-docker.pkg.dev/fairwinds-ops/oss/goldilocks@sha256:28d33623c6374047c4df1a2c85f6738fe56047e7b2015d1bb244ab3656dd4006
      - us-docker.pkg.dev/fairwinds-ops/oss/goldilocks:v4.13.0
      sizeBytes: 16382165
    - names:
      - public.ecr.aws/eks-distro/kubernetes-csi/external-provisioner@sha256:2af11d993e8c140223ca9f0c48c0bfd9acba826fc02f94a66c5d4aa374bb6b3f
      - public.ecr.aws/eks-distro/kubernetes-csi/external-provisioner:v4.0.0-eks-1-29-5
      sizeBytes: 15528882
    - names:
      - public.ecr.aws/eks-distro/kubernetes-csi/external-resizer@sha256:3c16e8c595cd7634df6d029cc3cc1c0fbc4f138856b78ec7105af86d6150f456
      - public.ecr.aws/eks-distro/kubernetes-csi/external-resizer:v1.10.0-eks-1-29-5
      sizeBytes: 14888133
    - names:
      - public.ecr.aws/eks-distro/kubernetes-csi/external-snapshotter/csi-snapshotter@sha256:f71c925aa46c1a30d8ee2595b84252a6165743de61f2f3eb634d704cff8ecf26
      - public.ecr.aws/eks-distro/kubernetes-csi/external-snapshotter/csi-snapshotter:v7.0.0-eks-1-29-5
      sizeBytes: 14671966
    - names:
      - public.ecr.aws/eks-distro/kubernetes-csi/external-attacher@sha256:c5c06101bc2b9052cb528f65102a5d940d753bb310c7a50e4ec33ccd66eb77ce
      - public.ecr.aws/eks-distro/kubernetes-csi/external-attacher:v4.5.0-eks-1-29-5
      sizeBytes: 14593764
    - names:
      - docker.io/library/nginx@sha256:a74534e76ee1121d418fa7394ca930eb67440deda413848bc67c68138535b989
      - docker.io/library/nginx:1.21-alpine
      sizeBytes: 10170636
    - names:
      - public.ecr.aws/eks-distro/kubernetes-csi/livenessprobe@sha256:5ef330be579f9ec1c0e9fb84b346ae1cf1c0cffaadf11ed78951319a3b0946ed
      - public.ecr.aws/eks-distro/kubernetes-csi/livenessprobe:v2.12.0-eks-1-29-7
      sizeBytes: 8063647
    - names:
      - public.ecr.aws/eks-distro/kubernetes-csi/livenessprobe@sha256:0228897d2395dc2ab1ccf963f496135620037145f92642f4ca71dab0c5dbf027
      - public.ecr.aws/eks-distro/kubernetes-csi/livenessprobe:v2.12.0-eks-1-29-5
      sizeBytes: 8063540
    - names:
      - public.ecr.aws/eks-distro/kubernetes-csi/node-driver-registrar@sha256:a5264fe089aa754e60dbad6a8eee411c90ee241e5fe68dfad90246b2ceb15fe9
      - public.ecr.aws/eks-distro/kubernetes-csi/node-driver-registrar:v2.10.0-eks-1-29-7
      sizeBytes: 6779878
    - names:
      - public.ecr.aws/eks-distro/kubernetes-csi/node-driver-registrar@sha256:c4a8d8843a331be6a1117e683175e07733fbf149ef8bd4255b75e5fdf1736cff
      - public.ecr.aws/eks-distro/kubernetes-csi/node-driver-registrar:v2.10.0-eks-1-29-5
      sizeBytes: 6779439
    - names:
      - docker.io/library/busybox@sha256:f85340bf132ae937d2c2a763b8335c9bab35d6e8293f70f606b9c6178d84f42b
      - docker.io/library/busybox:latest
      sizeBytes: 2156518
    - names:
      - ************.dkr.ecr.eu-central-1.amazonaws.com/eks/pause@sha256:529cf6b1b6e5b76e901abc43aee825badbd93f9c5ee5f1e316d46a83abbce5a2
      - ************.dkr.ecr.eu-central-1.amazonaws.com/eks/pause:3.5
      sizeBytes: 298689
    nodeInfo:
      architecture: amd64
      bootID: c4c23e55-a7f9-4137-b607-aa5f5027cc4e
      containerRuntimeVersion: containerd://1.7.27
      kernelVersion: 5.10.238-231.953.amzn2.x86_64
      kubeProxyVersion: v1.32.3-eks-473151a
      kubeletVersion: v1.32.3-eks-473151a
      machineID: ec2355f3019de8641f5a1fdf2fb17812
      operatingSystem: linux
      osImage: Amazon Linux 2
      systemUUID: ec2355f3-019d-e864-1f5a-1fdf2fb17812
- apiVersion: v1
  kind: Node
  metadata:
    annotations:
      alpha.kubernetes.io/provided-node-ip: ***********
      csi.volume.kubernetes.io/nodeid: '{"ebs.csi.aws.com":"i-0e94a4bb2e7797d12","s3.csi.aws.com":"ip-10-0-11-124.eu-central-1.compute.internal"}'
      node.alpha.kubernetes.io/ttl: "0"
      volumes.kubernetes.io/controller-managed-attach-detach: "true"
    creationTimestamp: "2025-07-11T09:39:54Z"
    labels:
      beta.kubernetes.io/arch: amd64
      beta.kubernetes.io/instance-type: t3a.large
      beta.kubernetes.io/os: linux
      eks.amazonaws.com/capacityType: SPOT
      eks.amazonaws.com/nodegroup: production-wks-spot-node-group
      eks.amazonaws.com/nodegroup-image: ami-0b4892d01b0bc996c
      failure-domain.beta.kubernetes.io/region: eu-central-1
      failure-domain.beta.kubernetes.io/zone: eu-central-1b
      k8s.io/cloud-provider-aws: 923b45e46921a8260adf43a035942c66
      kubernetes.io/arch: amd64
      kubernetes.io/hostname: ip-10-0-11-124.eu-central-1.compute.internal
      kubernetes.io/os: linux
      node-type: spot
      node.kubernetes.io/instance-type: t3a.large
      topology.ebs.csi.aws.com/zone: eu-central-1b
      topology.k8s.aws/zone-id: euc1-az3
      topology.kubernetes.io/region: eu-central-1
      topology.kubernetes.io/zone: eu-central-1b
      workload: general
    name: ip-10-0-11-124.eu-central-1.compute.internal
    resourceVersion: "24427195"
    uid: 720d5341-2e2c-480e-8310-1cbbd168e4ae
  spec:
    providerID: aws:///eu-central-1b/i-0e94a4bb2e7797d12
    taints:
    - effect: PreferNoSchedule
      key: DeletionCandidateOfClusterAutoscaler
      value: "**********"
  status:
    addresses:
    - address: ***********
      type: InternalIP
    - address: ip-10-0-11-124.eu-central-1.compute.internal
      type: InternalDNS
    - address: ip-10-0-11-124.eu-central-1.compute.internal
      type: Hostname
    allocatable:
      cpu: 1930m
      ephemeral-storage: "18242267924"
      hugepages-1Gi: "0"
      hugepages-2Mi: "0"
      memory: 7287684Ki
      pods: "35"
    capacity:
      cpu: "2"
      ephemeral-storage: 20959212Ki
      hugepages-1Gi: "0"
      hugepages-2Mi: "0"
      memory: 8045444Ki
      pods: "35"
    conditions:
    - lastHeartbeatTime: "2025-07-11T21:09:34Z"
      lastTransitionTime: "2025-07-11T09:39:53Z"
      message: kubelet has sufficient memory available
      reason: KubeletHasSufficientMemory
      status: "False"
      type: MemoryPressure
    - lastHeartbeatTime: "2025-07-11T21:09:34Z"
      lastTransitionTime: "2025-07-11T09:39:53Z"
      message: kubelet has no disk pressure
      reason: KubeletHasNoDiskPressure
      status: "False"
      type: DiskPressure
    - lastHeartbeatTime: "2025-07-11T21:09:34Z"
      lastTransitionTime: "2025-07-11T09:39:53Z"
      message: kubelet has sufficient PID available
      reason: KubeletHasSufficientPID
      status: "False"
      type: PIDPressure
    - lastHeartbeatTime: "2025-07-11T21:09:34Z"
      lastTransitionTime: "2025-07-11T09:40:11Z"
      message: kubelet is posting ready status
      reason: KubeletReady
      status: "True"
      type: Ready
    daemonEndpoints:
      kubeletEndpoint:
        Port: 10250
    images:
    - names:
      - docker.io/falcosecurity/falco-driver-loader@sha256:2a66353991bbe7199f4715e3c08d5bc0247a71d753913c6ca66b6cad9297fd6f
      - docker.io/falcosecurity/falco-driver-loader:0.41.1
      sizeBytes: 340843905
    - names:
      - docker.io/falcosecurity/falco@sha256:731c5b47e697c56749d97f1fb30399248e1019e6959b2a2db866a17af7af6395
      - docker.io/falcosecurity/falco:0.41.1
      sizeBytes: 80664481
    - names:
      - public.ecr.aws/mountpoint-s3-csi-driver/aws-mountpoint-s3-csi-driver@sha256:d56c640f4346487322d343355c17a040c20f7132bb91f3e292064e726774b81d
      - public.ecr.aws/mountpoint-s3-csi-driver/aws-mountpoint-s3-csi-driver:v1.10.0
      sizeBytes: 65449064
    - names:
      - ************.dkr.ecr.eu-central-1.amazonaws.com/amazon-k8s-cni-init@sha256:a6f314161aafa0ee8a3d7a6d452a6a707d0b21940f3fb3d493071fc283b64281
      - 066635153087.dkr.ecr.il-central-1.amazonaws.com/amazon-k8s-cni-init:v1.19.2
      - 066635153087.dkr.ecr.il-central-1.amazonaws.com/amazon-k8s-cni-init:v1.19.2-eksbuild.1
      - 121268973566.dkr.ecr.ap-southeast-7.amazonaws.com/amazon-k8s-cni-init:v1.19.2
      - 121268973566.dkr.ecr.ap-southeast-7.amazonaws.com/amazon-k8s-cni-init:v1.19.2-eksbuild.1
      sizeBytes: 62982129
    - names:
      - 066635153087.dkr.ecr.il-central-1.amazonaws.com/amazon-k8s-cni-init:v1.20.0-rc1-eksbuild.4
      - 121268973566.dkr.ecr.ap-southeast-7.amazonaws.com/amazon-k8s-cni-init:v1.20.0-rc1-eksbuild.4
      - 151610086707.dkr.ecr.ap-southeast-5.amazonaws.com/amazon-k8s-cni-init:v1.20.0-rc1-eksbuild.4
      - 296578399912.dkr.ecr.ap-southeast-3.amazonaws.com/amazon-k8s-cni-init:v1.20.0-rc1-eksbuild.4
      - 455263428931.dkr.ecr.eu-south-2.amazonaws.com/amazon-k8s-cni-init:v1.20.0-rc1-eksbuild.4
      sizeBytes: 62360666
    - names:
      - 066635153087.dkr.ecr.il-central-1.amazonaws.com/amazon-k8s-cni:v1.20.0-rc1-eksbuild.4
      - 121268973566.dkr.ecr.ap-southeast-7.amazonaws.com/amazon-k8s-cni:v1.20.0-rc1-eksbuild.4
      - 151610086707.dkr.ecr.ap-southeast-5.amazonaws.com/amazon-k8s-cni:v1.20.0-rc1-eksbuild.4
      - 296578399912.dkr.ecr.ap-southeast-3.amazonaws.com/amazon-k8s-cni:v1.20.0-rc1-eksbuild.4
      - 455263428931.dkr.ecr.eu-south-2.amazonaws.com/amazon-k8s-cni:v1.20.0-rc1-eksbuild.4
      sizeBytes: 49616023
    - names:
      - 066635153087.dkr.ecr.il-central-1.amazonaws.com/amazon-k8s-cni:v1.19.2
      - 066635153087.dkr.ecr.il-central-1.amazonaws.com/amazon-k8s-cni:v1.19.2-eksbuild.1
      - 121268973566.dkr.ecr.ap-southeast-7.amazonaws.com/amazon-k8s-cni:v1.19.2
      - 121268973566.dkr.ecr.ap-southeast-7.amazonaws.com/amazon-k8s-cni:v1.19.2-eksbuild.1
      - 151610086707.dkr.ecr.ap-southeast-5.amazonaws.com/amazon-k8s-cni:v1.19.2
      sizeBytes: 48787219
    - names:
      - 066635153087.dkr.ecr.il-central-1.amazonaws.com/eks/kube-proxy:v1.32.3-minimal-eksbuild.7
      - 121268973566.dkr.ecr.ap-southeast-7.amazonaws.com/eks/kube-proxy:v1.32.3-minimal-eksbuild.7
      - 151610086707.dkr.ecr.ap-southeast-5.amazonaws.com/eks/kube-proxy:v1.32.3-minimal-eksbuild.7
      - 296578399912.dkr.ecr.ap-southeast-3.amazonaws.com/eks/kube-proxy:v1.32.3-minimal-eksbuild.7
      - 455263428931.dkr.ecr.eu-south-2.amazonaws.com/eks/kube-proxy:v1.32.3-minimal-eksbuild.7
      sizeBytes: 47544153
    - names:
      - ************.dkr.ecr.eu-central-1.amazonaws.com/amazon/aws-network-policy-agent@sha256:8f53fe281da5074976526a28a13422134a23aeab33199c0947fa61b34086eac3
      - ************.dkr.ecr.eu-central-1.amazonaws.com/amazon/aws-network-policy-agent:v1.1.6-eksbuild.1
      sizeBytes: 40743598
    - names:
      - 066635153087.dkr.ecr.il-central-1.amazonaws.com/eks/kube-proxy:v1.32.0-minimal-eksbuild.2
      - 121268973566.dkr.ecr.ap-southeast-7.amazonaws.com/eks/kube-proxy:v1.32.0-minimal-eksbuild.2
      - 151610086707.dkr.ecr.ap-southeast-5.amazonaws.com/eks/kube-proxy:v1.32.0-minimal-eksbuild.2
      - 296578399912.dkr.ecr.ap-southeast-3.amazonaws.com/eks/kube-proxy:v1.32.0-minimal-eksbuild.2
      - 455263428931.dkr.ecr.eu-south-2.amazonaws.com/eks/kube-proxy:v1.32.0-minimal-eksbuild.2
      sizeBytes: 36604068
    - names:
      - docker.io/falcosecurity/falcoctl@sha256:8e02bfd0c44a954495a5c7f980693f603d47c1bec2e55c86319c55134b9a5b6e
      - docker.io/falcosecurity/falcoctl:0.11.2
      sizeBytes: 31964684
    - names:
      - public.ecr.aws/ebs-csi-driver/aws-ebs-csi-driver@sha256:2aa362f4c40746c4318938b2f3afc279aa799fb341025e1ddbd04a5893619d66
      - public.ecr.aws/ebs-csi-driver/aws-ebs-csi-driver:v1.28.0
      sizeBytes: 27275412
    - names:
      - public.ecr.aws/eks-distro/kubernetes-csi/livenessprobe@sha256:5ef330be579f9ec1c0e9fb84b346ae1cf1c0cffaadf11ed78951319a3b0946ed
      - public.ecr.aws/eks-distro/kubernetes-csi/livenessprobe:v2.12.0-eks-1-29-7
      sizeBytes: 8063647
    - names:
      - public.ecr.aws/eks-distro/kubernetes-csi/livenessprobe@sha256:0228897d2395dc2ab1ccf963f496135620037145f92642f4ca71dab0c5dbf027
      - public.ecr.aws/eks-distro/kubernetes-csi/livenessprobe:v2.12.0-eks-1-29-5
      sizeBytes: 8063540
    - names:
      - public.ecr.aws/eks-distro/kubernetes-csi/node-driver-registrar@sha256:a5264fe089aa754e60dbad6a8eee411c90ee241e5fe68dfad90246b2ceb15fe9
      - public.ecr.aws/eks-distro/kubernetes-csi/node-driver-registrar:v2.10.0-eks-1-29-7
      sizeBytes: 6779878
    - names:
      - public.ecr.aws/eks-distro/kubernetes-csi/node-driver-registrar@sha256:c4a8d8843a331be6a1117e683175e07733fbf149ef8bd4255b75e5fdf1736cff
      - public.ecr.aws/eks-distro/kubernetes-csi/node-driver-registrar:v2.10.0-eks-1-29-5
      sizeBytes: 6779439
    - names:
      - ************.dkr.ecr.eu-central-1.amazonaws.com/eks/pause@sha256:529cf6b1b6e5b76e901abc43aee825badbd93f9c5ee5f1e316d46a83abbce5a2
      - ************.dkr.ecr.eu-central-1.amazonaws.com/eks/pause:3.5
      sizeBytes: 298689
    nodeInfo:
      architecture: amd64
      bootID: d5724965-4c22-4ebd-95ae-6cee4b5e6daf
      containerRuntimeVersion: containerd://1.7.27
      kernelVersion: 5.10.236-228.935.amzn2.x86_64
      kubeProxyVersion: v1.32.3-eks-473151a
      kubeletVersion: v1.32.3-eks-473151a
      machineID: ec21587f65c3a125f62733342c012856
      operatingSystem: linux
      osImage: Amazon Linux 2
      systemUUID: ec21587f-65c3-a125-f627-33342c012856
- apiVersion: v1
  kind: Node
  metadata:
    annotations:
      alpha.kubernetes.io/provided-node-ip: ***********
      csi.volume.kubernetes.io/nodeid: '{"ebs.csi.aws.com":"i-094881e30bdfd561f","s3.csi.aws.com":"ip-10-0-11-143.eu-central-1.compute.internal"}'
      node.alpha.kubernetes.io/ttl: "0"
      volumes.kubernetes.io/controller-managed-attach-detach: "true"
    creationTimestamp: "2025-07-09T12:19:44Z"
    labels:
      beta.kubernetes.io/arch: amd64
      beta.kubernetes.io/instance-type: t3a.large
      beta.kubernetes.io/os: linux
      eks.amazonaws.com/capacityType: ON_DEMAND
      eks.amazonaws.com/nodegroup: production-wks-node-group
      eks.amazonaws.com/nodegroup-image: ami-010ae8fe27713f296
      eks.amazonaws.com/sourceLaunchTemplateId: lt-02f84270ec98bd1c2
      eks.amazonaws.com/sourceLaunchTemplateVersion: "2"
      failure-domain.beta.kubernetes.io/region: eu-central-1
      failure-domain.beta.kubernetes.io/zone: eu-central-1b
      k8s.io/cloud-provider-aws: 923b45e46921a8260adf43a035942c66
      kubernetes.io/arch: amd64
      kubernetes.io/hostname: ip-10-0-11-143.eu-central-1.compute.internal
      kubernetes.io/os: linux
      node-type: on-demand
      node.kubernetes.io/instance-type: t3a.large
      topology.ebs.csi.aws.com/zone: eu-central-1b
      topology.k8s.aws/zone-id: euc1-az3
      topology.kubernetes.io/region: eu-central-1
      topology.kubernetes.io/zone: eu-central-1b
      workload: critical
    name: ip-10-0-11-143.eu-central-1.compute.internal
    resourceVersion: "24426631"
    uid: 4cd95a0b-2926-4b9a-aa88-2763fabe7dbb
  spec:
    providerID: aws:///eu-central-1b/i-094881e30bdfd561f
  status:
    addresses:
    - address: ***********
      type: InternalIP
    - address: ip-10-0-11-143.eu-central-1.compute.internal
      type: InternalDNS
    - address: ip-10-0-11-143.eu-central-1.compute.internal
      type: Hostname
    allocatable:
      cpu: 1930m
      ephemeral-storage: "**********"
      hugepages-1Gi: "0"
      hugepages-2Mi: "0"
      memory: 7287684Ki
      pods: "35"
    capacity:
      cpu: "2"
      ephemeral-storage: 10473452Ki
      hugepages-1Gi: "0"
      hugepages-2Mi: "0"
      memory: 8045444Ki
      pods: "35"
    conditions:
    - lastHeartbeatTime: "2025-07-11T21:08:18Z"
      lastTransitionTime: "2025-07-09T12:19:44Z"
      message: kubelet has sufficient memory available
      reason: KubeletHasSufficientMemory
      status: "False"
      type: MemoryPressure
    - lastHeartbeatTime: "2025-07-11T21:08:18Z"
      lastTransitionTime: "2025-07-09T15:43:17Z"
      message: kubelet has no disk pressure
      reason: KubeletHasNoDiskPressure
      status: "False"
      type: DiskPressure
    - lastHeartbeatTime: "2025-07-11T21:08:18Z"
      lastTransitionTime: "2025-07-09T12:19:44Z"
      message: kubelet has sufficient PID available
      reason: KubeletHasSufficientPID
      status: "False"
      type: PIDPressure
    - lastHeartbeatTime: "2025-07-11T21:08:18Z"
      lastTransitionTime: "2025-07-09T12:20:01Z"
      message: kubelet is posting ready status
      reason: KubeletReady
      status: "True"
      type: Ready
    daemonEndpoints:
      kubeletEndpoint:
        Port: 10250
    images:
    - names:
      - docker.io/falcosecurity/falco-driver-loader@sha256:2a66353991bbe7199f4715e3c08d5bc0247a71d753913c6ca66b6cad9297fd6f
      - docker.io/falcosecurity/falco-driver-loader:0.41.1
      sizeBytes: 340843905
    - names:
      - 545009857703.dkr.ecr.eu-central-1.amazonaws.com/nginx_dev@sha256:d1cfc0a8648d5525984ff0ef1ff56d5aa8382b75efc4578cf3906fdaa80deea6
      - 545009857703.dkr.ecr.eu-central-1.amazonaws.com/nginx_dev:1.0.7
      sizeBytes: 122920236
    - names:
      - docker.io/istio/proxyv2@sha256:*****************************8cad13a35afddb9139ff795e36237327137
      - docker.io/istio/proxyv2:1.20.0
      sizeBytes: 96408950
    - names:
      - 545009857703.dkr.ecr.eu-central-1.amazonaws.com/rabbitmq_dev@sha256:a40d91656430a34f8ee2dfbac222d974915ef9d448f7e99ecb4118218a8a84c0
      - 545009857703.dkr.ecr.eu-central-1.amazonaws.com/rabbitmq_dev:1.02
      sizeBytes: 84808465
    - names:
      - registry.k8s.io/autoscaling/cluster-autoscaler@sha256:6ef10d108e0e45ecd883e074682330bbd4a3403e767ad56804800f2f4ee816da
      - registry.k8s.io/autoscaling/cluster-autoscaler:v1.33.0
      sizeBytes: 84601149
    - names:
      - docker.io/falcosecurity/falco@sha256:731c5b47e697c56749d97f1fb30399248e1019e6959b2a2db866a17af7af6395
      - docker.io/falcosecurity/falco:0.41.1
      sizeBytes: 80664481
    - names:
      - public.ecr.aws/mountpoint-s3-csi-driver/aws-mountpoint-s3-csi-driver@sha256:d56c640f4346487322d343355c17a040c20f7132bb91f3e292064e726774b81d
      - public.ecr.aws/mountpoint-s3-csi-driver/aws-mountpoint-s3-csi-driver:v1.10.0
      sizeBytes: 65449064
    - names:
      - ************.dkr.ecr.eu-central-1.amazonaws.com/amazon-k8s-cni-init@sha256:a6f314161aafa0ee8a3d7a6d452a6a707d0b21940f3fb3d493071fc283b64281
      - 066635153087.dkr.ecr.il-central-1.amazonaws.com/amazon-k8s-cni-init:v1.19.2
      - 066635153087.dkr.ecr.il-central-1.amazonaws.com/amazon-k8s-cni-init:v1.19.2-eksbuild.1
      - 121268973566.dkr.ecr.ap-southeast-7.amazonaws.com/amazon-k8s-cni-init:v1.19.2
      - 121268973566.dkr.ecr.ap-southeast-7.amazonaws.com/amazon-k8s-cni-init:v1.19.2-eksbuild.1
      sizeBytes: 62982129
    - names:
      - 066635153087.dkr.ecr.il-central-1.amazonaws.com/amazon-k8s-cni:v1.19.2
      - 066635153087.dkr.ecr.il-central-1.amazonaws.com/amazon-k8s-cni:v1.19.2-eksbuild.1
      - 121268973566.dkr.ecr.ap-southeast-7.amazonaws.com/amazon-k8s-cni:v1.19.2
      - 121268973566.dkr.ecr.ap-southeast-7.amazonaws.com/amazon-k8s-cni:v1.19.2-eksbuild.1
      - 151610086707.dkr.ecr.ap-southeast-5.amazonaws.com/amazon-k8s-cni:v1.19.2
      sizeBytes: 48787219
    - names:
      - docker.io/openpolicyagent/gatekeeper@sha256:940d47bd80306a9501981fbd0dbbd03a1683001ea31b68264b118798a7414048
      - docker.io/openpolicyagent/gatekeeper:v3.19.1
      sizeBytes: 46143117
    - names:
      - ************.dkr.ecr.eu-central-1.amazonaws.com/amazon/aws-network-policy-agent@sha256:8f53fe281da5074976526a28a13422134a23aeab33199c0947fa61b34086eac3
      - ************.dkr.ecr.eu-central-1.amazonaws.com/amazon/aws-network-policy-agent:v1.1.6-eksbuild.1
      sizeBytes: 40743598
    - names:
      - 066635153087.dkr.ecr.il-central-1.amazonaws.com/eks/kube-proxy:v1.32.0-minimal-eksbuild.2
      - 121268973566.dkr.ecr.ap-southeast-7.amazonaws.com/eks/kube-proxy:v1.32.0-minimal-eksbuild.2
      - 151610086707.dkr.ecr.ap-southeast-5.amazonaws.com/eks/kube-proxy:v1.32.0-minimal-eksbuild.2
      - 296578399912.dkr.ecr.ap-southeast-3.amazonaws.com/eks/kube-proxy:v1.32.0-minimal-eksbuild.2
      - 455263428931.dkr.ecr.eu-south-2.amazonaws.com/eks/kube-proxy:v1.32.0-minimal-eksbuild.2
      sizeBytes: 36604068
    - names:
      - public.ecr.aws/karpenter/controller@sha256:68db4f092cf9cc83f5ef9e2fbc5407c2cb682e81f64dfaa700a7602ede38b1cf
      sizeBytes: 36219978
    - names:
      - public.ecr.aws/karpenter/webhook@sha256:96a2d9b06d6bc5127801f358f74b1cf2d289b423a2e9ba40c573c0b14b17dafa
      sizeBytes: 35746922
    - names:
      - docker.io/falcosecurity/falcoctl@sha256:8e02bfd0c44a954495a5c7f980693f603d47c1bec2e55c86319c55134b9a5b6e
      - docker.io/falcosecurity/falcoctl:0.11.2
      sizeBytes: 31964684
    - names:
      - public.ecr.aws/ebs-csi-driver/aws-ebs-csi-driver@sha256:2aa362f4c40746c4318938b2f3afc279aa799fb341025e1ddbd04a5893619d66
      - public.ecr.aws/ebs-csi-driver/aws-ebs-csi-driver:v1.28.0
      sizeBytes: 27275412
    - names:
      - public.ecr.aws/eks/aws-load-balancer-controller@sha256:524a9de32a2190b51594afe7ae8f87eed57a04c6ac278f956dc5c4cd7410ab94
      - public.ecr.aws/eks/aws-load-balancer-controller:v2.13.3
      sizeBytes: 26849039
    - names:
      - registry.k8s.io/autoscaling/vpa-recommender@sha256:bc0bd96faf0e4845afe653a2692cfcb34c47b7a9ec5d0ab330fec5160c21e963
      - registry.k8s.io/autoscaling/vpa-recommender:1.0.0
      sizeBytes: 25089851
    - names:
      - registry.k8s.io/autoscaling/vpa-admission-controller@sha256:4561b814cd7bec4b2280bbb9ecdc5d90373fc74638d005283a0c47ce072c06e7
      - registry.k8s.io/autoscaling/vpa-admission-controller:1.0.0
      sizeBytes: 24711813
    - names:
      - ************.dkr.ecr.eu-central-1.amazonaws.com/eks/coredns@sha256:f184e31683ba315cb284bb6b429d416ecee71126ee1d9035af8d15462064e0b8
      - ************.dkr.ecr.eu-central-1.amazonaws.com/eks/coredns:v1.11.4-eksbuild.2
      sizeBytes: 22037887
    - names:
      - registry.k8s.io/metrics-server/metrics-server@sha256:ffcb2bf004d6aa0a17d90e0247cf94f2865c8901dcab4427034c341951c239f9
      - registry.k8s.io/metrics-server/metrics-server:v0.7.2
      sizeBytes: 19494617
    - names:
      - public.ecr.aws/eks-distro/kubernetes-csi/external-provisioner@sha256:2af11d993e8c140223ca9f0c48c0bfd9acba826fc02f94a66c5d4aa374bb6b3f
      - public.ecr.aws/eks-distro/kubernetes-csi/external-provisioner:v4.0.0-eks-1-29-5
      sizeBytes: 15528882
    - names:
      - public.ecr.aws/eks-distro/kubernetes-csi/external-resizer@sha256:3c16e8c595cd7634df6d029cc3cc1c0fbc4f138856b78ec7105af86d6150f456
      - public.ecr.aws/eks-distro/kubernetes-csi/external-resizer:v1.10.0-eks-1-29-5
      sizeBytes: 14888133
    - names:
      - public.ecr.aws/eks-distro/kubernetes-csi/external-snapshotter/csi-snapshotter@sha256:f71c925aa46c1a30d8ee2595b84252a6165743de61f2f3eb634d704cff8ecf26
      - public.ecr.aws/eks-distro/kubernetes-csi/external-snapshotter/csi-snapshotter:v7.0.0-eks-1-29-5
      sizeBytes: 14671966
    - names:
      - public.ecr.aws/eks-distro/kubernetes-csi/external-attacher@sha256:c5c06101bc2b9052cb528f65102a5d940d753bb310c7a50e4ec33ccd66eb77ce
      - public.ecr.aws/eks-distro/kubernetes-csi/external-attacher:v4.5.0-eks-1-29-5
      sizeBytes: 14593764
    - names:
      - docker.io/library/nginx@sha256:a74534e76ee1121d418fa7394ca930eb67440deda413848bc67c68138535b989
      - docker.io/library/nginx:1.21-alpine
      sizeBytes: 10170636
    - names:
      - public.ecr.aws/eks-distro/kubernetes-csi/livenessprobe@sha256:5ef330be579f9ec1c0e9fb84b346ae1cf1c0cffaadf11ed78951319a3b0946ed
      - public.ecr.aws/eks-distro/kubernetes-csi/livenessprobe:v2.12.0-eks-1-29-7
      sizeBytes: 8063647
    - names:
      - public.ecr.aws/eks-distro/kubernetes-csi/livenessprobe@sha256:0228897d2395dc2ab1ccf963f496135620037145f92642f4ca71dab0c5dbf027
      - public.ecr.aws/eks-distro/kubernetes-csi/livenessprobe:v2.12.0-eks-1-29-5
      sizeBytes: 8063540
    - names:
      - public.ecr.aws/eks-distro/kubernetes-csi/node-driver-registrar@sha256:a5264fe089aa754e60dbad6a8eee411c90ee241e5fe68dfad90246b2ceb15fe9
      - public.ecr.aws/eks-distro/kubernetes-csi/node-driver-registrar:v2.10.0-eks-1-29-7
      sizeBytes: 6779878
    - names:
      - public.ecr.aws/eks-distro/kubernetes-csi/node-driver-registrar@sha256:c4a8d8843a331be6a1117e683175e07733fbf149ef8bd4255b75e5fdf1736cff
      - public.ecr.aws/eks-distro/kubernetes-csi/node-driver-registrar:v2.10.0-eks-1-29-5
      sizeBytes: 6779439
    - names:
      - docker.io/library/busybox@sha256:f85340bf132ae937d2c2a763b8335c9bab35d6e8293f70f606b9c6178d84f42b
      - docker.io/library/busybox:latest
      sizeBytes: 2156518
    - names:
      - ************.dkr.ecr.eu-central-1.amazonaws.com/eks/pause@sha256:529cf6b1b6e5b76e901abc43aee825badbd93f9c5ee5f1e316d46a83abbce5a2
      - ************.dkr.ecr.eu-central-1.amazonaws.com/eks/pause:3.5
      sizeBytes: 298689
    nodeInfo:
      architecture: amd64
      bootID: 9650f92e-1b81-4bf8-a202-63b831a941cc
      containerRuntimeVersion: containerd://1.7.27
      kernelVersion: 5.10.238-231.953.amzn2.x86_64
      kubeProxyVersion: v1.32.3-eks-473151a
      kubeletVersion: v1.32.3-eks-473151a
      machineID: ec21ad6f53e24456ce5b415e7aa8a616
      operatingSystem: linux
      osImage: Amazon Linux 2
      systemUUID: ec21ad6f-53e2-4456-ce5b-415e7aa8a616
- apiVersion: v1
  kind: Node
  metadata:
    annotations:
      alpha.kubernetes.io/provided-node-ip: ***********
      csi.volume.kubernetes.io/nodeid: '{"ebs.csi.aws.com":"i-042d9bf12c4fad70d","s3.csi.aws.com":"ip-10-0-11-248.eu-central-1.compute.internal"}'
      node.alpha.kubernetes.io/ttl: "0"
      volumes.kubernetes.io/controller-managed-attach-detach: "true"
    creationTimestamp: "2025-07-11T09:41:00Z"
    labels:
      beta.kubernetes.io/arch: amd64
      beta.kubernetes.io/instance-type: t3a.large
      beta.kubernetes.io/os: linux
      eks.amazonaws.com/capacityType: SPOT
      eks.amazonaws.com/nodegroup: production-wks-spot-node-group
      eks.amazonaws.com/nodegroup-image: ami-0b4892d01b0bc996c
      failure-domain.beta.kubernetes.io/region: eu-central-1
      failure-domain.beta.kubernetes.io/zone: eu-central-1b
      k8s.io/cloud-provider-aws: 923b45e46921a8260adf43a035942c66
      kubernetes.io/arch: amd64
      kubernetes.io/hostname: ip-10-0-11-248.eu-central-1.compute.internal
      kubernetes.io/os: linux
      node-type: spot
      node.kubernetes.io/instance-type: t3a.large
      topology.ebs.csi.aws.com/zone: eu-central-1b
      topology.k8s.aws/zone-id: euc1-az3
      topology.kubernetes.io/region: eu-central-1
      topology.kubernetes.io/zone: eu-central-1b
      workload: general
    name: ip-10-0-11-248.eu-central-1.compute.internal
    resourceVersion: "24427663"
    uid: 7294d5ec-df74-40d2-b548-70cf39640405
  spec:
    providerID: aws:///eu-central-1b/i-042d9bf12c4fad70d
    taints:
    - effect: PreferNoSchedule
      key: DeletionCandidateOfClusterAutoscaler
      value: "**********"
  status:
    addresses:
    - address: ***********
      type: InternalIP
    - address: ip-10-0-11-248.eu-central-1.compute.internal
      type: InternalDNS
    - address: ip-10-0-11-248.eu-central-1.compute.internal
      type: Hostname
    allocatable:
      cpu: 1930m
      ephemeral-storage: "18242267924"
      hugepages-1Gi: "0"
      hugepages-2Mi: "0"
      memory: 7287684Ki
      pods: "35"
    capacity:
      cpu: "2"
      ephemeral-storage: 20959212Ki
      hugepages-1Gi: "0"
      hugepages-2Mi: "0"
      memory: 8045444Ki
      pods: "35"
    conditions:
    - lastHeartbeatTime: "2025-07-11T21:10:33Z"
      lastTransitionTime: "2025-07-11T09:40:59Z"
      message: kubelet has sufficient memory available
      reason: KubeletHasSufficientMemory
      status: "False"
      type: MemoryPressure
    - lastHeartbeatTime: "2025-07-11T21:10:33Z"
      lastTransitionTime: "2025-07-11T09:40:59Z"
      message: kubelet has no disk pressure
      reason: KubeletHasNoDiskPressure
      status: "False"
      type: DiskPressure
    - lastHeartbeatTime: "2025-07-11T21:10:33Z"
      lastTransitionTime: "2025-07-11T09:40:59Z"
      message: kubelet has sufficient PID available
      reason: KubeletHasSufficientPID
      status: "False"
      type: PIDPressure
    - lastHeartbeatTime: "2025-07-11T21:10:33Z"
      lastTransitionTime: "2025-07-11T09:41:17Z"
      message: kubelet is posting ready status
      reason: KubeletReady
      status: "True"
      type: Ready
    daemonEndpoints:
      kubeletEndpoint:
        Port: 10250
    images:
    - names:
      - docker.io/falcosecurity/falco-driver-loader@sha256:2a66353991bbe7199f4715e3c08d5bc0247a71d753913c6ca66b6cad9297fd6f
      - docker.io/falcosecurity/falco-driver-loader:0.41.1
      sizeBytes: 340843905
    - names:
      - docker.io/falcosecurity/falco@sha256:731c5b47e697c56749d97f1fb30399248e1019e6959b2a2db866a17af7af6395
      - docker.io/falcosecurity/falco:0.41.1
      sizeBytes: 80664481
    - names:
      - public.ecr.aws/mountpoint-s3-csi-driver/aws-mountpoint-s3-csi-driver@sha256:d56c640f4346487322d343355c17a040c20f7132bb91f3e292064e726774b81d
      - public.ecr.aws/mountpoint-s3-csi-driver/aws-mountpoint-s3-csi-driver:v1.10.0
      sizeBytes: 65449064
    - names:
      - ************.dkr.ecr.eu-central-1.amazonaws.com/amazon-k8s-cni-init@sha256:a6f314161aafa0ee8a3d7a6d452a6a707d0b21940f3fb3d493071fc283b64281
      - 066635153087.dkr.ecr.il-central-1.amazonaws.com/amazon-k8s-cni-init:v1.19.2
      - 066635153087.dkr.ecr.il-central-1.amazonaws.com/amazon-k8s-cni-init:v1.19.2-eksbuild.1
      - 121268973566.dkr.ecr.ap-southeast-7.amazonaws.com/amazon-k8s-cni-init:v1.19.2
      - 121268973566.dkr.ecr.ap-southeast-7.amazonaws.com/amazon-k8s-cni-init:v1.19.2-eksbuild.1
      sizeBytes: 62982129
    - names:
      - 066635153087.dkr.ecr.il-central-1.amazonaws.com/amazon-k8s-cni-init:v1.20.0-rc1-eksbuild.4
      - 121268973566.dkr.ecr.ap-southeast-7.amazonaws.com/amazon-k8s-cni-init:v1.20.0-rc1-eksbuild.4
      - 151610086707.dkr.ecr.ap-southeast-5.amazonaws.com/amazon-k8s-cni-init:v1.20.0-rc1-eksbuild.4
      - 296578399912.dkr.ecr.ap-southeast-3.amazonaws.com/amazon-k8s-cni-init:v1.20.0-rc1-eksbuild.4
      - 455263428931.dkr.ecr.eu-south-2.amazonaws.com/amazon-k8s-cni-init:v1.20.0-rc1-eksbuild.4
      sizeBytes: 62360666
    - names:
      - 066635153087.dkr.ecr.il-central-1.amazonaws.com/amazon-k8s-cni:v1.20.0-rc1-eksbuild.4
      - 121268973566.dkr.ecr.ap-southeast-7.amazonaws.com/amazon-k8s-cni:v1.20.0-rc1-eksbuild.4
      - 151610086707.dkr.ecr.ap-southeast-5.amazonaws.com/amazon-k8s-cni:v1.20.0-rc1-eksbuild.4
      - 296578399912.dkr.ecr.ap-southeast-3.amazonaws.com/amazon-k8s-cni:v1.20.0-rc1-eksbuild.4
      - 455263428931.dkr.ecr.eu-south-2.amazonaws.com/amazon-k8s-cni:v1.20.0-rc1-eksbuild.4
      sizeBytes: 49616023
    - names:
      - 066635153087.dkr.ecr.il-central-1.amazonaws.com/amazon-k8s-cni:v1.19.2
      - 066635153087.dkr.ecr.il-central-1.amazonaws.com/amazon-k8s-cni:v1.19.2-eksbuild.1
      - 121268973566.dkr.ecr.ap-southeast-7.amazonaws.com/amazon-k8s-cni:v1.19.2
      - 121268973566.dkr.ecr.ap-southeast-7.amazonaws.com/amazon-k8s-cni:v1.19.2-eksbuild.1
      - 151610086707.dkr.ecr.ap-southeast-5.amazonaws.com/amazon-k8s-cni:v1.19.2
      sizeBytes: 48787219
    - names:
      - 066635153087.dkr.ecr.il-central-1.amazonaws.com/eks/kube-proxy:v1.32.3-minimal-eksbuild.7
      - 121268973566.dkr.ecr.ap-southeast-7.amazonaws.com/eks/kube-proxy:v1.32.3-minimal-eksbuild.7
      - 151610086707.dkr.ecr.ap-southeast-5.amazonaws.com/eks/kube-proxy:v1.32.3-minimal-eksbuild.7
      - 296578399912.dkr.ecr.ap-southeast-3.amazonaws.com/eks/kube-proxy:v1.32.3-minimal-eksbuild.7
      - 455263428931.dkr.ecr.eu-south-2.amazonaws.com/eks/kube-proxy:v1.32.3-minimal-eksbuild.7
      sizeBytes: 47544153
    - names:
      - ************.dkr.ecr.eu-central-1.amazonaws.com/amazon/aws-network-policy-agent@sha256:8f53fe281da5074976526a28a13422134a23aeab33199c0947fa61b34086eac3
      - ************.dkr.ecr.eu-central-1.amazonaws.com/amazon/aws-network-policy-agent:v1.1.6-eksbuild.1
      sizeBytes: 40743598
    - names:
      - 066635153087.dkr.ecr.il-central-1.amazonaws.com/eks/kube-proxy:v1.32.0-minimal-eksbuild.2
      - 121268973566.dkr.ecr.ap-southeast-7.amazonaws.com/eks/kube-proxy:v1.32.0-minimal-eksbuild.2
      - 151610086707.dkr.ecr.ap-southeast-5.amazonaws.com/eks/kube-proxy:v1.32.0-minimal-eksbuild.2
      - 296578399912.dkr.ecr.ap-southeast-3.amazonaws.com/eks/kube-proxy:v1.32.0-minimal-eksbuild.2
      - 455263428931.dkr.ecr.eu-south-2.amazonaws.com/eks/kube-proxy:v1.32.0-minimal-eksbuild.2
      sizeBytes: 36604068
    - names:
      - docker.io/falcosecurity/falcoctl@sha256:8e02bfd0c44a954495a5c7f980693f603d47c1bec2e55c86319c55134b9a5b6e
      - docker.io/falcosecurity/falcoctl:0.11.2
      sizeBytes: 31964684
    - names:
      - public.ecr.aws/ebs-csi-driver/aws-ebs-csi-driver@sha256:2aa362f4c40746c4318938b2f3afc279aa799fb341025e1ddbd04a5893619d66
      - public.ecr.aws/ebs-csi-driver/aws-ebs-csi-driver:v1.28.0
      sizeBytes: 27275412
    - names:
      - public.ecr.aws/eks-distro/kubernetes-csi/livenessprobe@sha256:5ef330be579f9ec1c0e9fb84b346ae1cf1c0cffaadf11ed78951319a3b0946ed
      - public.ecr.aws/eks-distro/kubernetes-csi/livenessprobe:v2.12.0-eks-1-29-7
      sizeBytes: 8063647
    - names:
      - public.ecr.aws/eks-distro/kubernetes-csi/livenessprobe@sha256:0228897d2395dc2ab1ccf963f496135620037145f92642f4ca71dab0c5dbf027
      - public.ecr.aws/eks-distro/kubernetes-csi/livenessprobe:v2.12.0-eks-1-29-5
      sizeBytes: 8063540
    - names:
      - public.ecr.aws/eks-distro/kubernetes-csi/node-driver-registrar@sha256:a5264fe089aa754e60dbad6a8eee411c90ee241e5fe68dfad90246b2ceb15fe9
      - public.ecr.aws/eks-distro/kubernetes-csi/node-driver-registrar:v2.10.0-eks-1-29-7
      sizeBytes: 6779878
    - names:
      - public.ecr.aws/eks-distro/kubernetes-csi/node-driver-registrar@sha256:c4a8d8843a331be6a1117e683175e07733fbf149ef8bd4255b75e5fdf1736cff
      - public.ecr.aws/eks-distro/kubernetes-csi/node-driver-registrar:v2.10.0-eks-1-29-5
      sizeBytes: 6779439
    - names:
      - ************.dkr.ecr.eu-central-1.amazonaws.com/eks/pause@sha256:529cf6b1b6e5b76e901abc43aee825badbd93f9c5ee5f1e316d46a83abbce5a2
      - ************.dkr.ecr.eu-central-1.amazonaws.com/eks/pause:3.5
      sizeBytes: 298689
    nodeInfo:
      architecture: amd64
      bootID: d00fb557-664f-43f0-8219-498ca750c32b
      containerRuntimeVersion: containerd://1.7.27
      kernelVersion: 5.10.236-228.935.amzn2.x86_64
      kubeProxyVersion: v1.32.3-eks-473151a
      kubeletVersion: v1.32.3-eks-473151a
      machineID: ec23c6a034d4c149c10a0a3389bd7e37
      operatingSystem: linux
      osImage: Amazon Linux 2
      systemUUID: ec23c6a0-34d4-c149-c10a-0a3389bd7e37
- apiVersion: v1
  kind: Node
  metadata:
    annotations:
      alpha.kubernetes.io/provided-node-ip: ***********
      csi.volume.kubernetes.io/nodeid: '{"ebs.csi.aws.com":"i-03ce4f2bc439dfed8","s3.csi.aws.com":"ip-10-0-12-146.eu-central-1.compute.internal"}'
      node.alpha.kubernetes.io/ttl: "0"
      volumes.kubernetes.io/controller-managed-attach-detach: "true"
    creationTimestamp: "2025-07-09T16:00:04Z"
    labels:
      beta.kubernetes.io/arch: amd64
      beta.kubernetes.io/instance-type: t3a.large
      beta.kubernetes.io/os: linux
      eks.amazonaws.com/capacityType: ON_DEMAND
      eks.amazonaws.com/nodegroup: production-wks-node-group
      eks.amazonaws.com/nodegroup-image: ami-010ae8fe27713f296
      eks.amazonaws.com/sourceLaunchTemplateId: lt-02f84270ec98bd1c2
      eks.amazonaws.com/sourceLaunchTemplateVersion: "2"
      failure-domain.beta.kubernetes.io/region: eu-central-1
      failure-domain.beta.kubernetes.io/zone: eu-central-1c
      k8s.io/cloud-provider-aws: 923b45e46921a8260adf43a035942c66
      kubernetes.io/arch: amd64
      kubernetes.io/hostname: ip-10-0-12-146.eu-central-1.compute.internal
      kubernetes.io/os: linux
      node-type: on-demand
      node.kubernetes.io/instance-type: t3a.large
      topology.ebs.csi.aws.com/zone: eu-central-1c
      topology.k8s.aws/zone-id: euc1-az1
      topology.kubernetes.io/region: eu-central-1
      topology.kubernetes.io/zone: eu-central-1c
      workload: critical
    name: ip-10-0-12-146.eu-central-1.compute.internal
    resourceVersion: "24426206"
    uid: 9a3aa98f-1a3e-45fc-8a49-799584b25c56
  spec:
    providerID: aws:///eu-central-1c/i-03ce4f2bc439dfed8
  status:
    addresses:
    - address: ***********
      type: InternalIP
    - address: ip-10-0-12-146.eu-central-1.compute.internal
      type: InternalDNS
    - address: ip-10-0-12-146.eu-central-1.compute.internal
      type: Hostname
    allocatable:
      cpu: 1930m
      ephemeral-storage: "**********"
      hugepages-1Gi: "0"
      hugepages-2Mi: "0"
      memory: 7287676Ki
      pods: "35"
    capacity:
      cpu: "2"
      ephemeral-storage: 10473452Ki
      hugepages-1Gi: "0"
      hugepages-2Mi: "0"
      memory: 8045436Ki
      pods: "35"
    conditions:
    - lastHeartbeatTime: "2025-07-11T21:07:19Z"
      lastTransitionTime: "2025-07-09T16:00:04Z"
      message: kubelet has sufficient memory available
      reason: KubeletHasSufficientMemory
      status: "False"
      type: MemoryPressure
    - lastHeartbeatTime: "2025-07-11T21:07:19Z"
      lastTransitionTime: "2025-07-11T20:52:02Z"
      message: kubelet has no disk pressure
      reason: KubeletHasNoDiskPressure
      status: "False"
      type: DiskPressure
    - lastHeartbeatTime: "2025-07-11T21:07:19Z"
      lastTransitionTime: "2025-07-09T16:00:04Z"
      message: kubelet has sufficient PID available
      reason: KubeletHasSufficientPID
      status: "False"
      type: PIDPressure
    - lastHeartbeatTime: "2025-07-11T21:07:19Z"
      lastTransitionTime: "2025-07-09T16:00:22Z"
      message: kubelet is posting ready status
      reason: KubeletReady
      status: "True"
      type: Ready
    daemonEndpoints:
      kubeletEndpoint:
        Port: 10250
    images:
    - names:
      - 545009857703.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev@sha256:8bb582cf70da312d64d434b42c4e4f96edfca15bae0d4245ec423cf1d3f1b62e
      - 545009857703.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:2.0.56-test
      sizeBytes: 657789910
    - names:
      - docker.io/library/mysql@sha256:af4aa26a10d3cddc91401808a4f8dfe53eb169caf31d7d141a29e21493dfefb8
      - docker.io/library/mysql:8.0
      sizeBytes: 235111183
    - names:
      - docker.io/istio/proxyv2@sha256:*****************************8cad13a35afddb9139ff795e36237327137
      - docker.io/istio/proxyv2:1.20.0
      sizeBytes: 96408950
    - names:
      - 545009857703.dkr.ecr.eu-central-1.amazonaws.com/rabbitmq_dev@sha256:a40d91656430a34f8ee2dfbac222d974915ef9d448f7e99ecb4118218a8a84c0
      - 545009857703.dkr.ecr.eu-central-1.amazonaws.com/rabbitmq_dev:1.02
      sizeBytes: 84808465
    - names:
      - docker.io/istio/pilot@sha256:da619cc0915a27988ba8357916d84b4a137253a9df967e6521b14dc7100bb246
      - docker.io/istio/pilot:1.20.0
      sizeBytes: 74042223
    - names:
      - public.ecr.aws/mountpoint-s3-csi-driver/aws-mountpoint-s3-csi-driver@sha256:d56c640f4346487322d343355c17a040c20f7132bb91f3e292064e726774b81d
      - public.ecr.aws/mountpoint-s3-csi-driver/aws-mountpoint-s3-csi-driver:v1.10.0
      sizeBytes: 65449064
    - names:
      - ************.dkr.ecr.eu-central-1.amazonaws.com/amazon-k8s-cni-init@sha256:a6f314161aafa0ee8a3d7a6d452a6a707d0b21940f3fb3d493071fc283b64281
      - 066635153087.dkr.ecr.il-central-1.amazonaws.com/amazon-k8s-cni-init:v1.19.2
      - 066635153087.dkr.ecr.il-central-1.amazonaws.com/amazon-k8s-cni-init:v1.19.2-eksbuild.1
      - 121268973566.dkr.ecr.ap-southeast-7.amazonaws.com/amazon-k8s-cni-init:v1.19.2
      - 121268973566.dkr.ecr.ap-southeast-7.amazonaws.com/amazon-k8s-cni-init:v1.19.2-eksbuild.1
      sizeBytes: 62982129
    - names:
      - 066635153087.dkr.ecr.il-central-1.amazonaws.com/amazon-k8s-cni:v1.19.2
      - 066635153087.dkr.ecr.il-central-1.amazonaws.com/amazon-k8s-cni:v1.19.2-eksbuild.1
      - 121268973566.dkr.ecr.ap-southeast-7.amazonaws.com/amazon-k8s-cni:v1.19.2
      - 121268973566.dkr.ecr.ap-southeast-7.amazonaws.com/amazon-k8s-cni:v1.19.2-eksbuild.1
      - 151610086707.dkr.ecr.ap-southeast-5.amazonaws.com/amazon-k8s-cni:v1.19.2
      sizeBytes: 48787219
    - names:
      - ************.dkr.ecr.eu-central-1.amazonaws.com/amazon/aws-network-policy-agent@sha256:8f53fe281da5074976526a28a13422134a23aeab33199c0947fa61b34086eac3
      - ************.dkr.ecr.eu-central-1.amazonaws.com/amazon/aws-network-policy-agent:v1.1.6-eksbuild.1
      sizeBytes: 40743598
    - names:
      - 066635153087.dkr.ecr.il-central-1.amazonaws.com/eks/kube-proxy:v1.32.0-minimal-eksbuild.2
      - 121268973566.dkr.ecr.ap-southeast-7.amazonaws.com/eks/kube-proxy:v1.32.0-minimal-eksbuild.2
      - 151610086707.dkr.ecr.ap-southeast-5.amazonaws.com/eks/kube-proxy:v1.32.0-minimal-eksbuild.2
      - 296578399912.dkr.ecr.ap-southeast-3.amazonaws.com/eks/kube-proxy:v1.32.0-minimal-eksbuild.2
      - 455263428931.dkr.ecr.eu-south-2.amazonaws.com/eks/kube-proxy:v1.32.0-minimal-eksbuild.2
      sizeBytes: 36604068
    - names:
      - public.ecr.aws/ebs-csi-driver/aws-ebs-csi-driver@sha256:2aa362f4c40746c4318938b2f3afc279aa799fb341025e1ddbd04a5893619d66
      - public.ecr.aws/ebs-csi-driver/aws-ebs-csi-driver:v1.28.0
      sizeBytes: 27275412
    - names:
      - docker.io/library/nginx@sha256:a74534e76ee1121d418fa7394ca930eb67440deda413848bc67c68138535b989
      - docker.io/library/nginx:1.21-alpine
      sizeBytes: 10170636
    - names:
      - docker.io/curlimages/curl@sha256:9a1ed35addb45476afa911696297f8e115993df459278ed036182dd2cd22b67b
      - docker.io/curlimages/curl:latest
      sizeBytes: 9834279
    - names:
      - public.ecr.aws/eks-distro/kubernetes-csi/livenessprobe@sha256:5ef330be579f9ec1c0e9fb84b346ae1cf1c0cffaadf11ed78951319a3b0946ed
      - public.ecr.aws/eks-distro/kubernetes-csi/livenessprobe:v2.12.0-eks-1-29-7
      sizeBytes: 8063647
    - names:
      - public.ecr.aws/eks-distro/kubernetes-csi/livenessprobe@sha256:0228897d2395dc2ab1ccf963f496135620037145f92642f4ca71dab0c5dbf027
      - public.ecr.aws/eks-distro/kubernetes-csi/livenessprobe:v2.12.0-eks-1-29-5
      sizeBytes: 8063540
    - names:
      - public.ecr.aws/eks-distro/kubernetes-csi/node-driver-registrar@sha256:a5264fe089aa754e60dbad6a8eee411c90ee241e5fe68dfad90246b2ceb15fe9
      - public.ecr.aws/eks-distro/kubernetes-csi/node-driver-registrar:v2.10.0-eks-1-29-7
      sizeBytes: 6779878
    - names:
      - public.ecr.aws/eks-distro/kubernetes-csi/node-driver-registrar@sha256:c4a8d8843a331be6a1117e683175e07733fbf149ef8bd4255b75e5fdf1736cff
      - public.ecr.aws/eks-distro/kubernetes-csi/node-driver-registrar:v2.10.0-eks-1-29-5
      sizeBytes: 6779439
    - names:
      - docker.io/library/busybox@sha256:f85340bf132ae937d2c2a763b8335c9bab35d6e8293f70f606b9c6178d84f42b
      - docker.io/library/busybox:latest
      sizeBytes: 2156518
    - names:
      - ************.dkr.ecr.eu-central-1.amazonaws.com/eks/pause@sha256:529cf6b1b6e5b76e901abc43aee825badbd93f9c5ee5f1e316d46a83abbce5a2
      - ************.dkr.ecr.eu-central-1.amazonaws.com/eks/pause:3.5
      sizeBytes: 298689
    nodeInfo:
      architecture: amd64
      bootID: 3a25caee-08c8-4681-b149-6d87eeca464e
      containerRuntimeVersion: containerd://1.7.27
      kernelVersion: 5.10.238-231.953.amzn2.x86_64
      kubeProxyVersion: v1.32.3-eks-473151a
      kubeletVersion: v1.32.3-eks-473151a
      machineID: ec28bc532c44d06cae5166cf2c2b5479
      operatingSystem: linux
      osImage: Amazon Linux 2
      systemUUID: ec28bc53-2c44-d06c-ae51-66cf2c2b5479
- apiVersion: v1
  kind: Node
  metadata:
    annotations:
      alpha.kubernetes.io/provided-node-ip: ***********
      csi.volume.kubernetes.io/nodeid: '{"ebs.csi.aws.com":"i-089e242fa73e59ca3","s3.csi.aws.com":"ip-10-0-12-214.eu-central-1.compute.internal"}'
      node.alpha.kubernetes.io/ttl: "0"
      volumes.kubernetes.io/controller-managed-attach-detach: "true"
    creationTimestamp: "2025-07-11T09:07:36Z"
    labels:
      beta.kubernetes.io/arch: amd64
      beta.kubernetes.io/instance-type: t3a.large
      beta.kubernetes.io/os: linux
      eks.amazonaws.com/capacityType: SPOT
      eks.amazonaws.com/nodegroup: production-wks-spot-node-group
      eks.amazonaws.com/nodegroup-image: ami-0b4892d01b0bc996c
      failure-domain.beta.kubernetes.io/region: eu-central-1
      failure-domain.beta.kubernetes.io/zone: eu-central-1c
      k8s.io/cloud-provider-aws: 923b45e46921a8260adf43a035942c66
      kubernetes.io/arch: amd64
      kubernetes.io/hostname: ip-10-0-12-214.eu-central-1.compute.internal
      kubernetes.io/os: linux
      node-type: spot
      node.kubernetes.io/instance-type: t3a.large
      topology.ebs.csi.aws.com/zone: eu-central-1c
      topology.k8s.aws/zone-id: euc1-az1
      topology.kubernetes.io/region: eu-central-1
      topology.kubernetes.io/zone: eu-central-1c
      workload: general
    name: ip-10-0-12-214.eu-central-1.compute.internal
    resourceVersion: "24427039"
    uid: 526e6db5-eb2a-495e-8721-d63f52d850d9
  spec:
    providerID: aws:///eu-central-1c/i-089e242fa73e59ca3
    taints:
    - effect: PreferNoSchedule
      key: DeletionCandidateOfClusterAutoscaler
      value: "**********"
  status:
    addresses:
    - address: ***********
      type: InternalIP
    - address: ip-10-0-12-214.eu-central-1.compute.internal
      type: InternalDNS
    - address: ip-10-0-12-214.eu-central-1.compute.internal
      type: Hostname
    allocatable:
      cpu: 1930m
      ephemeral-storage: "18242267924"
      hugepages-1Gi: "0"
      hugepages-2Mi: "0"
      memory: 7287684Ki
      pods: "35"
    capacity:
      cpu: "2"
      ephemeral-storage: 20959212Ki
      hugepages-1Gi: "0"
      hugepages-2Mi: "0"
      memory: 8045444Ki
      pods: "35"
    conditions:
    - lastHeartbeatTime: "2025-07-11T21:09:13Z"
      lastTransitionTime: "2025-07-11T09:07:36Z"
      message: kubelet has sufficient memory available
      reason: KubeletHasSufficientMemory
      status: "False"
      type: MemoryPressure
    - lastHeartbeatTime: "2025-07-11T21:09:13Z"
      lastTransitionTime: "2025-07-11T09:07:36Z"
      message: kubelet has no disk pressure
      reason: KubeletHasNoDiskPressure
      status: "False"
      type: DiskPressure
    - lastHeartbeatTime: "2025-07-11T21:09:13Z"
      lastTransitionTime: "2025-07-11T09:07:36Z"
      message: kubelet has sufficient PID available
      reason: KubeletHasSufficientPID
      status: "False"
      type: PIDPressure
    - lastHeartbeatTime: "2025-07-11T21:09:13Z"
      lastTransitionTime: "2025-07-11T09:07:53Z"
      message: kubelet is posting ready status
      reason: KubeletReady
      status: "True"
      type: Ready
    daemonEndpoints:
      kubeletEndpoint:
        Port: 10250
    images:
    - names:
      - 545009857703.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev@sha256:8bb582cf70da312d64d434b42c4e4f96edfca15bae0d4245ec423cf1d3f1b62e
      - 545009857703.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:2.0.56-test
      sizeBytes: 657789910
    - names:
      - docker.io/falcosecurity/falco-driver-loader@sha256:2a66353991bbe7199f4715e3c08d5bc0247a71d753913c6ca66b6cad9297fd6f
      - docker.io/falcosecurity/falco-driver-loader:0.41.1
      sizeBytes: 340843905
    - names:
      - docker.io/library/mysql@sha256:af4aa26a10d3cddc91401808a4f8dfe53eb169caf31d7d141a29e21493dfefb8
      - docker.io/library/mysql:8.0
      sizeBytes: 235111183
    - names:
      - 545009857703.dkr.ecr.eu-central-1.amazonaws.com/nginx_dev@sha256:42618102e5cc405c551c142acd857aaaa953559b63ca02fff46760e34d4ff149
      - 545009857703.dkr.ecr.eu-central-1.amazonaws.com/nginx_dev:1.0.6-update_ssl
      sizeBytes: 122795520
    - names:
      - docker.io/bitnami/kubectl@sha256:e706851b19c0c4e668614b7c5a6b0c5bbcfbe7fb73f5d999250e0da8bfff42c6
      - docker.io/bitnami/kubectl:latest
      sizeBytes: 113092335
    - names:
      - docker.io/istio/proxyv2@sha256:*****************************8cad13a35afddb9139ff795e36237327137
      - docker.io/istio/proxyv2:1.20.0
      sizeBytes: 96408950
    - names:
      - docker.io/falcosecurity/falco@sha256:731c5b47e697c56749d97f1fb30399248e1019e6959b2a2db866a17af7af6395
      - docker.io/falcosecurity/falco:0.41.1
      sizeBytes: 80664481
    - names:
      - public.ecr.aws/mountpoint-s3-csi-driver/aws-mountpoint-s3-csi-driver@sha256:d56c640f4346487322d343355c17a040c20f7132bb91f3e292064e726774b81d
      - public.ecr.aws/mountpoint-s3-csi-driver/aws-mountpoint-s3-csi-driver:v1.10.0
      sizeBytes: 65449064
    - names:
      - ************.dkr.ecr.eu-central-1.amazonaws.com/amazon-k8s-cni-init@sha256:a6f314161aafa0ee8a3d7a6d452a6a707d0b21940f3fb3d493071fc283b64281
      - 066635153087.dkr.ecr.il-central-1.amazonaws.com/amazon-k8s-cni-init:v1.19.2
      - 066635153087.dkr.ecr.il-central-1.amazonaws.com/amazon-k8s-cni-init:v1.19.2-eksbuild.1
      - 121268973566.dkr.ecr.ap-southeast-7.amazonaws.com/amazon-k8s-cni-init:v1.19.2
      - 121268973566.dkr.ecr.ap-southeast-7.amazonaws.com/amazon-k8s-cni-init:v1.19.2-eksbuild.1
      sizeBytes: 62982129
    - names:
      - 066635153087.dkr.ecr.il-central-1.amazonaws.com/amazon-k8s-cni-init:v1.20.0-rc1-eksbuild.4
      - 121268973566.dkr.ecr.ap-southeast-7.amazonaws.com/amazon-k8s-cni-init:v1.20.0-rc1-eksbuild.4
      - 151610086707.dkr.ecr.ap-southeast-5.amazonaws.com/amazon-k8s-cni-init:v1.20.0-rc1-eksbuild.4
      - 296578399912.dkr.ecr.ap-southeast-3.amazonaws.com/amazon-k8s-cni-init:v1.20.0-rc1-eksbuild.4
      - 455263428931.dkr.ecr.eu-south-2.amazonaws.com/amazon-k8s-cni-init:v1.20.0-rc1-eksbuild.4
      sizeBytes: 62360666
    - names:
      - 066635153087.dkr.ecr.il-central-1.amazonaws.com/amazon-k8s-cni:v1.20.0-rc1-eksbuild.4
      - 121268973566.dkr.ecr.ap-southeast-7.amazonaws.com/amazon-k8s-cni:v1.20.0-rc1-eksbuild.4
      - 151610086707.dkr.ecr.ap-southeast-5.amazonaws.com/amazon-k8s-cni:v1.20.0-rc1-eksbuild.4
      - 296578399912.dkr.ecr.ap-southeast-3.amazonaws.com/amazon-k8s-cni:v1.20.0-rc1-eksbuild.4
      - 455263428931.dkr.ecr.eu-south-2.amazonaws.com/amazon-k8s-cni:v1.20.0-rc1-eksbuild.4
      sizeBytes: 49616023
    - names:
      - 066635153087.dkr.ecr.il-central-1.amazonaws.com/amazon-k8s-cni:v1.19.2
      - 066635153087.dkr.ecr.il-central-1.amazonaws.com/amazon-k8s-cni:v1.19.2-eksbuild.1
      - 121268973566.dkr.ecr.ap-southeast-7.amazonaws.com/amazon-k8s-cni:v1.19.2
      - 121268973566.dkr.ecr.ap-southeast-7.amazonaws.com/amazon-k8s-cni:v1.19.2-eksbuild.1
      - 151610086707.dkr.ecr.ap-southeast-5.amazonaws.com/amazon-k8s-cni:v1.19.2
      sizeBytes: 48787219
    - names:
      - 066635153087.dkr.ecr.il-central-1.amazonaws.com/eks/kube-proxy:v1.32.3-minimal-eksbuild.7
      - 121268973566.dkr.ecr.ap-southeast-7.amazonaws.com/eks/kube-proxy:v1.32.3-minimal-eksbuild.7
      - 151610086707.dkr.ecr.ap-southeast-5.amazonaws.com/eks/kube-proxy:v1.32.3-minimal-eksbuild.7
      - 296578399912.dkr.ecr.ap-southeast-3.amazonaws.com/eks/kube-proxy:v1.32.3-minimal-eksbuild.7
      - 455263428931.dkr.ecr.eu-south-2.amazonaws.com/eks/kube-proxy:v1.32.3-minimal-eksbuild.7
      sizeBytes: 47544153
    - names:
      - ************.dkr.ecr.eu-central-1.amazonaws.com/amazon/aws-network-policy-agent@sha256:8f53fe281da5074976526a28a13422134a23aeab33199c0947fa61b34086eac3
      - ************.dkr.ecr.eu-central-1.amazonaws.com/amazon/aws-network-policy-agent:v1.1.6-eksbuild.1
      sizeBytes: 40743598
    - names:
      - 066635153087.dkr.ecr.il-central-1.amazonaws.com/eks/kube-proxy:v1.32.0-minimal-eksbuild.2
      - 121268973566.dkr.ecr.ap-southeast-7.amazonaws.com/eks/kube-proxy:v1.32.0-minimal-eksbuild.2
      - 151610086707.dkr.ecr.ap-southeast-5.amazonaws.com/eks/kube-proxy:v1.32.0-minimal-eksbuild.2
      - 296578399912.dkr.ecr.ap-southeast-3.amazonaws.com/eks/kube-proxy:v1.32.0-minimal-eksbuild.2
      - 455263428931.dkr.ecr.eu-south-2.amazonaws.com/eks/kube-proxy:v1.32.0-minimal-eksbuild.2
      sizeBytes: 36604068
    - names:
      - docker.io/falcosecurity/falcoctl@sha256:8e02bfd0c44a954495a5c7f980693f603d47c1bec2e55c86319c55134b9a5b6e
      - docker.io/falcosecurity/falcoctl:0.11.2
      sizeBytes: 31964684
    - names:
      - public.ecr.aws/ebs-csi-driver/aws-ebs-csi-driver@sha256:2aa362f4c40746c4318938b2f3afc279aa799fb341025e1ddbd04a5893619d66
      - public.ecr.aws/ebs-csi-driver/aws-ebs-csi-driver:v1.28.0
      sizeBytes: 27275412
    - names:
      - docker.io/library/nginx@sha256:b2e814d28359e77bd0aa5fed1939620075e4ffa0eb20423cc557b375bd5c14ad
      - docker.io/library/nginx:alpine
      sizeBytes: 22419244
    - names:
      - docker.io/library/nginx@sha256:a74534e76ee1121d418fa7394ca930eb67440deda413848bc67c68138535b989
      - docker.io/library/nginx:1.21-alpine
      sizeBytes: 10170636
    - names:
      - docker.io/curlimages/curl@sha256:9a1ed35addb45476afa911696297f8e115993df459278ed036182dd2cd22b67b
      - docker.io/curlimages/curl:latest
      sizeBytes: 9834279
    - names:
      - public.ecr.aws/eks-distro/kubernetes-csi/livenessprobe@sha256:5ef330be579f9ec1c0e9fb84b346ae1cf1c0cffaadf11ed78951319a3b0946ed
      - public.ecr.aws/eks-distro/kubernetes-csi/livenessprobe:v2.12.0-eks-1-29-7
      sizeBytes: 8063647
    - names:
      - public.ecr.aws/eks-distro/kubernetes-csi/livenessprobe@sha256:0228897d2395dc2ab1ccf963f496135620037145f92642f4ca71dab0c5dbf027
      - public.ecr.aws/eks-distro/kubernetes-csi/livenessprobe:v2.12.0-eks-1-29-5
      sizeBytes: 8063540
    - names:
      - public.ecr.aws/eks-distro/kubernetes-csi/node-driver-registrar@sha256:a5264fe089aa754e60dbad6a8eee411c90ee241e5fe68dfad90246b2ceb15fe9
      - public.ecr.aws/eks-distro/kubernetes-csi/node-driver-registrar:v2.10.0-eks-1-29-7
      sizeBytes: 6779878
    - names:
      - public.ecr.aws/eks-distro/kubernetes-csi/node-driver-registrar@sha256:c4a8d8843a331be6a1117e683175e07733fbf149ef8bd4255b75e5fdf1736cff
      - public.ecr.aws/eks-distro/kubernetes-csi/node-driver-registrar:v2.10.0-eks-1-29-5
      sizeBytes: 6779439
    - names:
      - docker.io/library/alpine@sha256:8a1f59ffb675680d47db6337b49d22281a139e9d709335b492be023728e11715
      - docker.io/library/alpine:latest
      sizeBytes: 3807667
    - names:
      - docker.io/library/busybox@sha256:f85340bf132ae937d2c2a763b8335c9bab35d6e8293f70f606b9c6178d84f42b
      - docker.io/library/busybox:latest
      sizeBytes: 2156518
    - names:
      - ************.dkr.ecr.eu-central-1.amazonaws.com/eks/pause@sha256:529cf6b1b6e5b76e901abc43aee825badbd93f9c5ee5f1e316d46a83abbce5a2
      - ************.dkr.ecr.eu-central-1.amazonaws.com/eks/pause:3.5
      sizeBytes: 298689
    nodeInfo:
      architecture: amd64
      bootID: 6f4bf553-fabc-4a39-9b9e-cc2ce4a08f76
      containerRuntimeVersion: containerd://1.7.27
      kernelVersion: 5.10.236-228.935.amzn2.x86_64
      kubeProxyVersion: v1.32.3-eks-473151a
      kubeletVersion: v1.32.3-eks-473151a
      machineID: ec2118e8079b135b15fb163b7361c10f
      operatingSystem: linux
      osImage: Amazon Linux 2
      systemUUID: ec2118e8-079b-135b-15fb-163b7361c10f
- apiVersion: v1
  kind: Node
  metadata:
    annotations:
      alpha.kubernetes.io/provided-node-ip: **********
      csi.volume.kubernetes.io/nodeid: '{"ebs.csi.aws.com":"i-01421f7b91fce916c","s3.csi.aws.com":"ip-10-0-12-77.eu-central-1.compute.internal"}'
      node.alpha.kubernetes.io/ttl: "0"
      volumes.kubernetes.io/controller-managed-attach-detach: "true"
    creationTimestamp: "2025-07-09T16:00:02Z"
    labels:
      beta.kubernetes.io/arch: amd64
      beta.kubernetes.io/instance-type: t3a.large
      beta.kubernetes.io/os: linux
      eks.amazonaws.com/capacityType: ON_DEMAND
      eks.amazonaws.com/nodegroup: production-wks-node-group
      eks.amazonaws.com/nodegroup-image: ami-010ae8fe27713f296
      eks.amazonaws.com/sourceLaunchTemplateId: lt-02f84270ec98bd1c2
      eks.amazonaws.com/sourceLaunchTemplateVersion: "2"
      failure-domain.beta.kubernetes.io/region: eu-central-1
      failure-domain.beta.kubernetes.io/zone: eu-central-1c
      k8s.io/cloud-provider-aws: 923b45e46921a8260adf43a035942c66
      kubernetes.io/arch: amd64
      kubernetes.io/hostname: ip-10-0-12-77.eu-central-1.compute.internal
      kubernetes.io/os: linux
      node-type: on-demand
      node.kubernetes.io/instance-type: t3a.large
      topology.ebs.csi.aws.com/zone: eu-central-1c
      topology.k8s.aws/zone-id: euc1-az1
      topology.kubernetes.io/region: eu-central-1
      topology.kubernetes.io/zone: eu-central-1c
      workload: critical
    name: ip-10-0-12-77.eu-central-1.compute.internal
    resourceVersion: "24428015"
    uid: 32041102-d823-4891-a554-d1d5e909d611
  spec:
    providerID: aws:///eu-central-1c/i-01421f7b91fce916c
    taints:
    - effect: PreferNoSchedule
      key: DeletionCandidateOfClusterAutoscaler
      value: "**********"
  status:
    addresses:
    - address: **********
      type: InternalIP
    - address: ip-10-0-12-77.eu-central-1.compute.internal
      type: InternalDNS
    - address: ip-10-0-12-77.eu-central-1.compute.internal
      type: Hostname
    allocatable:
      cpu: 1930m
      ephemeral-storage: "**********"
      hugepages-1Gi: "0"
      hugepages-2Mi: "0"
      memory: 7287684Ki
      pods: "35"
    capacity:
      cpu: "2"
      ephemeral-storage: 10473452Ki
      hugepages-1Gi: "0"
      hugepages-2Mi: "0"
      memory: 8045444Ki
      pods: "35"
    conditions:
    - lastHeartbeatTime: "2025-07-11T21:11:21Z"
      lastTransitionTime: "2025-07-09T16:00:02Z"
      message: kubelet has sufficient memory available
      reason: KubeletHasSufficientMemory
      status: "False"
      type: MemoryPressure
    - lastHeartbeatTime: "2025-07-11T21:11:21Z"
      lastTransitionTime: "2025-07-11T16:21:56Z"
      message: kubelet has no disk pressure
      reason: KubeletHasNoDiskPressure
      status: "False"
      type: DiskPressure
    - lastHeartbeatTime: "2025-07-11T21:11:21Z"
      lastTransitionTime: "2025-07-09T16:00:02Z"
      message: kubelet has sufficient PID available
      reason: KubeletHasSufficientPID
      status: "False"
      type: PIDPressure
    - lastHeartbeatTime: "2025-07-11T21:11:21Z"
      lastTransitionTime: "2025-07-09T16:00:20Z"
      message: kubelet is posting ready status
      reason: KubeletReady
      status: "True"
      type: Ready
    daemonEndpoints:
      kubeletEndpoint:
        Port: 10250
    images:
    - names:
      - docker.io/falcosecurity/falco-driver-loader@sha256:2a66353991bbe7199f4715e3c08d5bc0247a71d753913c6ca66b6cad9297fd6f
      - docker.io/falcosecurity/falco-driver-loader:0.41.1
      sizeBytes: 340843905
    - names:
      - docker.io/library/mysql@sha256:af4aa26a10d3cddc91401808a4f8dfe53eb169caf31d7d141a29e21493dfefb8
      - docker.io/library/mysql:8.0
      sizeBytes: 235111183
    - names:
      - 545009857703.dkr.ecr.eu-central-1.amazonaws.com/nginx_dev@sha256:42618102e5cc405c551c142acd857aaaa953559b63ca02fff46760e34d4ff149
      - 545009857703.dkr.ecr.eu-central-1.amazonaws.com/nginx_dev:1.0.6-update_ssl
      sizeBytes: 122795520
    - names:
      - docker.io/istio/proxyv2@sha256:*****************************8cad13a35afddb9139ff795e36237327137
      - docker.io/istio/proxyv2:1.20.0
      sizeBytes: 96408950
    - names:
      - docker.io/falcosecurity/falco@sha256:731c5b47e697c56749d97f1fb30399248e1019e6959b2a2db866a17af7af6395
      - docker.io/falcosecurity/falco:0.41.1
      sizeBytes: 80664481
    - names:
      - public.ecr.aws/mountpoint-s3-csi-driver/aws-mountpoint-s3-csi-driver@sha256:d56c640f4346487322d343355c17a040c20f7132bb91f3e292064e726774b81d
      - public.ecr.aws/mountpoint-s3-csi-driver/aws-mountpoint-s3-csi-driver:v1.10.0
      sizeBytes: 65449064
    - names:
      - ************.dkr.ecr.eu-central-1.amazonaws.com/amazon-k8s-cni-init@sha256:a6f314161aafa0ee8a3d7a6d452a6a707d0b21940f3fb3d493071fc283b64281
      - 066635153087.dkr.ecr.il-central-1.amazonaws.com/amazon-k8s-cni-init:v1.19.2
      - 066635153087.dkr.ecr.il-central-1.amazonaws.com/amazon-k8s-cni-init:v1.19.2-eksbuild.1
      - 121268973566.dkr.ecr.ap-southeast-7.amazonaws.com/amazon-k8s-cni-init:v1.19.2
      - 121268973566.dkr.ecr.ap-southeast-7.amazonaws.com/amazon-k8s-cni-init:v1.19.2-eksbuild.1
      sizeBytes: 62982129
    - names:
      - 066635153087.dkr.ecr.il-central-1.amazonaws.com/amazon-k8s-cni:v1.19.2
      - 066635153087.dkr.ecr.il-central-1.amazonaws.com/amazon-k8s-cni:v1.19.2-eksbuild.1
      - 121268973566.dkr.ecr.ap-southeast-7.amazonaws.com/amazon-k8s-cni:v1.19.2
      - 121268973566.dkr.ecr.ap-southeast-7.amazonaws.com/amazon-k8s-cni:v1.19.2-eksbuild.1
      - 151610086707.dkr.ecr.ap-southeast-5.amazonaws.com/amazon-k8s-cni:v1.19.2
      sizeBytes: 48787219
    - names:
      - ************.dkr.ecr.eu-central-1.amazonaws.com/amazon/aws-network-policy-agent@sha256:8f53fe281da5074976526a28a13422134a23aeab33199c0947fa61b34086eac3
      - ************.dkr.ecr.eu-central-1.amazonaws.com/amazon/aws-network-policy-agent:v1.1.6-eksbuild.1
      sizeBytes: 40743598
    - names:
      - 066635153087.dkr.ecr.il-central-1.amazonaws.com/eks/kube-proxy:v1.32.0-minimal-eksbuild.2
      - 121268973566.dkr.ecr.ap-southeast-7.amazonaws.com/eks/kube-proxy:v1.32.0-minimal-eksbuild.2
      - 151610086707.dkr.ecr.ap-southeast-5.amazonaws.com/eks/kube-proxy:v1.32.0-minimal-eksbuild.2
      - 296578399912.dkr.ecr.ap-southeast-3.amazonaws.com/eks/kube-proxy:v1.32.0-minimal-eksbuild.2
      - 455263428931.dkr.ecr.eu-south-2.amazonaws.com/eks/kube-proxy:v1.32.0-minimal-eksbuild.2
      sizeBytes: 36604068
    - names:
      - docker.io/falcosecurity/falcoctl@sha256:8e02bfd0c44a954495a5c7f980693f603d47c1bec2e55c86319c55134b9a5b6e
      - docker.io/falcosecurity/falcoctl:0.11.2
      sizeBytes: 31964684
    - names:
      - public.ecr.aws/ebs-csi-driver/aws-ebs-csi-driver@sha256:2aa362f4c40746c4318938b2f3afc279aa799fb341025e1ddbd04a5893619d66
      - public.ecr.aws/ebs-csi-driver/aws-ebs-csi-driver:v1.28.0
      sizeBytes: 27275412
    - names:
      - docker.io/library/nginx@sha256:a74534e76ee1121d418fa7394ca930eb67440deda413848bc67c68138535b989
      - docker.io/library/nginx:1.21-alpine
      sizeBytes: 10170636
    - names:
      - docker.io/curlimages/curl@sha256:9a1ed35addb45476afa911696297f8e115993df459278ed036182dd2cd22b67b
      - docker.io/curlimages/curl:latest
      sizeBytes: 9834279
    - names:
      - public.ecr.aws/eks-distro/kubernetes-csi/livenessprobe@sha256:5ef330be579f9ec1c0e9fb84b346ae1cf1c0cffaadf11ed78951319a3b0946ed
      - public.ecr.aws/eks-distro/kubernetes-csi/livenessprobe:v2.12.0-eks-1-29-7
      sizeBytes: 8063647
    - names:
      - public.ecr.aws/eks-distro/kubernetes-csi/livenessprobe@sha256:0228897d2395dc2ab1ccf963f496135620037145f92642f4ca71dab0c5dbf027
      - public.ecr.aws/eks-distro/kubernetes-csi/livenessprobe:v2.12.0-eks-1-29-5
      sizeBytes: 8063540
    - names:
      - public.ecr.aws/eks-distro/kubernetes-csi/node-driver-registrar@sha256:a5264fe089aa754e60dbad6a8eee411c90ee241e5fe68dfad90246b2ceb15fe9
      - public.ecr.aws/eks-distro/kubernetes-csi/node-driver-registrar:v2.10.0-eks-1-29-7
      sizeBytes: 6779878
    - names:
      - public.ecr.aws/eks-distro/kubernetes-csi/node-driver-registrar@sha256:c4a8d8843a331be6a1117e683175e07733fbf149ef8bd4255b75e5fdf1736cff
      - public.ecr.aws/eks-distro/kubernetes-csi/node-driver-registrar:v2.10.0-eks-1-29-5
      sizeBytes: 6779439
    - names:
      - docker.io/library/busybox@sha256:f85340bf132ae937d2c2a763b8335c9bab35d6e8293f70f606b9c6178d84f42b
      - docker.io/library/busybox:latest
      sizeBytes: 2156518
    - names:
      - ************.dkr.ecr.eu-central-1.amazonaws.com/eks/pause@sha256:529cf6b1b6e5b76e901abc43aee825badbd93f9c5ee5f1e316d46a83abbce5a2
      - ************.dkr.ecr.eu-central-1.amazonaws.com/eks/pause:3.5
      sizeBytes: 298689
    nodeInfo:
      architecture: amd64
      bootID: 3586efa8-9542-4feb-a3ef-7025ca180f67
      containerRuntimeVersion: containerd://1.7.27
      kernelVersion: 5.10.238-231.953.amzn2.x86_64
      kubeProxyVersion: v1.32.3-eks-473151a
      kubeletVersion: v1.32.3-eks-473151a
      machineID: ec29ccc4d011955ce6154893fde932db
      operatingSystem: linux
      osImage: Amazon Linux 2
      systemUUID: ec29ccc4-d011-955c-e615-4893fde932db
kind: List
metadata:
  resourceVersion: ""
