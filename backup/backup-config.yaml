apiVersion: velero.io/v1
kind: Schedule
metadata:
  name: daily-backup
  namespace: velero
spec:
  schedule: "0 1 * * *"  # Daily at 1 AM
  template:
    hooks: {}
    includedNamespaces:
    - '*'
    ttl: 720h  # 30 days retention
    storageLocation: default
    volumeSnapshotLocations:
    - default
---
apiVersion: velero.io/v1
kind: BackupStorageLocation
metadata:
  name: default
  namespace: velero
spec:
  provider: aws
  objectStorage:
    bucket: architrave-backups
  config:
    region: eu-west-2
---
apiVersion: velero.io/v1
kind: VolumeSnapshotLocation
metadata:
  name: default
  namespace: velero
spec:
  provider: aws
  config:
    region: eu-west-2
---
apiVersion: batch/v1
kind: CronJob
metadata:
  name: backup-verification
  namespace: velero
spec:
  schedule: "0 2 * * *"  # Daily at 2 AM
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: backup-verifier
            image: velero/velero:v1.9.0
            command:
            - /bin/sh
            - -c
            - |
              velero backup describe daily-backup
              velero backup logs daily-backup
              velero backup download daily-backup
          restartPolicy: OnFailure
---
apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: backup-alerts
  namespace: monitoring
spec:
  groups:
  - name: backup.rules
    rules:
    - alert: BackupFailed
      expr: velero_backup_failed_total > 0
      for: 1h
      labels:
        severity: critical
      annotations:
        summary: Backup has failed
        description: "Backup {{ $labels.backup }} has failed"
    - alert: BackupVerificationFailed
      expr: velero_backup_verification_failed_total > 0
      for: 1h
      labels:
        severity: critical
      annotations:
        summary: Backup verification has failed
        description: "Backup verification for {{ $labels.backup }} has failed" 