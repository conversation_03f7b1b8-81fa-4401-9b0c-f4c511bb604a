apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: default-deny-all
  namespace: tenant-test-tenant-maintenance
spec:
  podSelector: {}
  policyTypes:
  - Ingress
  - Egress
---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: allow-frontend-to-backend
  namespace: tenant-test-tenant-maintenance
spec:
  podSelector:
    matchLabels:
      app: test-tenant-maintenance-backend
  policyTypes:
  - Ingress
  ingress:
  - from:
    - podSelector:
        matchLabels:
          app: test-tenant-maintenance-frontend
    ports:
    - protocol: TCP
      port: 8080
---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: allow-backend-to-rabbitmq
  namespace: tenant-test-tenant-maintenance
spec:
  podSelector:
    matchLabels:
      app: test-tenant-maintenance-rabbitmq
  policyTypes:
  - Ingress
  ingress:
  - from:
    - podSelector:
        matchLabels:
          app: test-tenant-maintenance-backend
    ports:
    - protocol: TCP
      port: 5672
    - protocol: TCP
      port: 15672
---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: allow-backend-egress
  namespace: tenant-test-tenant-maintenance
spec:
  podSelector:
    matchLabels:
      app: test-tenant-maintenance-backend
  policyTypes:
  - Egress
  egress:
  - to: []
    ports:
    - protocol: TCP
      port: 3306
    - protocol: TCP
      port: 443
    - protocol: TCP
      port: 80
---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: allow-rabbitmq-management
  namespace: tenant-test-tenant-maintenance
spec:
  podSelector:
    matchLabels:
      app: test-tenant-maintenance-rabbitmq
  policyTypes:
  - Ingress
  ingress:
  - from:
    - podSelector:
        matchLabels:
          app: test-tenant-maintenance-backend
    ports:
    - protocol: TCP
      port: 15672 