#!/bin/bash

# Comprehensive Tenant Component Verification Script
# Usage: ./verify-tenant-components.sh <tenant-id>

set -e

TENANT_ID=${1:-test-verify}
NAMESPACE="tenant-${TENANT_ID}"

echo "🔍 Comprehensive Component Verification for Tenant: ${TENANT_ID}"
echo "=================================================="

# Function to print status
print_status() {
    local status=$1
    local message=$2
    if [ "$status" = "✅" ]; then
        echo -e "${status} ${message}"
    else
        echo -e "${status} ${message}"
    fi
}

# 1. Check namespace exists
echo -e "\n📋 1. Namespace Verification"
if kubectl get namespace $NAMESPACE >/dev/null 2>&1; then
    print_status "✅" "Namespace $NAMESPACE exists"
else
    print_status "❌" "Namespace $NAMESPACE not found"
    exit 1
fi

# 2. Check all pods are running
echo -e "\n🐳 2. Pod Status Verification"
PODS=$(kubectl get pods -n $NAMESPACE --no-headers -o custom-columns=":metadata.name")
ALL_RUNNING=true

for pod in $PODS; do
    STATUS=$(kubectl get pod $pod -n $NAMESPACE -o jsonpath='{.status.phase}')
    READY=$(kubectl get pod $pod -n $NAMESPACE -o jsonpath='{.status.containerStatuses[*].ready}' | tr ' ' '\n' | grep -c "true" || echo "0")
    TOTAL=$(kubectl get pod $pod -n $NAMESPACE -o jsonpath='{.status.containerStatuses[*].ready}' | tr ' ' '\n' | wc -l)
    
    if [ "$STATUS" = "Running" ] && [ "$READY" = "$TOTAL" ]; then
        print_status "✅" "Pod $pod: Running ($READY/$TOTAL ready)"
    else
        print_status "❌" "Pod $pod: $STATUS ($READY/$TOTAL ready)"
        ALL_RUNNING=false
    fi
done

# 3. Check all services
echo -e "\n🌐 3. Service Verification"
SERVICES=$(kubectl get services -n $NAMESPACE --no-headers -o custom-columns=":metadata.name")
for service in $SERVICES; do
    if kubectl get service $service -n $NAMESPACE >/dev/null 2>&1; then
        print_status "✅" "Service $service exists"
    else
        print_status "❌" "Service $service not found"
    fi
done

# 4. Check deployments
echo -e "\n🚀 4. Deployment Verification"
DEPLOYMENTS=$(kubectl get deployments -n $NAMESPACE --no-headers -o custom-columns=":metadata.name")
for deployment in $DEPLOYMENTS; do
    READY=$(kubectl get deployment $deployment -n $NAMESPACE -o jsonpath='{.status.readyReplicas}')
    DESIRED=$(kubectl get deployment $deployment -n $NAMESPACE -o jsonpath='{.spec.replicas}')
    if [ "$READY" = "$DESIRED" ]; then
        print_status "✅" "Deployment $deployment: Ready ($READY/$DESIRED)"
    else
        print_status "❌" "Deployment $deployment: Not ready ($READY/$DESIRED)"
    fi
done

# 5. Check Istio VirtualService
echo -e "\n🔗 5. Istio VirtualService Verification"
if kubectl get virtualservice -n $NAMESPACE >/dev/null 2>&1; then
    print_status "✅" "Istio VirtualService exists"
    kubectl get virtualservice -n $NAMESPACE -o custom-columns="NAME:.metadata.name,HOSTS:.spec.hosts[0]"
else
    print_status "❌" "Istio VirtualService not found"
fi

# 6. Check autoscaling (KEDA ScaledObjects)
echo -e "\n📈 6. Autoscaling Verification"
if kubectl get scaledobject -n $NAMESPACE >/dev/null 2>&1; then
    print_status "✅" "KEDA ScaledObjects exist"
    kubectl get scaledobject -n $NAMESPACE -o custom-columns="NAME:.metadata.name,TARGET:.spec.scaleTargetRef.name,MIN:.spec.minReplicaCount,MAX:.spec.maxReplicaCount"
else
    print_status "❌" "KEDA ScaledObjects not found"
fi

# 7. Check database connectivity
echo -e "\n🗄️ 7. Database Connectivity Verification"
BACKEND_POD=$(kubectl get pods -n $NAMESPACE -l app=${TENANT_ID}-backend --field-selector=status.phase=Running -o jsonpath='{.items[0].metadata.name}')
if [ -n "$BACKEND_POD" ]; then
    if kubectl exec -n $NAMESPACE $BACKEND_POD -c backend -- php -r "
        try {
            \$dsn = 'mysql:host=' . getenv('DB_HOST') . ';port=' . getenv('DB_PORT') . ';dbname=' . getenv('DB_NAME') . ';charset=utf8mb4';
            \$options = [PDO::MYSQL_ATTR_SSL_CA => getenv('DB_SSL_CA'), PDO::MYSQL_ATTR_SSL_VERIFY_SERVER_CERT => false];
            \$pdo = new PDO(\$dsn, getenv('DB_USER'), getenv('DB_PASSWORD'), \$options);
            echo 'SUCCESS';
        } catch (Exception \$e) {
            echo 'FAILED: ' . \$e->getMessage();
        }
    " | grep -q "SUCCESS"; then
        print_status "✅" "Database connectivity successful"
    else
        print_status "❌" "Database connectivity failed"
    fi
else
    print_status "❌" "Backend pod not found"
fi

# 8. Check S3 mount functionality
echo -e "\n📁 8. S3 Mount Verification"
if [ -n "$BACKEND_POD" ]; then
    # Test S3 mount write access
    TEST_FILE="/storage/clear/verification-test-$(date +%s).txt"
    if kubectl exec -n $NAMESPACE $BACKEND_POD -c backend -- sh -c "echo 'S3 mount verification test' > $TEST_FILE && cat $TEST_FILE" >/dev/null 2>&1; then
        print_status "✅" "S3 mount write/read access working"
    else
        print_status "❌" "S3 mount write/read access failed"
    fi
    
    # Check S3 directory structure
    if kubectl exec -n $NAMESPACE $BACKEND_POD -c backend -- ls -la /storage/clear/ | grep -q "assets\|ims-import\|logo\|quarantine\|tmp"; then
        print_status "✅" "S3 directory structure correct"
    else
        print_status "❌" "S3 directory structure incorrect"
    fi
else
    print_status "❌" "Backend pod not found for S3 test"
fi

# 9. Check S3 bucket structure
echo -e "\n🪣 9. S3 Bucket Structure Verification"
if aws s3 ls s3://architravetestdb/${TENANT_ID}/ >/dev/null 2>&1; then
    print_status "✅" "S3 bucket directory exists"
    echo "   Directory structure:"
    aws s3 ls s3://architravetestdb/${TENANT_ID}/ --recursive | head -10
else
    print_status "❌" "S3 bucket directory not found"
fi

# 10. Check RabbitMQ connectivity
echo -e "\n🐰 10. RabbitMQ Verification"
RABBITMQ_POD=$(kubectl get pods -n $NAMESPACE -l app=${TENANT_ID}-rabbitmq --field-selector=status.phase=Running -o jsonpath='{.items[0].metadata.name}')
if [ -n "$RABBITMQ_POD" ]; then
    if kubectl exec -n $NAMESPACE $RABBITMQ_POD -- rabbitmqctl status >/dev/null 2>&1; then
        print_status "✅" "RabbitMQ is running"
    else
        print_status "❌" "RabbitMQ is not responding"
    fi
else
    print_status "❌" "RabbitMQ pod not found"
fi

# 11. Check frontend accessibility
echo -e "\n🎨 11. Frontend Accessibility Verification"
FRONTEND_POD=$(kubectl get pods -n $NAMESPACE -l app=${TENANT_ID}-frontend --field-selector=status.phase=Running -o jsonpath='{.items[0].metadata.name}')
if [ -n "$FRONTEND_POD" ]; then
    if kubectl exec -n $NAMESPACE $FRONTEND_POD -c nginx -- curl -s -f http://localhost:80/ >/dev/null 2>&1; then
        print_status "✅" "Frontend nginx is responding"
    else
        print_status "❌" "Frontend nginx is not responding"
    fi
else
    print_status "❌" "Frontend pod not found"
fi

# 12. Check backend API accessibility
echo -e "\n🔧 12. Backend API Verification"
if [ -n "$BACKEND_POD" ]; then
    HTTP_CODE=$(kubectl exec -n $NAMESPACE $BACKEND_POD -c nginx -- curl -s -w "%{http_code}" http://localhost:8080/api/ -o /dev/null)
    if [ "$HTTP_CODE" = "200" ] || [ "$HTTP_CODE" = "500" ]; then
        print_status "✅" "Backend API is responding (HTTP $HTTP_CODE)"
    else
        print_status "❌" "Backend API is not responding (HTTP $HTTP_CODE)"
    fi
else
    print_status "❌" "Backend pod not found"
fi

# 13. Check resource usage
echo -e "\n📊 13. Resource Usage Verification"
echo "   Current resource usage:"
kubectl top pods -n $NAMESPACE --no-headers | while read line; do
    POD_NAME=$(echo $line | awk '{print $1}')
    CPU=$(echo $line | awk '{print $2}')
    MEMORY=$(echo $line | awk '{print $3}')
    echo "   $POD_NAME: CPU=$CPU, Memory=$MEMORY"
done

# 14. Check security context
echo -e "\n🛡️ 14. Security Context Verification"
if kubectl get pods -n $NAMESPACE -o jsonpath='{.items[*].spec.securityContext.runAsNonRoot}' | grep -q "true"; then
    print_status "✅" "Security context: runAsNonRoot enabled"
else
    print_status "⚠️" "Security context: runAsNonRoot not enabled"
fi

# Summary
echo -e "\n=================================================="
echo "🎯 VERIFICATION SUMMARY"
echo "=================================================="

if [ "$ALL_RUNNING" = true ]; then
    print_status "✅" "All components are working correctly!"
    echo "   Tenant $TENANT_ID is fully operational"
else
    print_status "⚠️" "Some components have issues"
    echo "   Please check the detailed output above"
fi

echo -e "\n📋 Quick Access Information:"
echo "   Namespace: $NAMESPACE"
echo "   Domain: ${TENANT_ID}.architrave-assets.com"
echo "   S3 Bucket: architravetestdb/${TENANT_ID}/"
echo "   Database: architrave (shared)"
echo "   Autoscaling: KEDA enabled"

echo -e "\n🔗 Useful Commands:"
echo "   kubectl get pods -n $NAMESPACE"
echo "   kubectl logs -n $NAMESPACE <pod-name>"
echo "   kubectl exec -n $NAMESPACE <pod-name> -- bash"
echo "   aws s3 ls s3://architravetestdb/${TENANT_ID}/" 