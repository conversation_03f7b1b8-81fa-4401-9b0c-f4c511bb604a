apiVersion: v1
kind: ConfigMap
metadata:
  name: istio-validation-test-frontend-config
  namespace: tenant-istio-validation-test
  labels:
    app: istio-validation-test-frontend
    managed-by: advanced-tenant-onboard
data:
  default.conf: |
    server {
        listen 80;
        server_name istio-validation-test.architrave-assets.com;
        root /usr/share/nginx/html;
        index index.html index.htm;

        # Frontend static files
        location / {
            try_files $uri $uri/ /index.html;
            add_header Cache-Control "public, max-age=3600";
        }

        # Proxy API requests to backend service
        location /api/ {
            proxy_pass http://istio-validation-test-backend:8080/api/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_connect_timeout 30s;
            proxy_send_timeout 30s;
            proxy_read_timeout 30s;
        }

        # Health check endpoint
        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }

        # Security headers
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header X-XSS-Protection "1; mode=block" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header Referrer-Policy "no-referrer-when-downgrade" always;
        add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
    }
