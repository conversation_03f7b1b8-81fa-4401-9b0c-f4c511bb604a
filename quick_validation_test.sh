#!/bin/bash

# Quick Validation Test Script for Enhanced Tenant Onboarding
# This script performs basic validation of the critical fixes

set -e

echo "🚀 Starting Quick Validation Tests for Enhanced Tenant Onboarding"
echo "=================================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Test counter
TESTS_PASSED=0
TESTS_FAILED=0

# Function to print test results
print_result() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}✅ PASS${NC}: $2"
        ((TESTS_PASSED++))
    else
        echo -e "${RED}❌ FAIL${NC}: $2"
        ((TESTS_FAILED++))
    fi
}

# Test 1: Verify enhanced script compilation
echo -e "\n${YELLOW}Test 1: Enhanced Script Compilation${NC}"
if [ -f "./advanced_tenant_onboard_enhanced" ]; then
    print_result 0 "Enhanced script binary exists"
else
    print_result 1 "Enhanced script binary not found"
fi

# Test 2: Verify command-line interface
echo -e "\n${YELLOW}Test 2: Command-Line Interface Validation${NC}"
if ./advanced_tenant_onboard_enhanced --help > /dev/null 2>&1; then
    print_result 0 "Help command works correctly"
else
    print_result 1 "Help command failed"
fi

# Test 3: Verify required flags are present
echo -e "\n${YELLOW}Test 3: Required Flags Validation${NC}"
HELP_OUTPUT=$(./advanced_tenant_onboard_enhanced --help 2>&1)

# Check for critical flags
if echo "$HELP_OUTPUT" | grep -q "enable-auto-fix"; then
    print_result 0 "enable-auto-fix flag present"
else
    print_result 1 "enable-auto-fix flag missing"
fi

if echo "$HELP_OUTPUT" | grep -q "enable-hetzner-dns"; then
    print_result 0 "enable-hetzner-dns flag present"
else
    print_result 1 "enable-hetzner-dns flag missing"
fi

if echo "$HELP_OUTPUT" | grep -q "ssl-certificate-arn"; then
    print_result 0 "ssl-certificate-arn flag present"
else
    print_result 1 "ssl-certificate-arn flag missing"
fi

# Test 4: Verify AWS CLI access
echo -e "\n${YELLOW}Test 4: AWS CLI Access Validation${NC}"
if aws sts get-caller-identity > /dev/null 2>&1; then
    print_result 0 "AWS CLI access working"
else
    print_result 1 "AWS CLI access failed - check credentials"
fi

# Test 5: Verify kubectl access
echo -e "\n${YELLOW}Test 5: Kubernetes Access Validation${NC}"
if kubectl cluster-info > /dev/null 2>&1; then
    print_result 0 "kubectl access working"
else
    print_result 1 "kubectl access failed - check kubeconfig"
fi

# Test 6: Verify RDS connectivity (from cluster)
echo -e "\n${YELLOW}Test 6: RDS Connectivity Test${NC}"
RDS_TEST_POD="rds-connectivity-test-$(date +%s)"
kubectl run $RDS_TEST_POD --image=mysql:8.0 --rm -i --restart=Never --timeout=60s -- \
  mysql -h production-aurora-serverless.cluster-cpmagwki2kv8.eu-central-1.rds.amazonaws.com \
  -P 3306 -u admin -p$(aws secretsmanager get-secret-value --secret-id production/rds/master-new --query SecretString --output text | jq -r '.password') \
  architrave -e "SELECT 1 as connection_test;" > /dev/null 2>&1

if [ $? -eq 0 ]; then
    print_result 0 "RDS connectivity from cluster working"
else
    print_result 1 "RDS connectivity from cluster failed"
fi

# Test 7: Verify S3 access
echo -e "\n${YELLOW}Test 7: S3 Access Validation${NC}"
if aws s3 ls s3://architravetestdb/architrave_1.45.2.sql > /dev/null 2>&1; then
    print_result 0 "S3 SQL file access working"
else
    print_result 1 "S3 SQL file access failed"
fi

# Test 8: Verify SQL file integrity
echo -e "\n${YELLOW}Test 8: SQL File Integrity Check${NC}"
TEMP_SQL_FILE="/tmp/test-architrave-schema.sql"
aws s3 cp s3://architravetestdb/architrave_1.45.2.sql $TEMP_SQL_FILE > /dev/null 2>&1

if [ -f "$TEMP_SQL_FILE" ]; then
    FILE_SIZE=$(stat -f%z "$TEMP_SQL_FILE" 2>/dev/null || stat -c%s "$TEMP_SQL_FILE" 2>/dev/null)
    if [ "$FILE_SIZE" -gt 1024 ]; then
        print_result 0 "SQL file downloaded and has reasonable size ($FILE_SIZE bytes)"
    else
        print_result 1 "SQL file too small ($FILE_SIZE bytes)"
    fi
    
    # Check for required content
    if grep -q "CREATE TABLE" "$TEMP_SQL_FILE" && grep -q "user_roles" "$TEMP_SQL_FILE"; then
        print_result 0 "SQL file contains required CREATE TABLE and user_roles content"
    else
        print_result 1 "SQL file missing required content"
    fi
    
    rm -f "$TEMP_SQL_FILE"
else
    print_result 1 "Failed to download SQL file from S3"
fi

# Test 9: Verify ECR image access
echo -e "\n${YELLOW}Test 9: ECR Image Access Validation${NC}"
if aws ecr describe-images --repository-name webapp_dev --image-ids imageTag=2.0.56-test > /dev/null 2>&1; then
    print_result 0 "ECR backend image access working"
else
    print_result 1 "ECR backend image access failed"
fi

# Test 10: Dry-run validation with minimal flags
echo -e "\n${YELLOW}Test 10: Dry-Run Validation${NC}"
# This test would require a more sophisticated approach to avoid actual resource creation
# For now, we'll just validate that the script accepts the target command structure
TARGET_COMMAND="./advanced_tenant_onboard_enhanced --tenant-id validation-test --tenant-name validation-test --domain architrave-assets.com --enable-auto-fix --enable-production-audit --enable-hetzner-dns --ssl-certificate-arn arn:aws:acm:eu-central-1:************:certificate/8d306c2f-62e2-42f4-a941-562ce65cda32 --aws-region eu-central-1 --aws-account-id ************ --hetzner-zone architrave-assets.com --help"

if $TARGET_COMMAND > /dev/null 2>&1; then
    print_result 0 "Target command structure validation passed"
else
    print_result 1 "Target command structure validation failed"
fi

# Summary
echo -e "\n${YELLOW}===============================================${NC}"
echo -e "${YELLOW}VALIDATION SUMMARY${NC}"
echo -e "${YELLOW}===============================================${NC}"
echo -e "${GREEN}Tests Passed: $TESTS_PASSED${NC}"
echo -e "${RED}Tests Failed: $TESTS_FAILED${NC}"

if [ $TESTS_FAILED -eq 0 ]; then
    echo -e "\n${GREEN}🎉 ALL VALIDATION TESTS PASSED!${NC}"
    echo -e "${GREEN}✅ Enhanced tenant onboarding script is ready for production testing${NC}"
    echo -e "\n${YELLOW}Next Steps:${NC}"
    echo "1. Execute the full target command in a test environment"
    echo "2. Monitor logs for 'CRITICAL SUCCESS' messages"
    echo "3. Validate all components using the comprehensive testing strategy"
    echo "4. Perform failure scenario testing"
    exit 0
else
    echo -e "\n${RED}⚠️  SOME VALIDATION TESTS FAILED${NC}"
    echo -e "${RED}Please address the failed tests before proceeding with production testing${NC}"
    exit 1
fi
