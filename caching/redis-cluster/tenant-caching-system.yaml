---
# Tenant-Specific Redis Caching System
apiVersion: v1
kind: Namespace
metadata:
  name: caching-system
  labels:
    pod-security.kubernetes.io/enforce: restricted
    pod-security.kubernetes.io/audit: restricted
    pod-security.kubernetes.io/warn: restricted
---
# Redis Cluster for Multi-Tenant Caching
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: redis-cluster
  namespace: caching-system
  labels:
    app: redis-cluster
spec:
  serviceName: redis-cluster
  replicas: 6
  selector:
    matchLabels:
      app: redis-cluster
  template:
    metadata:
      labels:
        app: redis-cluster
    spec:
      securityContext:
        runAsNonRoot: true
        runAsUser: 999
        runAsGroup: 999
        fsGroup: 999
        seccompProfile:
          type: RuntimeDefault
      containers:
      - name: redis
        image: redis:7-alpine
        ports:
        - containerPort: 6379
          name: redis
        - containerPort: 16379
          name: cluster
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          capabilities:
            drop: ["ALL"]
        resources:
          requests:
            memory: "512Mi"
            cpu: "200m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        command:
        - redis-server
        - /etc/redis/redis.conf
        volumeMounts:
        - name: redis-config
          mountPath: /etc/redis
        - name: redis-data
          mountPath: /data
        - name: tmp
          mountPath: /tmp
        env:
        - name: REDIS_CLUSTER_ANNOUNCE_IP
          valueFrom:
            fieldRef:
              fieldPath: status.podIP
        livenessProbe:
          exec:
            command:
            - redis-cli
            - ping
          initialDelaySeconds: 30
          periodSeconds: 30
        readinessProbe:
          exec:
            command:
            - redis-cli
            - ping
          initialDelaySeconds: 5
          periodSeconds: 10
      volumes:
      - name: redis-config
        configMap:
          name: redis-config
      - name: tmp
        emptyDir: {}
  volumeClaimTemplates:
  - metadata:
      name: redis-data
    spec:
      accessModes: ["ReadWriteOnce"]
      resources:
        requests:
          storage: 10Gi
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: redis-config
  namespace: caching-system
data:
  redis.conf: |
    # Redis Cluster Configuration
    port 6379
    cluster-enabled yes
    cluster-config-file nodes.conf
    cluster-node-timeout 5000
    appendonly yes
    appendfsync everysec
    
    # Security
    protected-mode yes
    requirepass "redis-cluster-password"
    masterauth "redis-cluster-password"
    
    # Memory Management
    maxmemory 800mb
    maxmemory-policy allkeys-lru
    
    # Persistence
    save 900 1
    save 300 10
    save 60 10000
    
    # Logging
    loglevel notice
    logfile ""
    
    # Network
    tcp-keepalive 300
    timeout 0
    
    # Performance
    tcp-backlog 511
    databases 16
    
    # Tenant Isolation
    # Each tenant will use a different database number
    # Database 0-15 available for tenant isolation
---
apiVersion: v1
kind: Service
metadata:
  name: redis-cluster
  namespace: caching-system
  labels:
    app: redis-cluster
spec:
  ports:
  - port: 6379
    targetPort: 6379
    name: redis
  - port: 16379
    targetPort: 16379
    name: cluster
  clusterIP: None
  selector:
    app: redis-cluster
---
# Cache Manager for Tenant-Specific Caching
apiVersion: apps/v1
kind: Deployment
metadata:
  name: cache-manager
  namespace: caching-system
  labels:
    app: cache-manager
spec:
  replicas: 2
  selector:
    matchLabels:
      app: cache-manager
  template:
    metadata:
      labels:
        app: cache-manager
    spec:
      serviceAccountName: cache-manager
      securityContext:
        runAsNonRoot: true
        runAsUser: 65534
        runAsGroup: 65534
        fsGroup: 65534
        seccompProfile:
          type: RuntimeDefault
      containers:
      - name: cache-manager
        image: python:3.11-alpine
        command: ["/bin/sh", "-c"]
        args:
        - |
          pip install redis flask kubernetes prometheus_client
          cat > /app/cache_manager.py << 'EOF'
          #!/usr/bin/env python3
          import redis
          import json
          import time
          import hashlib
          from flask import Flask, request, jsonify
          from kubernetes import client, config
          from prometheus_client import Counter, Histogram, Gauge, start_http_server
          
          app = Flask(__name__)
          
          # Prometheus metrics
          cache_hits = Counter('cache_hits_total', 'Total cache hits', ['tenant'])
          cache_misses = Counter('cache_misses_total', 'Total cache misses', ['tenant'])
          cache_operations = Histogram('cache_operation_duration_seconds', 'Cache operation duration', ['tenant', 'operation'])
          cache_memory_usage = Gauge('cache_memory_usage_bytes', 'Cache memory usage', ['tenant'])
          
          # Redis connection pool
          redis_pool = redis.ConnectionPool(
              host='redis-cluster.caching-system.svc.cluster.local',
              port=6379,
              password='redis-cluster-password',
              decode_responses=True,
              max_connections=100
          )
          
          # Load Kubernetes config
          try:
              config.load_incluster_config()
          except:
              config.load_kube_config()
          
          def get_tenant_redis_db(tenant_id):
              """Get Redis database number for tenant (0-15)"""
              tenant_hash = hashlib.md5(tenant_id.encode()).hexdigest()
              return int(tenant_hash[:2], 16) % 16
          
          def get_redis_client(tenant_id):
              """Get Redis client for specific tenant"""
              db_num = get_tenant_redis_db(tenant_id)
              return redis.Redis(connection_pool=redis_pool, db=db_num)
          
          @app.route('/cache/<tenant_id>/<key>', methods=['GET'])
          def get_cache(tenant_id, key):
              with cache_operations.labels(tenant=tenant_id, operation='get').time():
                  try:
                      r = get_redis_client(tenant_id)
                      value = r.get(key)
                      
                      if value:
                          cache_hits.labels(tenant=tenant_id).inc()
                          return jsonify({'key': key, 'value': value, 'hit': True})
                      else:
                          cache_misses.labels(tenant=tenant_id).inc()
                          return jsonify({'key': key, 'value': None, 'hit': False}), 404
                  
                  except Exception as e:
                      return jsonify({'error': str(e)}), 500
          
          @app.route('/cache/<tenant_id>/<key>', methods=['POST'])
          def set_cache(tenant_id, key):
              with cache_operations.labels(tenant=tenant_id, operation='set').time():
                  try:
                      data = request.get_json()
                      value = data.get('value')
                      ttl = data.get('ttl', 3600)  # Default 1 hour TTL
                      
                      r = get_redis_client(tenant_id)
                      r.setex(key, ttl, value)
                      
                      return jsonify({'key': key, 'value': value, 'ttl': ttl, 'status': 'set'})
                  
                  except Exception as e:
                      return jsonify({'error': str(e)}), 500
          
          @app.route('/cache/<tenant_id>/<key>', methods=['DELETE'])
          def delete_cache(tenant_id, key):
              with cache_operations.labels(tenant=tenant_id, operation='delete').time():
                  try:
                      r = get_redis_client(tenant_id)
                      deleted = r.delete(key)
                      
                      return jsonify({'key': key, 'deleted': bool(deleted)})
                  
                  except Exception as e:
                      return jsonify({'error': str(e)}), 500
          
          @app.route('/cache/<tenant_id>/flush', methods=['POST'])
          def flush_tenant_cache(tenant_id):
              with cache_operations.labels(tenant=tenant_id, operation='flush').time():
                  try:
                      r = get_redis_client(tenant_id)
                      r.flushdb()
                      
                      return jsonify({'tenant_id': tenant_id, 'status': 'flushed'})
                  
                  except Exception as e:
                      return jsonify({'error': str(e)}), 500
          
          @app.route('/cache/<tenant_id>/stats', methods=['GET'])
          def get_cache_stats(tenant_id):
              try:
                  r = get_redis_client(tenant_id)
                  info = r.info()
                  
                  stats = {
                      'tenant_id': tenant_id,
                      'database': get_tenant_redis_db(tenant_id),
                      'keys': r.dbsize(),
                      'memory_usage': info.get('used_memory', 0),
                      'hits': info.get('keyspace_hits', 0),
                      'misses': info.get('keyspace_misses', 0),
                      'expired_keys': info.get('expired_keys', 0),
                      'evicted_keys': info.get('evicted_keys', 0)
                  }
                  
                  # Update Prometheus metrics
                  cache_memory_usage.labels(tenant=tenant_id).set(stats['memory_usage'])
                  
                  return jsonify(stats)
              
              except Exception as e:
                  return jsonify({'error': str(e)}), 500
          
          @app.route('/cache/<tenant_id>/keys', methods=['GET'])
          def list_cache_keys(tenant_id):
              try:
                  r = get_redis_client(tenant_id)
                  pattern = request.args.get('pattern', '*')
                  keys = r.keys(pattern)
                  
                  return jsonify({'tenant_id': tenant_id, 'keys': keys, 'count': len(keys)})
              
              except Exception as e:
                  return jsonify({'error': str(e)}), 500
          
          @app.route('/health', methods=['GET'])
          def health_check():
              try:
                  # Test Redis connection
                  r = redis.Redis(connection_pool=redis_pool, db=0)
                  r.ping()
                  
                  return jsonify({'status': 'healthy', 'redis': 'connected'})
              
              except Exception as e:
                  return jsonify({'status': 'unhealthy', 'error': str(e)}), 500
          
          def update_metrics():
              """Background task to update metrics"""
              while True:
                  try:
                      # Get all tenant namespaces
                      v1 = client.CoreV1Api()
                      namespaces = v1.list_namespace()
                      
                      for ns in namespaces.items:
                          if ns.metadata.name.startswith('tenant-'):
                              tenant_id = ns.metadata.name.replace('tenant-', '')
                              
                              try:
                                  r = get_redis_client(tenant_id)
                                  info = r.info()
                                  cache_memory_usage.labels(tenant=tenant_id).set(info.get('used_memory', 0))
                              except:
                                  pass
                      
                      time.sleep(60)  # Update every minute
                  except Exception as e:
                      print(f"Error updating metrics: {e}")
                      time.sleep(60)
          
          if __name__ == '__main__':
              # Start Prometheus metrics server
              start_http_server(8000)
              
              # Start metrics update thread
              import threading
              metrics_thread = threading.Thread(target=update_metrics, daemon=True)
              metrics_thread.start()
              
              # Start Flask app
              app.run(host='0.0.0.0', port=8080)
          EOF
          
          python /app/cache_manager.py
        ports:
        - containerPort: 8080
          name: http
        - containerPort: 8000
          name: metrics
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          capabilities:
            drop: ["ALL"]
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "200m"
        volumeMounts:
        - name: tmp
          mountPath: /tmp
        - name: app
          mountPath: /app
        env:
        - name: REDIS_HOST
          value: "redis-cluster.caching-system.svc.cluster.local"
        - name: REDIS_PORT
          value: "6379"
        - name: REDIS_PASSWORD
          value: "redis-cluster-password"
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 10
      volumes:
      - name: tmp
        emptyDir: {}
      - name: app
        emptyDir: {}
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: cache-manager
  namespace: caching-system
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: cache-manager
rules:
- apiGroups: [""]
  resources: ["namespaces"]
  verbs: ["get", "list", "watch"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: cache-manager
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: cache-manager
subjects:
- kind: ServiceAccount
  name: cache-manager
  namespace: caching-system
---
apiVersion: v1
kind: Service
metadata:
  name: cache-manager
  namespace: caching-system
  labels:
    app: cache-manager
spec:
  ports:
  - port: 8080
    targetPort: 8080
    name: http
  - port: 8000
    targetPort: 8000
    name: metrics
  selector:
    app: cache-manager
---
# Cache Cleanup CronJob
apiVersion: batch/v1
kind: CronJob
metadata:
  name: cache-cleanup
  namespace: caching-system
spec:
  schedule: "0 2 * * *"  # Daily at 2 AM
  jobTemplate:
    spec:
      template:
        spec:
          serviceAccountName: cache-cleanup
          securityContext:
            runAsNonRoot: true
            runAsUser: 65534
            runAsGroup: 65534
            fsGroup: 65534
            seccompProfile:
              type: RuntimeDefault
          containers:
          - name: cleanup
            image: redis:7-alpine
            command: ["/bin/sh", "-c"]
            args:
            - |
              # Connect to Redis and clean up expired keys
              for db in {0..15}; do
                echo "Cleaning database $db..."
                redis-cli -h redis-cluster.caching-system.svc.cluster.local -p 6379 -a redis-cluster-password -n $db --scan --pattern "*" | head -1000 | xargs -r redis-cli -h redis-cluster.caching-system.svc.cluster.local -p 6379 -a redis-cluster-password -n $db TTL | grep -E "^-1$" | wc -l
              done
              echo "Cache cleanup completed"
            securityContext:
              allowPrivilegeEscalation: false
              readOnlyRootFilesystem: true
              capabilities:
                drop: ["ALL"]
            resources:
              requests:
                memory: "64Mi"
                cpu: "50m"
              limits:
                memory: "128Mi"
                cpu: "100m"
          restartPolicy: OnFailure
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: cache-cleanup
  namespace: caching-system
