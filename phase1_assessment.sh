#!/bin/bash
# Phase 1: Infrastructure Assessment Script
set -e

echo "🔍 PHASE 1: INFRASTRUCTURE ASSESSMENT"
echo "====================================="
echo ""

echo "Step 1.1: Checking Istio Gateway status..."
kubectl get gateway tenant-gateway -n istio-system -o wide 2>/dev/null || echo "❌ tenant-gateway not found"
echo ""

echo "Step 1.2: Verifying Istio ingress gateway service..."
kubectl get svc istio-ingressgateway -n istio-system -o wide 2>/dev/null || echo "❌ istio-ingressgateway service not found"
echo ""

echo "Step 1.3: Checking all Istio gateways..."
kubectl get gateway --all-namespaces 2>/dev/null || echo "No Gateway resources found"
echo ""

echo "Step 1.4: Checking conflicting Kubernetes Ingress resources..."
kubectl get ingress --all-namespaces 2>/dev/null || echo "No Ingress resources found"
echo ""

echo "Step 1.5: Checking AWS Load Balancer Controller..."
kubectl get deployment aws-load-balancer-controller -n kube-system 2>/dev/null || echo "AWS Load Balancer Controller not installed"
echo ""

echo "Step 1.6: Identifying existing tenant namespaces..."
TENANT_NAMESPACES=$(kubectl get namespaces | grep "tenant-" | awk '{print $1}' 2>/dev/null || echo "")
if [ -z "$TENANT_NAMESPACES" ]; then
    echo "❌ No tenant namespaces found"
else
    echo "✅ Found tenant namespaces:"
    echo "$TENANT_NAMESPACES"
fi
echo ""

echo "Step 1.7: Checking existing VirtualService resources..."
kubectl get virtualservice --all-namespaces 2>/dev/null || echo "No VirtualService resources found"
echo ""

echo "Step 1.8: Checking Istio system namespace and components..."
kubectl get namespace istio-system 2>/dev/null || echo "❌ istio-system namespace not found"
echo ""
kubectl get pods -n istio-system 2>/dev/null || echo "❌ No Istio pods found"
echo ""

echo "Step 1.9: Checking Istio ingress gateway external endpoint..."
EXTERNAL_IP=$(kubectl get svc istio-ingressgateway -n istio-system -o jsonpath='{.status.loadBalancer.ingress[0].hostname}' 2>/dev/null || echo "")
if [ -z "$EXTERNAL_IP" ]; then
    EXTERNAL_IP=$(kubectl get svc istio-ingressgateway -n istio-system -o jsonpath='{.status.loadBalancer.ingress[0].ip}' 2>/dev/null || echo "")
fi

if [ -z "$EXTERNAL_IP" ]; then
    echo "❌ No external IP/hostname found for Istio ingress gateway"
else
    echo "✅ Istio ingress gateway external endpoint: $EXTERNAL_IP"
fi
echo ""

echo "🏁 PHASE 1 ASSESSMENT COMPLETE"
echo "=============================="
