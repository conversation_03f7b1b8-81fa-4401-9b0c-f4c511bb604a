#!/bin/bash

# Exit on error
set -e

echo "🚀 Starting deployment of all components..."

# Check prerequisites
echo "🔍 Checking prerequisites..."

# Check kubectl
if ! command -v kubectl &> /dev/null; then
    echo "❌ kubectl is not installed"
    exit 1
fi

# Check velero
if ! command -v velero &> /dev/null; then
    echo "❌ velero is not installed"
    exit 1
fi

# Check AWS CLI
if ! command -v aws &> /dev/null; then
    echo "❌ AWS CLI is not installed"
    exit 1
fi

# Check jq
if ! command -v jq &> /dev/null; then
    echo "❌ jq is not installed"
    exit 1
fi

# Create necessary directories
echo "📁 Creating directories..."
mkdir -p monitoring backup testing

# Check AWS credentials
echo "🔑 Checking AWS credentials..."
if [ ! -f "credentials-velero" ]; then
    echo "❌ AWS credentials file not found"
    echo "Please create credentials-velero file with AWS credentials"
    exit 1
fi

# Deploy backup components
echo "💾 Deploying backup components..."
./deploy-additional-components.sh

# Deploy monitoring components
echo "📊 Deploying monitoring components..."
./deploy-monitoring.sh

# Verify deployments
echo "✅ Verifying deployments..."

# Check Velero
echo "🔍 Checking Velero..."
kubectl wait --for=condition=ready pod -l app.kubernetes.io/name=velero -n velero --timeout=300s

# Check monitoring
echo "🔍 Checking monitoring..."
kubectl wait --for=condition=ready pod -l app=grafana -n monitoring --timeout=300s
kubectl wait --for=condition=ready pod -l app=prometheus -n monitoring --timeout=300s

# Check performance testing
echo "🔍 Checking performance testing..."
kubectl wait --for=condition=ready pod -l app=k6 -n testing --timeout=300s

# Print success message
echo "🎉 All components deployed successfully!"
echo "
Next steps:
1. Access Grafana dashboards:
   - Backup Monitoring: http://grafana.monitoring:3000/d/backup-monitoring
   - Performance Monitoring: http://grafana.monitoring:3000/d/performance-monitoring

2. Verify backup configuration:
   kubectl get schedule -n velero
   kubectl get backupstoragelocation -n velero

3. Run a test backup:
   velero backup create test-backup

4. Check monitoring setup:
   kubectl get prometheusrules -n monitoring
   kubectl get servicemonitor -n monitoring

5. Run a performance test:
   kubectl create -f testing/performance-test.yaml

For detailed documentation, see docs/DEPLOYMENT_GUIDE.md
For quick reference, see docs/QUICK_START.md
" 