#!/bin/bash
# Advanced Tenant Management System Deployment Script
# This script deploys a comprehensive tenant management system for 100+ tenants

set -e

# Configuration
NAMESPACE="tenant-management"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    local level=$1
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    case $level in
        "INFO")
            echo -e "${GREEN}[${timestamp}] [INFO]${NC} $message"
            ;;
        "WARN")
            echo -e "${YELLOW}[${timestamp}] [WARN]${NC} $message"
            ;;
        "ERROR")
            echo -e "${RED}[${timestamp}] [ERROR]${NC} $message"
            ;;
        "DEBUG")
            echo -e "${BLUE}[${timestamp}] [DEBUG]${NC} $message"
            ;;
    esac
}

# Function to check if kubectl is available
check_kubectl() {
    if ! command -v kubectl &> /dev/null; then
        log "ERROR" "kubectl is not installed or not in PATH"
        exit 1
    fi
    
    if ! kubectl cluster-info &> /dev/null; then
        log "ERROR" "Cannot connect to Kubernetes cluster"
        exit 1
    fi
    
    log "INFO" "Kubernetes cluster connection verified"
}

# Function to create namespace
create_namespace() {
    log "INFO" "Creating namespace: $NAMESPACE"
    
    if kubectl get namespace "$NAMESPACE" &> /dev/null; then
        log "WARN" "Namespace $NAMESPACE already exists"
    else
        kubectl create namespace "$NAMESPACE"
        log "INFO" "Namespace $NAMESPACE created successfully"
    fi
    
    # Label the namespace
    kubectl label namespace "$NAMESPACE" \
        app=advanced-tenant-management \
        version=2.0 \
        --overwrite
}

# Function to create PersistentVolumeClaim for database
create_database_pvc() {
    log "INFO" "Creating PersistentVolumeClaim for tenant event database"
    
    cat <<EOF | kubectl apply -f -
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: tenant-event-db-pvc
  namespace: $NAMESPACE
  labels:
    app: tenant-event-storage
    component: database
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 20Gi
  storageClassName: gp2
EOF
    
    log "INFO" "PersistentVolumeClaim created successfully"
}

# Function to deploy database and event storage
deploy_event_storage() {
    log "INFO" "Deploying tenant event storage system"
    
    if [[ -f "$SCRIPT_DIR/tenant-event-storage-system.yaml" ]]; then
        kubectl apply -f "$SCRIPT_DIR/tenant-event-storage-system.yaml"
        log "INFO" "Tenant event storage system deployed"
    else
        log "ERROR" "tenant-event-storage-system.yaml not found"
        exit 1
    fi
    
    # Wait for database to be ready
    log "INFO" "Waiting for database to be ready..."
    kubectl wait --for=condition=ready pod \
        -l app=tenant-event-storage,component=database \
        -n "$NAMESPACE" \
        --timeout=300s
    
    log "INFO" "Database is ready"
}

# Function to deploy event API
deploy_event_api() {
    log "INFO" "Deploying tenant event API"
    
    if [[ -f "$SCRIPT_DIR/tenant-event-api.yaml" ]]; then
        kubectl apply -f "$SCRIPT_DIR/tenant-event-api.yaml"
        log "INFO" "Tenant event API deployed"
    else
        log "ERROR" "tenant-event-api.yaml not found"
        exit 1
    fi
    
    # Wait for API to be ready
    log "INFO" "Waiting for API to be ready..."
    kubectl wait --for=condition=ready pod \
        -l app=tenant-event-api \
        -n "$NAMESPACE" \
        --timeout=300s
    
    log "INFO" "Event API is ready"
}

# Function to deploy advanced UI
deploy_advanced_ui() {
    log "INFO" "Deploying advanced tenant management UI"
    
    if [[ -f "$SCRIPT_DIR/enhanced-tenant-manager.yaml" ]]; then
        kubectl apply -f "$SCRIPT_DIR/enhanced-tenant-manager.yaml"
        log "INFO" "Advanced tenant management UI deployed"
    else
        log "ERROR" "enhanced-tenant-manager.yaml not found"
        exit 1
    fi
    
    # Wait for UI to be ready
    log "INFO" "Waiting for UI to be ready..."
    kubectl wait --for=condition=ready pod \
        -l app=advanced-tenant-manager \
        -n "$NAMESPACE" \
        --timeout=300s
    
    log "INFO" "Advanced UI is ready"
}

# Function to setup RBAC
setup_rbac() {
    log "INFO" "Setting up RBAC for tenant management"
    
    cat <<EOF | kubectl apply -f -
apiVersion: v1
kind: ServiceAccount
metadata:
  name: tenant-manager
  namespace: $NAMESPACE
  labels:
    app: advanced-tenant-management
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: tenant-manager
  labels:
    app: advanced-tenant-management
rules:
- apiGroups: [""]
  resources: ["namespaces", "pods", "services", "secrets", "configmaps", "persistentvolumeclaims"]
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]
- apiGroups: ["apps"]
  resources: ["deployments", "replicasets"]
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]
- apiGroups: ["networking.k8s.io"]
  resources: ["ingresses", "networkpolicies"]
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]
- apiGroups: ["tenant.architrave.io"]
  resources: ["tenants", "tenantdatabases"]
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: tenant-manager
  labels:
    app: advanced-tenant-management
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: tenant-manager
subjects:
- kind: ServiceAccount
  name: tenant-manager
  namespace: $NAMESPACE
EOF
    
    log "INFO" "RBAC setup completed"
}

# Function to install tenant event integration script
install_integration_script() {
    log "INFO" "Installing tenant event integration script"
    
    if [[ -f "$SCRIPT_DIR/tenant-event-integration.py" ]]; then
        # Create a ConfigMap with the integration script
        kubectl create configmap tenant-event-integration \
            --from-file="$SCRIPT_DIR/tenant-event-integration.py" \
            -n "$NAMESPACE" \
            --dry-run=client -o yaml | kubectl apply -f -
        
        log "INFO" "Tenant event integration script installed"
    else
        log "ERROR" "tenant-event-integration.py not found"
        exit 1
    fi
}

# Function to verify deployment
verify_deployment() {
    log "INFO" "Verifying deployment..."
    
    # Check all pods are running
    local pods_ready=true
    
    log "INFO" "Checking pod status..."
    kubectl get pods -n "$NAMESPACE" -o wide
    
    # Check database
    if ! kubectl get pod -l app=tenant-event-storage,component=database -n "$NAMESPACE" | grep -q Running; then
        log "ERROR" "Database pod is not running"
        pods_ready=false
    fi
    
    # Check API
    if ! kubectl get pod -l app=tenant-event-api -n "$NAMESPACE" | grep -q Running; then
        log "ERROR" "API pod is not running"
        pods_ready=false
    fi
    
    # Check UI
    if ! kubectl get pod -l app=advanced-tenant-manager -n "$NAMESPACE" | grep -q Running; then
        log "ERROR" "UI pod is not running"
        pods_ready=false
    fi
    
    if [[ "$pods_ready" == "true" ]]; then
        log "INFO" "All pods are running successfully"
    else
        log "ERROR" "Some pods are not running properly"
        return 1
    fi
    
    # Test API connectivity
    log "INFO" "Testing API connectivity..."
    local api_pod=$(kubectl get pod -l app=tenant-event-api -n "$NAMESPACE" -o jsonpath='{.items[0].metadata.name}')
    
    if kubectl exec -n "$NAMESPACE" "$api_pod" -- curl -s http://localhost:5000/health > /dev/null; then
        log "INFO" "API health check passed"
    else
        log "ERROR" "API health check failed"
        return 1
    fi
    
    log "INFO" "Deployment verification completed successfully"
}

# Function to display access information
display_access_info() {
    log "INFO" "Deployment completed successfully!"
    echo
    echo "=== ACCESS INFORMATION ==="
    echo
    
    # Get service information
    echo "Services:"
    kubectl get services -n "$NAMESPACE"
    echo
    
    # Get ingress information if available
    if kubectl get ingress -n "$NAMESPACE" &> /dev/null; then
        echo "Ingress:"
        kubectl get ingress -n "$NAMESPACE"
        echo
    fi
    
    echo "To access the Advanced Tenant Management UI:"
    echo "1. Port forward: kubectl port-forward -n $NAMESPACE svc/advanced-tenant-manager 8080:80"
    echo "2. Open browser: http://localhost:8080"
    echo
    
    echo "To access the Tenant Event API:"
    echo "1. Port forward: kubectl port-forward -n $NAMESPACE svc/tenant-event-api 8081:80"
    echo "2. API endpoint: http://localhost:8081"
    echo
    
    echo "To use the tenant event integration:"
    echo "1. Copy the integration script: kubectl cp $NAMESPACE/\$(kubectl get pod -l app=tenant-event-api -n $NAMESPACE -o jsonpath='{.items[0].metadata.name}'):/app/tenant-event-integration.py ./tenant-event-integration.py"
    echo "2. Run: python3 tenant-event-integration.py onboard --tenant-id test-tenant --tenant-name 'Test Tenant' --subdomain test"
    echo
    
    log "INFO" "Advanced Tenant Management System is ready for 100+ tenants!"
}

# Main deployment function
main() {
    log "INFO" "Starting Advanced Tenant Management System deployment"
    
    # Check prerequisites
    check_kubectl
    
    # Create namespace
    create_namespace
    
    # Setup RBAC
    setup_rbac
    
    # Create PVC for database
    create_database_pvc
    
    # Deploy components
    deploy_event_storage
    deploy_event_api
    deploy_advanced_ui
    
    # Install integration script
    install_integration_script
    
    # Verify deployment
    verify_deployment
    
    # Display access information
    display_access_info
}

# Handle script arguments
case "${1:-deploy}" in
    "deploy")
        main
        ;;
    "verify")
        verify_deployment
        ;;
    "cleanup")
        log "INFO" "Cleaning up Advanced Tenant Management System"
        kubectl delete namespace "$NAMESPACE" --ignore-not-found=true
        log "INFO" "Cleanup completed"
        ;;
    "status")
        log "INFO" "Checking Advanced Tenant Management System status"
        kubectl get all -n "$NAMESPACE"
        ;;
    *)
        echo "Usage: $0 [deploy|verify|cleanup|status]"
        echo "  deploy  - Deploy the complete system (default)"
        echo "  verify  - Verify the deployment"
        echo "  cleanup - Remove the complete system"
        echo "  status  - Show system status"
        exit 1
        ;;
esac
