<?php

$config = [
    'doctrine' => [
        'connection' => [
            'orm_default' => [
                'driverClass' => 'Doctrine\\DBAL\\Driver\\Mysqli\\Driver',
                'params' => [
                    'host' => getenv('DB_HOST') ?: getenv('MYSQL_HOST'),
                    'port' => getenv('DB_PORT') ?: '3306',
                    'user' => getenv('DB_USER') ?: getenv('MYSQL_USER'),
                    'dbname' => getenv('DB_NAME') ?: getenv('MYSQL_DATABASE'),
                    'password' => getenv('DB_PASSWORD') ?: getenv('MYSQL_PASSWORD'),
                    'charset' => 'utf8mb4',
                    'driverOptions' => [
                        21 => false, // MYSQLI_OPT_SSL_VERIFY_SERVER_CERT - MUST be boolean false for Aurora
                    ],
                ],
            ],
        ],
    ],
    'db' => [
        'driver' => 'pdo_mysql',
        'host' => getenv('DB_HOST') ?: getenv('MYSQL_HOST'),
        'port' => getenv('DB_PORT') ?: '3306',
        'dbname' => getenv('DB_NAME') ?: getenv('MYSQL_DATABASE'),
        'database' => getenv('DB_NAME') ?: getenv('MYSQL_DATABASE'),
        'user' => getenv('DB_USER') ?: getenv('MYSQL_USER'),
        'username' => getenv('DB_USER') ?: getenv('MYSQL_USER'),
        'password' => getenv('DB_PASSWORD') ?: getenv('MYSQL_PASSWORD'),
        'charset' => 'utf8mb4',
        'driverOptions' => [
            1009 => '/tmp/rds-ca-2019-root.pem', // PDO::MYSQL_ATTR_SSL_CA
            1014 => false, // PDO::MYSQL_ATTR_SSL_VERIFY_SERVER_CERT
        ],
    ],
    // CRITICAL FIX: Add missing databaseName field required by CLI
    'databaseName' => 'architrave',
];

return $config;
