apiVersion: networking.istio.io/v1beta1
kind: Gateway
metadata:
  name: tenant-gateway
  namespace: istio-system
spec:
  selector:
    istio: ingressgateway
  servers:
  # Original architrave.com configuration
  - hosts:
    - '*.architrave.com'
    port:
      name: http
      number: 80
      protocol: HTTP
  - hosts:
    - '*.architrave.com'
    port:
      name: https
      number: 443
      protocol: HTTPS
    tls:
      credentialName: architrave-tls
      mode: SIMPLE
  # New architrave-assets.com configuration
  - hosts:
    - '*.architrave-assets.com'
    port:
      name: http-assets
      number: 80
      protocol: HTTP
  - hosts:
    - '*.architrave-assets.com'
    port:
      name: https-assets
      number: 443
      protocol: HTTPS
    tls:
      credentialName: architrave-assets-wildcard-cert
      mode: SIMPLE
