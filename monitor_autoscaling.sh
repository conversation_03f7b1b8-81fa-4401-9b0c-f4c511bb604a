#!/bin/bash

# Enhanced Autoscaling Monitoring Script
# This script monitors the performance of the enhanced autoscaling system

set -e

echo "📊 ENHANCED AUTOSCALING MONITORING DASHBOARD"
echo "============================================"
echo ""

# Function to show cluster capacity
show_cluster_capacity() {
    echo "🏗️ CLUSTER CAPACITY STATUS"
    echo "=========================="
    
    # Node information
    echo "🔹 Node Status:"
    kubectl get nodes --no-headers | awk '{print $2}' | sort | uniq -c | while read count status; do
        echo "   $status: $count nodes"
    done
    
    echo ""
    echo "🔹 Node Details:"
    kubectl get nodes -o custom-columns="NAME:.metadata.name,STATUS:.status.conditions[?(@.type=='Ready')].status,INSTANCE:.metadata.labels.node\.kubernetes\.io/instance-type,ZONE:.metadata.labels.topology\.kubernetes\.io/zone" --no-headers | while read name status instance zone; do
        echo "   $name: $status ($instance in $zone)"
    done
    
    echo ""
}

# Function to show autoscaler status
show_autoscaler_status() {
    echo "⚙️ AUTOSCALER STATUS"
    echo "==================="
    
    # Cluster Autoscaler
    echo "🔹 Cluster Autoscaler:"
    if kubectl get deployment cluster-autoscaler-aws-cluster-autoscaler -n kube-system &>/dev/null; then
        REPLICAS=$(kubectl get deployment cluster-autoscaler-aws-cluster-autoscaler -n kube-system -o jsonpath='{.status.readyReplicas}/{.spec.replicas}')
        echo "   Status: $REPLICAS ready"
        
        # Get recent logs
        echo "   Recent Activity:"
        kubectl logs -n kube-system deployment/cluster-autoscaler-aws-cluster-autoscaler --tail=5 | sed 's/^/     /'
    else
        echo "   Status: Not found"
    fi
    
    echo ""
    
    # Karpenter
    echo "🔹 Karpenter:"
    if kubectl get deployment karpenter -n karpenter &>/dev/null; then
        REPLICAS=$(kubectl get deployment karpenter -n karpenter -o jsonpath='{.status.readyReplicas}/{.spec.replicas}')
        echo "   Status: $REPLICAS ready"
        
        # Get provisioners
        echo "   Provisioners:"
        kubectl get provisioners -o custom-columns="NAME:.metadata.name,CPU:.spec.limits.resources.cpu,MEMORY:.spec.limits.resources.memory" --no-headers 2>/dev/null | while read name cpu memory; do
            echo "     $name: CPU=$cpu, Memory=$memory"
        done || echo "     No provisioners found"
    else
        echo "   Status: Not found"
    fi
    
    echo ""
    
    # KEDA
    echo "🔹 KEDA:"
    if kubectl get deployment keda-operator -n keda-system &>/dev/null; then
        REPLICAS=$(kubectl get deployment keda-operator -n keda-system -o jsonpath='{.status.readyReplicas}/{.spec.replicas}')
        echo "   Status: $REPLICAS ready"
        
        # Get scaled objects
        echo "   Scaled Objects:"
        kubectl get scaledobjects --all-namespaces --no-headers 2>/dev/null | wc -l | xargs echo "     Total:"
    else
        echo "   Status: Not found"
    fi
    
    echo ""
}

# Function to show resource utilization
show_resource_utilization() {
    echo "📈 RESOURCE UTILIZATION"
    echo "======================"
    
    # Node resource usage
    echo "🔹 Node Resource Usage:"
    kubectl top nodes 2>/dev/null | while read line; do
        echo "   $line"
    done || echo "   Metrics server not available"
    
    echo ""
    
    # Pod resource usage by namespace
    echo "🔹 Top Resource Consuming Pods:"
    kubectl top pods --all-namespaces --sort-by=memory 2>/dev/null | head -10 | while read line; do
        echo "   $line"
    done || echo "   Metrics server not available"
    
    echo ""
}

# Function to show tenant status
show_tenant_status() {
    echo "🏢 TENANT STATUS"
    echo "==============="
    
    # Count tenant namespaces
    TENANT_COUNT=$(kubectl get namespaces | grep -c "^tenant-" || echo "0")
    echo "🔹 Total Tenant Namespaces: $TENANT_COUNT"
    
    echo ""
    echo "🔹 Tenant Details:"
    kubectl get namespaces | grep "^tenant-" | while read namespace status age; do
        POD_COUNT=$(kubectl get pods -n "$namespace" --no-headers 2>/dev/null | wc -l)
        SERVICE_COUNT=$(kubectl get services -n "$namespace" --no-headers 2>/dev/null | wc -l)
        echo "   $namespace: $POD_COUNT pods, $SERVICE_COUNT services (Age: $age)"
    done
    
    echo ""
}

# Function to show scaling events
show_scaling_events() {
    echo "📋 RECENT SCALING EVENTS"
    echo "======================="
    
    # Get recent events related to scaling
    echo "🔹 Cluster Events (Last 10):"
    kubectl get events --all-namespaces --sort-by='.lastTimestamp' | grep -E "(Scaled|Created|Started|Pulled)" | tail -10 | while read line; do
        echo "   $line"
    done
    
    echo ""
}

# Function to show capacity recommendations
show_capacity_recommendations() {
    echo "💡 CAPACITY RECOMMENDATIONS"
    echo "=========================="
    
    # Calculate current utilization
    TOTAL_NODES=$(kubectl get nodes --no-headers | wc -l)
    TENANT_COUNT=$(kubectl get namespaces | grep -c "^tenant-" || echo "0")
    
    if [ "$TOTAL_NODES" -gt 0 ] && [ "$TENANT_COUNT" -gt 0 ]; then
        TENANTS_PER_NODE=$(echo "scale=1; $TENANT_COUNT / $TOTAL_NODES" | bc -l 2>/dev/null || echo "N/A")
        echo "🔹 Current Density: $TENANTS_PER_NODE tenants per node"
        
        # Recommendations based on current usage
        if [ "$TENANT_COUNT" -lt 10 ]; then
            echo "🔹 Recommendation: Low tenant count - consider cost optimization"
        elif [ "$TENANT_COUNT" -lt 50 ]; then
            echo "🔹 Recommendation: Moderate load - monitor for scaling opportunities"
        else
            echo "🔹 Recommendation: High tenant density - ensure adequate resources"
        fi
    else
        echo "🔹 Unable to calculate recommendations"
    fi
    
    echo ""
    
    # Show theoretical capacity
    echo "🔹 Theoretical Maximum Capacity:"
    echo "   Main Nodes: 50 (t3a.large to m5.xlarge)"
    echo "   Spot Nodes: 30 (t3a.large to m5.large)"
    echo "   Total Nodes: 80"
    echo "   Estimated Tenant Capacity: 200-400 tenants"
    echo "   (Assuming 3-5 pods per tenant, 20-30 pods per node)"
    
    echo ""
}

# Function to run continuous monitoring
continuous_monitoring() {
    echo "🔄 CONTINUOUS MONITORING MODE"
    echo "============================"
    echo "Press Ctrl+C to stop"
    echo ""
    
    while true; do
        clear
        echo "📊 LIVE AUTOSCALING DASHBOARD - $(date)"
        echo "========================================"
        echo ""
        
        # Quick status
        NODES=$(kubectl get nodes --no-headers | wc -l)
        TENANTS=$(kubectl get namespaces | grep -c "^tenant-" || echo "0")
        PODS=$(kubectl get pods --all-namespaces --no-headers | wc -l)
        
        echo "🎯 QUICK STATUS: $NODES nodes | $TENANTS tenants | $PODS pods"
        echo ""
        
        # Resource usage
        kubectl top nodes 2>/dev/null | head -5 || echo "Metrics not available"
        
        echo ""
        echo "Next update in 30 seconds..."
        sleep 30
    done
}

# Function to generate report
generate_report() {
    echo "📄 GENERATING AUTOSCALING REPORT"
    echo "================================"
    
    REPORT_FILE="autoscaling_report_$(date +%Y%m%d_%H%M%S).txt"
    
    {
        echo "ENHANCED AUTOSCALING REPORT"
        echo "Generated: $(date)"
        echo "=========================="
        echo ""
        
        show_cluster_capacity
        show_autoscaler_status
        show_resource_utilization
        show_tenant_status
        show_scaling_events
        show_capacity_recommendations
        
    } > "$REPORT_FILE"
    
    echo "✅ Report saved to: $REPORT_FILE"
}

# Main function
main() {
    case "${1:-status}" in
        "status")
            show_cluster_capacity
            show_autoscaler_status
            show_resource_utilization
            show_tenant_status
            show_capacity_recommendations
            ;;
        "events")
            show_scaling_events
            ;;
        "continuous")
            continuous_monitoring
            ;;
        "report")
            generate_report
            ;;
        "help")
            echo "Usage: $0 [status|events|continuous|report|help]"
            echo ""
            echo "Commands:"
            echo "  status     - Show current autoscaling status (default)"
            echo "  events     - Show recent scaling events"
            echo "  continuous - Run continuous monitoring dashboard"
            echo "  report     - Generate detailed report file"
            echo "  help       - Show this help message"
            ;;
        *)
            echo "Unknown command: $1"
            echo "Use '$0 help' for usage information"
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"
