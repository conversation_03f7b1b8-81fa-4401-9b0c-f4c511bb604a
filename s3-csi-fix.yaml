---
apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  name: s3-sc-test-tenant
provisioner: s3.csi.aws.com
parameters:
  mounter: geesefs
  options: allow-delete,region=eu-central-1,uid=33,gid=33,file-mode=0666,dir-mode=0777,allow-other
---
apiVersion: v1
kind: PersistentVolume
metadata:
  name: s3-pv-test-tenant
spec:
  capacity:
    storage: 10Gi
  accessModes:
    - ReadWriteMany
  persistentVolumeReclaimPolicy: Delete
  storageClassName: s3-sc-test-tenant
  mountOptions:
    - allow-delete
    - region=eu-central-1
    - uid=33
    - gid=33
    - file-mode=0666
    - dir-mode=0777
    - allow-other
  csi:
    driver: s3.csi.aws.com
    volumeHandle: s3-csi-driver-volume-test-tenant
    volumeAttributes:
      bucketName: tenant-test-tenant-assets-1749715634
      region: eu-central-1
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: s3-pvc-test-tenant
  namespace: tenant-test-tenant
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 10Gi
  storageClassName: s3-sc-test-tenant
  volumeName: s3-pv-test-tenant
