#!/bin/bash

# Test script for HTTPS-enabled tenant onboarding
# This script tests the updated Go onboarding script with HTTPS support

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Configuration
TEST_TENANT_ID="httpstest$(date +%s)"
DOMAIN="architrave-assets.com"
SSL_CERT_ARN="arn:aws:acm:eu-central-1:545009857703:certificate/8d306c2f-62e2-42f4-a941-562ce65cda32"
LOAD_BALANCER_HOSTNAME=""

log_info "🚀 Starting HTTPS-enabled tenant onboarding test"
log_info "Test Tenant ID: $TEST_TENANT_ID"
log_info "Domain: $DOMAIN"
log_info "SSL Certificate ARN: $SSL_CERT_ARN"

# Step 1: Run the updated onboarding script
log_info "Step 1: Running updated Go onboarding script with HTTPS support..."

cd /Users/<USER>/Projects/new_project/infra-provisioning/tenant-management/scripts

go run advanced_tenant_onboard.go \
    --tenant-id="$TEST_TENANT_ID" \
    --tenant-name="HTTPS Test Tenant" \
    --domain="$DOMAIN" \
    --ssl-certificate-arn="$SSL_CERT_ARN" \
    --enable-auto-fix \
    --enable-production-audit

if [ $? -eq 0 ]; then
    log_success "Onboarding completed successfully"
else
    log_error "Onboarding failed"
    exit 1
fi

# Step 2: Wait for services to be ready
log_info "Step 2: Waiting for services to be ready..."
sleep 30

# Step 3: Get load balancer hostname
log_info "Step 3: Getting load balancer hostname..."
LOAD_BALANCER_HOSTNAME=$(kubectl get svc istio-ingress -n istio-system -o jsonpath='{.status.loadBalancer.ingress[0].hostname}')

if [ -z "$LOAD_BALANCER_HOSTNAME" ]; then
    log_error "Could not get load balancer hostname"
    exit 1
fi

log_info "Load Balancer Hostname: $LOAD_BALANCER_HOSTNAME"

# Step 4: Test HTTP access
log_info "Step 4: Testing HTTP access..."
HTTP_RESPONSE=$(curl -H "Host: $TEST_TENANT_ID.$DOMAIN" \
    http://$LOAD_BALANCER_HOSTNAME/ \
    --max-time 10 -s -o /dev/null -w "%{http_code}")

if [ "$HTTP_RESPONSE" = "200" ]; then
    log_success "HTTP access working (Status: $HTTP_RESPONSE)"
else
    log_warning "HTTP access returned status: $HTTP_RESPONSE"
fi

# Step 5: Test HTTPS access
log_info "Step 5: Testing HTTPS access..."
HTTPS_RESPONSE=$(curl -k -H "Host: $TEST_TENANT_ID.$DOMAIN" \
    https://$LOAD_BALANCER_HOSTNAME/ \
    --max-time 10 -s -o /dev/null -w "%{http_code}")

if [ "$HTTPS_RESPONSE" = "200" ]; then
    log_success "HTTPS access working (Status: $HTTPS_RESPONSE)"
else
    log_warning "HTTPS access returned status: $HTTPS_RESPONSE"
fi

# Step 6: Test API endpoints
log_info "Step 6: Testing API endpoints..."
API_RESPONSE=$(curl -k -H "Host: $TEST_TENANT_ID.$DOMAIN" \
    https://$LOAD_BALANCER_HOSTNAME/api/health \
    --max-time 10 -s)

if [ "$API_RESPONSE" = "healthy" ]; then
    log_success "API endpoint working (Response: $API_RESPONSE)"
else
    log_warning "API endpoint response: $API_RESPONSE"
fi

# Step 7: Verify SSL certificate
log_info "Step 7: Verifying SSL certificate..."
SSL_SUBJECT=$(openssl s_client -connect $LOAD_BALANCER_HOSTNAME:443 \
    -servername $TEST_TENANT_ID.$DOMAIN < /dev/null 2>/dev/null | \
    openssl x509 -noout -subject 2>/dev/null | grep -o "CN=.*")

if [[ "$SSL_SUBJECT" == *"architrave-assets.com"* ]]; then
    log_success "SSL certificate verified: $SSL_SUBJECT"
else
    log_warning "SSL certificate subject: $SSL_SUBJECT"
fi

# Step 8: Check Istio configuration
log_info "Step 8: Checking Istio configuration..."

# Check Gateway
if kubectl get gateway tenant-gateway -n istio-system >/dev/null 2>&1; then
    log_success "Istio Gateway exists"
else
    log_error "Istio Gateway not found"
fi

# Check VirtualService
if kubectl get virtualservice tenant-$TEST_TENANT_ID-vs -n tenant-$TEST_TENANT_ID >/dev/null 2>&1; then
    log_success "VirtualService exists"
else
    log_error "VirtualService not found"
fi

# Check for conflicting services
if kubectl get service webapp -n tenant-$TEST_TENANT_ID >/dev/null 2>&1; then
    log_warning "Conflicting webapp service still exists"
else
    log_success "No conflicting webapp service found"
fi

# Step 9: Summary
log_info "🎯 Test Summary:"
echo "  - Tenant ID: $TEST_TENANT_ID"
echo "  - HTTP Status: $HTTP_RESPONSE"
echo "  - HTTPS Status: $HTTPS_RESPONSE"
echo "  - API Response: $API_RESPONSE"
echo "  - SSL Certificate: $SSL_SUBJECT"
echo "  - Load Balancer: $LOAD_BALANCER_HOSTNAME"

if [ "$HTTP_RESPONSE" = "200" ] && [ "$HTTPS_RESPONSE" = "200" ] && [ "$API_RESPONSE" = "healthy" ]; then
    log_success "🎉 All tests passed! HTTPS-enabled onboarding is working correctly."
    echo ""
    log_info "You can now access the tenant at:"
    echo "  - HTTP:  http://$TEST_TENANT_ID.$DOMAIN (via load balancer)"
    echo "  - HTTPS: https://$TEST_TENANT_ID.$DOMAIN (via load balancer)"
    echo ""
    log_info "To set up DNS, create a CNAME record:"
    echo "  $TEST_TENANT_ID.$DOMAIN -> $LOAD_BALANCER_HOSTNAME"
else
    log_warning "Some tests failed. Check the output above for details."
fi

log_info "Test completed."
