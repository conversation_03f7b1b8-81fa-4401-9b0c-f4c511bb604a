#!/bin/bash

echo "=== Starting Tenant Operations ==="
echo "Current directory: $(pwd)"
echo "Date: $(date)"

echo ""
echo "=== Step 1: Check Kubernetes Cluster Status ==="
kubectl cluster-info || echo "Cluster info failed"

echo ""
echo "=== Step 2: Check Existing Tenants ==="
kubectl get namespaces | grep tenant || echo "No tenant namespaces found"

echo ""
echo "=== Step 3: Run Complete Offboarding ==="
echo "Running advanced tenant offboarding for fresh-test..."
python3 tenant-management/scripts/advanced_tenant_offboard.py --tenant-id fresh-test --force --verify || echo "Offboarding completed with status: $?"

echo ""
echo "=== Step 4: Run Advanced Onboarding ==="
echo "Running advanced tenant onboarding for fresh-test..."
python3 tenant-management/scripts/advanced_tenant_onboard.py --tenant-id fresh-test --tenant-name "Fresh Test" --subdomain fresh-test || echo "Onboarding completed with status: $?"

echo ""
echo "=== Step 5: Check Tenant Status After Onboarding ==="
kubectl get namespaces | grep tenant || echo "No tenant namespaces found"
kubectl get pods -n tenant-fresh-test || echo "No pods found in tenant-fresh-test namespace"

echo ""
echo "=== Tenant Operations Complete ==="
