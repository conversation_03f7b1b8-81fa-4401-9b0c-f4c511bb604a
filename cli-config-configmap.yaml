apiVersion: v1
kind: ConfigMap
metadata:
  name: cli-config
  namespace: tenant-tech-solutions
data:
  local.php: |
    <?php

    // CRITICAL FIX: Define missing gettext _() function
    if (!function_exists('_')) {
        function _($text) {
            return $text; // Simple fallback - just return the text as-is
        }
    }

    $config = [
        'doctrine' => [
            'connection' => [
                'orm_default' => [
                    'driverClass' => 'Doctrine\\DBAL\\Driver\\PDO\\MySQL\\Driver',
                    'params' => [
                        'host' => getenv('DB_HOST') ?: getenv('MYSQL_HOST'),
                        'port' => getenv('DB_PORT') ?: '3306',
                        'user' => getenv('DB_USER') ?: getenv('MYSQL_USER'),
                        'dbname' => getenv('DB_NAME') ?: getenv('MYSQL_DATABASE'),
                        'password' => getenv('DB_PASSWORD') ?: getenv('MYSQL_PASSWORD'),
                        'charset' => 'utf8mb4',
                        'driverOptions' => [
                            1009 => '/tmp/rds-ca-2019-root.pem', // PDO::MYSQL_ATTR_SSL_CA
                            1014 => false, // PDO::MYSQL_ATTR_SSL_VERIFY_SERVER_CERT
                        ],
                    ],
                ],
            ],
        ],
        'db' => [
            'driver' => 'pdo_mysql',
            'host' => getenv('DB_HOST') ?: getenv('MYSQL_HOST'),
            'port' => getenv('DB_PORT') ?: '3306',
            'dbname' => getenv('DB_NAME') ?: getenv('MYSQL_DATABASE'),
            'database' => getenv('DB_NAME') ?: getenv('MYSQL_DATABASE'),
            'user' => getenv('DB_USER') ?: getenv('MYSQL_USER'),
            'username' => getenv('DB_USER') ?: getenv('MYSQL_USER'),
            'password' => getenv('DB_PASSWORD') ?: getenv('MYSQL_PASSWORD'),
            'charset' => 'utf8mb4',
            'driverOptions' => [
                1009 => '/tmp/rds-ca-2019-root.pem', // PDO::MYSQL_ATTR_SSL_CA
                1014 => false, // PDO::MYSQL_ATTR_SSL_VERIFY_SERVER_CERT
            ],
        ],
        // CRITICAL FIX: Add missing databaseName field required by CLI (must be array format)
        'databaseName' => [
            'production' => 'architrave',
            'development' => 'architrave',
            'phpUnit' => 'architrave_test',
        ],
        // CRITICAL FIX: Add missing baseSqlFilePath field required by CLI
        'baseSqlFilePath' => '/storage/ArchAssets/data/architrave_1.45.2.sql',
        // Add other required fields
        'customerId' => 'tech-solutions',
        'appHost' => 'https://techsolutions.architrave.com',
        'appEnvironment' => 'production',
        'instance_pre_shared_key' => 'tenant-tech-solutions-key',
        // CRITICAL FIX: Add missing logger configuration for Sentry
        'logger' => [
            'slack' => [
                'webhook' => 'https://hooks.slack.com/services/disabled',
                'testMode' => true,
            ],
            'sentry' => [
                'webhook' => 'https://<EMAIL>/disabled',
                'testMode' => true,
            ],
            'ipro' => [
                'testMode' => true,
            ],
            'activate' => true,
            'writer' => 'Stream',
            'writerOptions' => 'php://stderr',
        ],
        // CRITICAL FIX: Add missing directory configuration (use accessible directories)
        'dir' => [
            'assets' => '/storage/ArchAssets/data/cache',
            'export' => '/storage/ArchAssets/data/cache',
            'tmp' => '/storage/ArchAssets/data/cache',
            'sftp' => '/storage/ArchAssets/data/cache',
            'import' => '/storage/ArchAssets/data/cache',
            'delphiTranslations' => '/storage/ArchAssets/data/language',
            'quarantine' => '/storage/ArchAssets/data/cache',
        ],
        // CRITICAL FIX: Add missing translator configuration for gettext _() function
        'translator' => [
            'locale' => 'en_US',
            'translation_file_patterns' => [
                [
                    'type' => 'gettext',
                    'base_dir' => '/storage/ArchAssets/data/language',
                    'pattern' => '%s.mo',
                ],
            ],
        ],
        // CRITICAL FIX: Add missing S3 configuration
        's3_object_storage' => [
            'key' => 'disabled',
            'secret' => 'disabled',
            'bucket' => 'tenant-tech-solutions-assets',
            'endpoint' => 'https://s3.eu-central-1.amazonaws.com',
            'region' => 'eu-central-1',
        ],
        // CRITICAL FIX: Add missing delphi configuration
        'delphi_instance_locale' => 'en_US',
        'dqaLockPeriodInMinutes' => 30,
        // CRITICAL FIX: Add missing system configuration section
        'system' => [
            'api_key_user_email' => getenv('API_KEY_USER_EMAIL') ?: '<EMAIL>',
            'scim_user_email' => getenv('SCIM_USER_EMAIL') ?: '<EMAIL>',
        ],
        // CRITICAL FIX: Add missing cloud communication configuration
        'cloud_communication' => [
            'global_search' => [
                'feature_flags' => ['global-search'],
                'aws_credentials' => [
                    'key' => 'disabled',
                    'secret' => 'disabled',
                ],
                's3' => [
                    'provider' => 'aws',
                    'endpoint' => 'https://s3.eu-central-1.amazonaws.com',
                    'bucket_name' => 'tenant-tech-solutions-assets',
                    'region' => 'eu-central-1',
                ],
                'sns' => [
                    'provider' => 'aws',
                    'endpoint' => 'https://sns.eu-central-1.amazonaws.com',
                    'topic' => 'arn:aws:sns:eu-central-1:000000000000:disabled',
                    'version' => 'latest',
                    'region' => 'eu-central-1',
                ],
            ],
        ],
        // CRITICAL FIX: Add missing release notes configuration
        'release_notes' => [
            'provider' => 'disabled',
        ],
    ];

    return $config;
