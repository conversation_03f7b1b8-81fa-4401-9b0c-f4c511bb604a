#!/bin/bash

# Automated Cluster Autoscaler IRSA Fix Script
# ============================================
# This script automatically fixes Cluster Autoscaler IAM permissions
# and enables autoscaling for EKS clusters

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
CLUSTER_NAME="production-wks"
AWS_REGION="eu-central-1"
AWS_ACCOUNT_ID="************"
ROLE_NAME="eks-cluster-autoscaler-irsa"
POLICY_NAME="AmazonEKSClusterAutoscalerPolicy"
SERVICE_ACCOUNT_NAME="cluster-autoscaler-aws-cluster-autoscaler"
SERVICE_ACCOUNT_NAMESPACE="kube-system"

# Functions
print_header() {
    echo -e "${BLUE}========================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}========================================${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check prerequisites
check_prerequisites() {
    print_header "Checking Prerequisites"
    
    # Check if kubectl is available
    if ! command -v kubectl &> /dev/null; then
        print_error "kubectl is not installed or not in PATH"
        exit 1
    fi
    print_success "kubectl is available"
    
    # Check if aws CLI is available
    if ! command -v aws &> /dev/null; then
        print_error "AWS CLI is not installed or not in PATH"
        exit 1
    fi
    print_success "AWS CLI is available"
    
    # Check if jq is available
    if ! command -v jq &> /dev/null; then
        print_error "jq is not installed or not in PATH"
        exit 1
    fi
    print_success "jq is available"
    
    # Check AWS credentials
    if ! aws sts get-caller-identity &> /dev/null; then
        print_error "AWS credentials are not configured"
        exit 1
    fi
    print_success "AWS credentials are configured"
    
    # Check cluster connectivity
    if ! kubectl cluster-info &> /dev/null; then
        print_error "Cannot connect to Kubernetes cluster"
        exit 1
    fi
    print_success "Connected to Kubernetes cluster"
}

# Get OIDC Provider ID
get_oidc_provider_id() {
    print_header "Getting OIDC Provider ID"
    
    print_info "Getting OIDC issuer URL for cluster: $CLUSTER_NAME"
    
    OIDC_ISSUER=$(aws eks describe-cluster \
        --name "$CLUSTER_NAME" \
        --region "$AWS_REGION" \
        --query "cluster.identity.oidc.issuer" \
        --output text 2>/dev/null)
    
    if [ -z "$OIDC_ISSUER" ]; then
        print_error "Failed to get OIDC issuer URL"
        exit 1
    fi
    
    print_success "OIDC Issuer URL: $OIDC_ISSUER"
    
    # Extract OIDC ID from issuer URL
    OIDC_ID=$(echo "$OIDC_ISSUER" | cut -d'/' -f5)
    
    if [ -z "$OIDC_ID" ]; then
        print_error "Failed to extract OIDC ID from issuer URL"
        exit 1
    fi
    
    print_success "OIDC ID: $OIDC_ID"
    export OIDC_ID
}

# Create IAM Policy
create_iam_policy() {
    print_header "Creating IAM Policy"
    
    # Check if policy already exists
    if aws iam get-policy --policy-arn "arn:aws:iam::$AWS_ACCOUNT_ID:policy/$POLICY_NAME" &> /dev/null; then
        print_warning "Policy $POLICY_NAME already exists, skipping creation"
        return 0
    fi
    
    print_info "Creating IAM policy: $POLICY_NAME"
    
    # Create policy document
    cat > /tmp/autoscaler-policy.json << 'EOF'
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "autoscaling:DescribeAutoScalingGroups",
                "autoscaling:DescribeAutoScalingInstances",
                "autoscaling:DescribeLaunchConfigurations",
                "autoscaling:DescribeTags",
                "autoscaling:SetDesiredCapacity",
                "autoscaling:TerminateInstanceInAutoScalingGroup",
                "ec2:DescribeLaunchTemplateVersions",
                "ec2:DescribeInstanceTypes",
                "ec2:DescribeInstances",
                "ec2:DescribeImages",
                "ec2:DescribeSubnets",
                "ec2:DescribeSecurityGroups",
                "ec2:DescribeLaunchTemplates",
                "ec2:DescribeTags",
                "ec2:DescribeVpcs"
            ],
            "Resource": "*"
        }
    ]
}
EOF
    
    # Create the policy
    aws iam create-policy \
        --policy-name "$POLICY_NAME" \
        --policy-document file:///tmp/autoscaler-policy.json \
        --description "Policy for EKS Cluster Autoscaler" \
        > /dev/null
    
    print_success "IAM policy $POLICY_NAME created successfully"
    
    # Clean up
    rm -f /tmp/autoscaler-policy.json
}

# Create IAM Role
create_iam_role() {
    print_header "Creating IAM Role"
    
    # Check if role already exists
    if aws iam get-role --role-name "$ROLE_NAME" &> /dev/null; then
        print_warning "Role $ROLE_NAME already exists, updating trust policy"
        
        # Update trust policy
        cat > /tmp/trust-policy.json << EOF
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Principal": {
                "Federated": "arn:aws:iam::$AWS_ACCOUNT_ID:oidc-provider/oidc.eks.$AWS_REGION.amazonaws.com/id/$OIDC_ID"
            },
            "Action": "sts:AssumeRoleWithWebIdentity",
            "Condition": {
                "StringEquals": {
                    "oidc.eks.$AWS_REGION.amazonaws.com/id/$OIDC_ID:sub": "system:serviceaccount:$SERVICE_ACCOUNT_NAMESPACE:$SERVICE_ACCOUNT_NAME"
                }
            }
        }
    ]
}
EOF
        
        aws iam update-assume-role-policy \
            --role-name "$ROLE_NAME" \
            --policy-document file:///tmp/trust-policy.json
        
        print_success "Trust policy updated for role $ROLE_NAME"
        rm -f /tmp/trust-policy.json
        return 0
    fi
    
    print_info "Creating IAM role: $ROLE_NAME"
    
    # Create trust policy
    cat > /tmp/trust-policy.json << EOF
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Principal": {
                "Federated": "arn:aws:iam::$AWS_ACCOUNT_ID:oidc-provider/oidc.eks.$AWS_REGION.amazonaws.com/id/$OIDC_ID"
            },
            "Action": "sts:AssumeRoleWithWebIdentity",
            "Condition": {
                "StringEquals": {
                    "oidc.eks.$AWS_REGION.amazonaws.com/id/$OIDC_ID:sub": "system:serviceaccount:$SERVICE_ACCOUNT_NAMESPACE:$SERVICE_ACCOUNT_NAME"
                }
            }
        }
    ]
}
EOF
    
    # Create the role
    aws iam create-role \
        --role-name "$ROLE_NAME" \
        --assume-role-policy-document file:///tmp/trust-policy.json \
        --description "IAM role for EKS Cluster Autoscaler" \
        > /dev/null
    
    print_success "IAM role $ROLE_NAME created successfully"
    
    # Clean up
    rm -f /tmp/trust-policy.json
}

# Attach policy to role
attach_policy_to_role() {
    print_header "Attaching Policy to Role"
    
    print_info "Attaching policy to role: $ROLE_NAME"
    
    # Check if policy is already attached
    if aws iam list-attached-role-policies \
        --role-name "$ROLE_NAME" \
        --query "AttachedPolicies[?PolicyName=='$POLICY_NAME'].PolicyName" \
        --output text | grep -q "$POLICY_NAME"; then
        print_warning "Policy $POLICY_NAME is already attached to role $ROLE_NAME"
        return 0
    fi
    
    # Attach the policy
    aws iam attach-role-policy \
        --role-name "$ROLE_NAME" \
        --policy-arn "arn:aws:iam::$AWS_ACCOUNT_ID:policy/$POLICY_NAME"
    
    print_success "Policy $POLICY_NAME attached to role $ROLE_NAME"
}

# Annotate Kubernetes Service Account
annotate_service_account() {
    print_header "Annotating Kubernetes Service Account"
    
    print_info "Checking if service account exists: $SERVICE_ACCOUNT_NAME"
    
    # Check if service account exists
    if ! kubectl get serviceaccount "$SERVICE_ACCOUNT_NAME" -n "$SERVICE_ACCOUNT_NAMESPACE" &> /dev/null; then
        print_error "Service account $SERVICE_ACCOUNT_NAME not found in namespace $SERVICE_ACCOUNT_NAMESPACE"
        exit 1
    fi
    
    print_info "Annotating service account with IAM role ARN"
    
    # Annotate the service account
    kubectl annotate serviceaccount "$SERVICE_ACCOUNT_NAME" \
        -n "$SERVICE_ACCOUNT_NAMESPACE" \
        "eks.amazonaws.com/role-arn=arn:aws:iam::$AWS_ACCOUNT_ID:role/$ROLE_NAME" \
        --overwrite
    
    print_success "Service account annotated with IAM role ARN"
}

# Restart Cluster Autoscaler
restart_autoscaler() {
    print_header "Restarting Cluster Autoscaler"
    
    print_info "Deleting existing autoscaler pods to restart with new permissions"
    
    # Delete autoscaler pods
    kubectl delete pods -n "$SERVICE_ACCOUNT_NAMESPACE" \
        -l app.kubernetes.io/name=cluster-autoscaler \
        --ignore-not-found=true
    
    print_success "Autoscaler pods deleted, new pods will start with updated permissions"
    
    # Wait for new pod to be ready
    print_info "Waiting for autoscaler pod to be ready..."
    
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if kubectl get pods -n "$SERVICE_ACCOUNT_NAMESPACE" \
            -l app.kubernetes.io/name=cluster-autoscaler \
            --field-selector=status.phase=Running \
            --no-headers | grep -q Running; then
            print_success "Autoscaler pod is running"
            break
        fi
        
        if [ $attempt -eq $max_attempts ]; then
            print_warning "Autoscaler pod is not ready after $max_attempts attempts"
            break
        fi
        
        echo -n "."
        sleep 10
        ((attempt++))
    done
    
    echo ""
}

# Verify Autoscaler Status
verify_autoscaler() {
    print_header "Verifying Cluster Autoscaler Status"
    
    print_info "Checking autoscaler pod status"
    
    # Get autoscaler pod name
    AUTOSCALER_POD=$(kubectl get pods -n "$SERVICE_ACCOUNT_NAMESPACE" \
        -l app.kubernetes.io/name=cluster-autoscaler \
        --field-selector=status.phase=Running \
        -o jsonpath='{.items[0].metadata.name}' 2>/dev/null)
    
    if [ -z "$AUTOSCALER_POD" ]; then
        print_error "No running autoscaler pod found"
        return 1
    fi
    
    print_success "Found autoscaler pod: $AUTOSCALER_POD"
    
    # Check pod logs for errors
    print_info "Checking autoscaler logs for errors"
    
    if kubectl logs "$AUTOSCALER_POD" -n "$SERVICE_ACCOUNT_NAMESPACE" 2>&1 | grep -q "sts:AssumeRoleWithWebIdentity"; then
        print_error "Autoscaler still has IAM permission issues"
        return 1
    fi
    
    if kubectl logs "$AUTOSCALER_POD" -n "$SERVICE_ACCOUNT_NAMESPACE" 2>&1 | grep -q "Failed to create AWS Manager"; then
        print_error "Autoscaler still has AWS manager creation issues"
        return 1
    fi
    
    print_success "Autoscaler logs show no IAM permission errors"
    
    # Check if autoscaler is working
    print_info "Checking autoscaler functionality"
    
    # Wait a bit for autoscaler to start working
    sleep 30
    
    # Check for pending pods that should trigger scaling
    PENDING_PODS=$(kubectl get pods --all-namespaces --field-selector=status.phase=Pending -o json | jq '.items | length')
    
    if [ "$PENDING_PODS" -gt 0 ]; then
        print_info "Found $PENDING_PODS pending pods - autoscaler should scale up nodes"
        
        # Wait for scaling to happen
        print_info "Waiting for autoscaler to scale up nodes..."
        sleep 60
        
        # Check if nodes were added
        INITIAL_NODES=$(kubectl get nodes --no-headers | wc -l)
        print_info "Current node count: $INITIAL_NODES"
    else
        print_success "No pending pods found - cluster has sufficient capacity"
    fi
    
    print_success "Cluster Autoscaler verification completed"
}

# Main execution
main() {
    print_header "Automated Cluster Autoscaler IRSA Fix"
    print_info "Cluster: $CLUSTER_NAME"
    print_info "Region: $AWS_REGION"
    print_info "Account: $AWS_ACCOUNT_ID"
    echo ""
    
    check_prerequisites
    get_oidc_provider_id
    create_iam_policy
    create_iam_role
    attach_policy_to_role
    annotate_service_account
    restart_autoscaler
    verify_autoscaler
    
    print_header "Fix Completed Successfully"
    print_success "Cluster Autoscaler IRSA permissions have been fixed"
    print_success "Your cluster can now autoscale nodes as needed"
    print_info "Pending pods should start automatically as new nodes are added"
    echo ""
    print_info "To monitor autoscaling, run:"
    echo "  kubectl get nodes"
    echo "  kubectl get pods --all-namespaces --field-selector=status.phase=Pending"
    echo "  kubectl logs -n kube-system -l app.kubernetes.io/name=cluster-autoscaler"
}

# Run main function
main "$@" 