#!/bin/bash
# Cleanup script to remove conflicting AWS Load Balancer Controller components
# and prepare for Istio-based external networking

set -e

echo "🧹 CLEANUP: REMOVING CONFLICTING ALB COMPONENTS"
echo "==============================================="
echo ""

echo "Step 1: Removing Kubernetes Ingress resources from tenant namespaces..."
TENANT_NAMESPACES=$(kubectl get namespaces | grep "tenant-" | awk '{print $1}' || echo "")
if [ -n "$TENANT_NAMESPACES" ]; then
    for namespace in $TENANT_NAMESPACES; do
        echo "  Checking namespace: $namespace"
        INGRESSES=$(kubectl get ingress -n $namespace --no-headers 2>/dev/null | awk '{print $1}' || echo "")
        if [ -n "$INGRESSES" ]; then
            for ingress in $INGRESSES; do
                echo "    Deleting ingress: $ingress"
                kubectl delete ingress $ingress -n $namespace || echo "    Failed to delete $ingress"
            done
        else
            echo "    No ingress resources found"
        fi
    done
else
    echo "  No tenant namespaces found"
fi
echo ""

echo "Step 2: Checking AWS Load Balancer Controller installation..."
if kubectl get deployment aws-load-balancer-controller -n kube-system >/dev/null 2>&1; then
    echo "  AWS Load Balancer Controller found - considering removal"
    echo "  WARNING: This will remove the AWS Load Balancer Controller"
    echo "  If you have other applications using ALB, this may affect them"
    echo "  Skipping automatic removal - manual intervention required"
    echo "  To remove manually: helm uninstall aws-load-balancer-controller -n kube-system"
else
    echo "  AWS Load Balancer Controller not found - no action needed"
fi
echo ""

echo "Step 3: Checking for Istio installation..."
if kubectl get namespace istio-system >/dev/null 2>&1; then
    echo "  ✅ istio-system namespace exists"
    
    echo "  Checking Istio components..."
    ISTIO_PODS=$(kubectl get pods -n istio-system --no-headers 2>/dev/null | wc -l || echo "0")
    if [ "$ISTIO_PODS" -gt 0 ]; then
        echo "  ✅ Found $ISTIO_PODS Istio pods running"
        kubectl get pods -n istio-system
    else
        echo "  ❌ No Istio pods found - Istio may not be properly installed"
    fi
    
    echo "  Checking Istio ingress gateway service..."
    if kubectl get svc istio-ingressgateway -n istio-system >/dev/null 2>&1; then
        echo "  ✅ istio-ingressgateway service exists"
        EXTERNAL_IP=$(kubectl get svc istio-ingressgateway -n istio-system -o jsonpath='{.status.loadBalancer.ingress[0].hostname}' 2>/dev/null || echo "")
        if [ -z "$EXTERNAL_IP" ]; then
            EXTERNAL_IP=$(kubectl get svc istio-ingressgateway -n istio-system -o jsonpath='{.status.loadBalancer.ingress[0].ip}' 2>/dev/null || echo "")
        fi
        
        if [ -n "$EXTERNAL_IP" ]; then
            echo "  ✅ External endpoint: $EXTERNAL_IP"
        else
            echo "  ⚠️  No external endpoint found - LoadBalancer may still be provisioning"
        fi
    else
        echo "  ❌ istio-ingressgateway service not found"
    fi
    
    echo "  Checking for tenant-gateway..."
    if kubectl get gateway tenant-gateway -n istio-system >/dev/null 2>&1; then
        echo "  ✅ tenant-gateway exists"
    else
        echo "  ❌ tenant-gateway not found - will be created by onboarding script"
    fi
    
else
    echo "  ❌ istio-system namespace not found"
    echo "  Istio needs to be installed for the new architecture to work"
    echo "  Please install Istio service mesh before proceeding"
fi
echo ""

echo "Step 4: Summary of current state..."
echo "  Tenant namespaces: $(echo "$TENANT_NAMESPACES" | wc -w)"
echo "  Ingress resources removed from tenant namespaces"
echo "  AWS Load Balancer Controller: $(kubectl get deployment aws-load-balancer-controller -n kube-system >/dev/null 2>&1 && echo "INSTALLED" || echo "NOT INSTALLED")"
echo "  Istio system: $(kubectl get namespace istio-system >/dev/null 2>&1 && echo "INSTALLED" || echo "NOT INSTALLED")"
echo ""

echo "🏁 CLEANUP COMPLETE"
echo "=================="
echo ""
echo "Next steps:"
echo "1. Ensure Istio is properly installed and configured"
echo "2. Run the updated advanced_tenant_onboard.go script"
echo "3. Test external connectivity via Istio Gateway"
echo ""
