---
# Comprehensive Auto-scaling Configuration
apiVersion: v1
kind: Namespace
metadata:
  name: autoscaling-system
  labels:
    pod-security.kubernetes.io/enforce: restricted
    pod-security.kubernetes.io/audit: restricted
    pod-security.kubernetes.io/warn: restricted
---
# Cluster Autoscaler
apiVersion: apps/v1
kind: Deployment
metadata:
  name: cluster-autoscaler
  namespace: autoscaling-system
  labels:
    app: cluster-autoscaler
spec:
  replicas: 1
  selector:
    matchLabels:
      app: cluster-autoscaler
  template:
    metadata:
      labels:
        app: cluster-autoscaler
    spec:
      serviceAccountName: cluster-autoscaler
      securityContext:
        runAsNonRoot: true
        runAsUser: 65534
        runAsGroup: 65534
        fsGroup: 65534
        seccompProfile:
          type: RuntimeDefault
      containers:
      - name: cluster-autoscaler
        image: k8s.gcr.io/autoscaling/cluster-autoscaler:v1.21.0
        command:
        - ./cluster-autoscaler
        - --v=4
        - --stderrthreshold=info
        - --cloud-provider=aws
        - --skip-nodes-with-local-storage=false
        - --expander=least-waste
        - --node-group-auto-discovery=asg:tag=k8s.io/cluster-autoscaler/enabled,k8s.io/cluster-autoscaler/architrave-cluster
        - --balance-similar-node-groups
        - --skip-nodes-with-system-pods=false
        - --scale-down-enabled=true
        - --scale-down-delay-after-add=10m
        - --scale-down-unneeded-time=10m
        - --scale-down-utilization-threshold=0.5
        - --max-node-provision-time=15m
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          capabilities:
            drop: ["ALL"]
        resources:
          requests:
            memory: "300Mi"
            cpu: "100m"
          limits:
            memory: "600Mi"
            cpu: "200m"
        env:
        - name: AWS_REGION
          value: "eu-central-1"
        volumeMounts:
        - name: ssl-certs
          mountPath: /etc/ssl/certs/ca-certificates.crt
          readOnly: true
        - name: tmp
          mountPath: /tmp
      volumes:
      - name: ssl-certs
        hostPath:
          path: /etc/ssl/certs/ca-certificates.crt
      - name: tmp
        emptyDir: {}
      nodeSelector:
        kubernetes.io/os: linux
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: cluster-autoscaler
  namespace: autoscaling-system
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::ACCOUNT_ID:role/cluster-autoscaler-role
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: cluster-autoscaler
rules:
- apiGroups: [""]
  resources: ["events", "endpoints"]
  verbs: ["create", "patch"]
- apiGroups: [""]
  resources: ["pods/eviction"]
  verbs: ["create"]
- apiGroups: [""]
  resources: ["pods/status"]
  verbs: ["update"]
- apiGroups: [""]
  resources: ["endpoints"]
  resourceNames: ["cluster-autoscaler"]
  verbs: ["get", "update"]
- apiGroups: [""]
  resources: ["nodes"]
  verbs: ["watch", "list", "get", "update"]
- apiGroups: [""]
  resources: ["pods", "services", "replicationcontrollers", "persistentvolumeclaims", "persistentvolumes"]
  verbs: ["watch", "list", "get"]
- apiGroups: ["extensions"]
  resources: ["replicasets", "daemonsets"]
  verbs: ["watch", "list", "get"]
- apiGroups: ["policy"]
  resources: ["poddisruptionbudgets"]
  verbs: ["watch", "list"]
- apiGroups: ["apps"]
  resources: ["statefulsets", "replicasets", "daemonsets"]
  verbs: ["watch", "list", "get"]
- apiGroups: ["storage.k8s.io"]
  resources: ["storageclasses", "csinodes"]
  verbs: ["watch", "list", "get"]
- apiGroups: ["batch", "extensions"]
  resources: ["jobs"]
  verbs: ["get", "list", "watch", "patch"]
- apiGroups: ["coordination.k8s.io"]
  resources: ["leases"]
  verbs: ["create"]
- apiGroups: ["coordination.k8s.io"]
  resourceNames: ["cluster-autoscaler"]
  resources: ["leases"]
  verbs: ["get", "update"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: cluster-autoscaler
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: cluster-autoscaler
subjects:
- kind: ServiceAccount
  name: cluster-autoscaler
  namespace: autoscaling-system
---
# Vertical Pod Autoscaler
apiVersion: apps/v1
kind: Deployment
metadata:
  name: vpa-recommender
  namespace: autoscaling-system
  labels:
    app: vpa-recommender
spec:
  replicas: 1
  selector:
    matchLabels:
      app: vpa-recommender
  template:
    metadata:
      labels:
        app: vpa-recommender
    spec:
      serviceAccountName: vpa-recommender
      securityContext:
        runAsNonRoot: true
        runAsUser: 65534
        runAsGroup: 65534
        fsGroup: 65534
        seccompProfile:
          type: RuntimeDefault
      containers:
      - name: recommender
        image: k8s.gcr.io/autoscaling/vpa-recommender:0.11.0
        command:
        - ./recommender
        - --v=4
        - --stderrthreshold=info
        - --pod-recommendation-min-cpu-millicores=25
        - --pod-recommendation-min-memory-mb=250
        - --storage=prometheus
        - --prometheus-address=http://prometheus.monitoring.svc.cluster.local:9090
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          capabilities:
            drop: ["ALL"]
        resources:
          requests:
            memory: "500Mi"
            cpu: "100m"
          limits:
            memory: "1Gi"
            cpu: "200m"
        ports:
        - containerPort: 8080
          name: metrics
        volumeMounts:
        - name: tmp
          mountPath: /tmp
      volumes:
      - name: tmp
        emptyDir: {}
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: vpa-recommender
  namespace: autoscaling-system
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: vpa-recommender
rules:
- apiGroups: [""]
  resources: ["pods", "nodes", "limitranges"]
  verbs: ["get", "list", "watch"]
- apiGroups: [""]
  resources: ["events"]
  verbs: ["get", "list", "watch", "create"]
- apiGroups: ["poc.autoscaling.k8s.io"]
  resources: ["verticalpodautoscalers"]
  verbs: ["get", "list", "watch", "patch"]
- apiGroups: ["autoscaling.k8s.io"]
  resources: ["verticalpodautoscalers"]
  verbs: ["get", "list", "watch", "patch"]
- apiGroups: ["apps"]
  resources: ["replicasets"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["apps"]
  resources: ["deployments"]
  verbs: ["get", "list", "watch"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: vpa-recommender
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: vpa-recommender
subjects:
- kind: ServiceAccount
  name: vpa-recommender
  namespace: autoscaling-system
---
# Custom Metrics API Server
apiVersion: apps/v1
kind: Deployment
metadata:
  name: custom-metrics-apiserver
  namespace: autoscaling-system
  labels:
    app: custom-metrics-apiserver
spec:
  replicas: 1
  selector:
    matchLabels:
      app: custom-metrics-apiserver
  template:
    metadata:
      labels:
        app: custom-metrics-apiserver
    spec:
      serviceAccountName: custom-metrics-apiserver
      securityContext:
        runAsNonRoot: true
        runAsUser: 65534
        runAsGroup: 65534
        fsGroup: 65534
        seccompProfile:
          type: RuntimeDefault
      containers:
      - name: custom-metrics-apiserver
        image: k8s.gcr.io/prometheus-adapter/prometheus-adapter:v0.9.1
        args:
        - --secure-port=6443
        - --tls-cert-file=/var/run/serving-cert/tls.crt
        - --tls-private-key-file=/var/run/serving-cert/tls.key
        - --logtostderr=true
        - --prometheus-url=http://prometheus.monitoring.svc.cluster.local:9090/
        - --metrics-relist-interval=1m
        - --v=4
        - --config=/etc/adapter/config.yaml
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          capabilities:
            drop: ["ALL"]
        resources:
          requests:
            memory: "200Mi"
            cpu: "100m"
          limits:
            memory: "400Mi"
            cpu: "200m"
        ports:
        - containerPort: 6443
          name: https
        volumeMounts:
        - name: volume-serving-cert
          mountPath: /var/run/serving-cert
          readOnly: true
        - name: config
          mountPath: /etc/adapter/
          readOnly: true
        - name: tmp
          mountPath: /tmp
      volumes:
      - name: volume-serving-cert
        secret:
          secretName: cm-adapter-serving-certs
      - name: config
        configMap:
          name: adapter-config
      - name: tmp
        emptyDir: {}
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: custom-metrics-apiserver
  namespace: autoscaling-system
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: adapter-config
  namespace: autoscaling-system
data:
  config.yaml: |
    rules:
    - seriesQuery: 'http_requests_per_second{namespace!="",pod!=""}'
      resources:
        overrides:
          namespace: {resource: "namespace"}
          pod: {resource: "pod"}
      name:
        matches: "^(.*)_per_second"
        as: "${1}_per_second"
      metricsQuery: 'sum(<<.Series>>{<<.LabelMatchers>>}) by (<<.GroupBy>>)'

    - seriesQuery: 'tenant_resource_usage{namespace!="",tenant_id!=""}'
      resources:
        overrides:
          namespace: {resource: "namespace"}
          tenant_id: {resource: "tenant"}
      name:
        matches: "^tenant_(.*)_usage"
        as: "tenant_${1}_usage"
      metricsQuery: 'sum(<<.Series>>{<<.LabelMatchers>>}) by (<<.GroupBy>>)'

    - seriesQuery: 'database_connections_active{namespace!=""}'
      resources:
        overrides:
          namespace: {resource: "namespace"}
      name:
        matches: "^database_(.*)_active"
        as: "database_${1}_active"
      metricsQuery: 'sum(<<.Series>>{<<.LabelMatchers>>}) by (<<.GroupBy>>)'
---
# Tenant-Specific HPA Template
apiVersion: v1
kind: ConfigMap
metadata:
  name: tenant-hpa-template
  namespace: autoscaling-system
data:
  hpa-template.yaml: |
    apiVersion: autoscaling/v2
    kind: HorizontalPodAutoscaler
    metadata:
      name: tenant-{{TENANT_ID}}-backend-hpa
      namespace: tenant-{{TENANT_ID}}
      labels:
        tenant-id: "{{TENANT_ID}}"
        component: autoscaling
    spec:
      scaleTargetRef:
        apiVersion: apps/v1
        kind: Deployment
        name: tenant-{{TENANT_ID}}-backend
      minReplicas: 2
      maxReplicas: 20
      metrics:
      - type: Resource
        resource:
          name: cpu
          target:
            type: Utilization
            averageUtilization: 70
      - type: Resource
        resource:
          name: memory
          target:
            type: Utilization
            averageUtilization: 80
      - type: Pods
        pods:
          metric:
            name: http_requests_per_second
          target:
            type: AverageValue
            averageValue: "100"
      - type: Object
        object:
          metric:
            name: database_connections_active
          describedObject:
            apiVersion: v1
            kind: Service
            name: tenant-{{TENANT_ID}}-backend
          target:
            type: Value
            value: "50"
      behavior:
        scaleDown:
          stabilizationWindowSeconds: 300
          policies:
          - type: Percent
            value: 10
            periodSeconds: 60
          - type: Pods
            value: 2
            periodSeconds: 60
          selectPolicy: Min
        scaleUp:
          stabilizationWindowSeconds: 60
          policies:
          - type: Percent
            value: 50
            periodSeconds: 60
          - type: Pods
            value: 4
            periodSeconds: 60
          selectPolicy: Max
    ---
    apiVersion: autoscaling/v2
    kind: HorizontalPodAutoscaler
    metadata:
      name: tenant-{{TENANT_ID}}-frontend-hpa
      namespace: tenant-{{TENANT_ID}}
      labels:
        tenant-id: "{{TENANT_ID}}"
        component: autoscaling
    spec:
      scaleTargetRef:
        apiVersion: apps/v1
        kind: Deployment
        name: tenant-{{TENANT_ID}}-frontend
      minReplicas: 1
      maxReplicas: 10
      metrics:
      - type: Resource
        resource:
          name: cpu
          target:
            type: Utilization
            averageUtilization: 70
      - type: Resource
        resource:
          name: memory
          target:
            type: Utilization
            averageUtilization: 80
      behavior:
        scaleDown:
          stabilizationWindowSeconds: 300
          policies:
          - type: Percent
            value: 25
            periodSeconds: 60
        scaleUp:
          stabilizationWindowSeconds: 60
          policies:
          - type: Percent
            value: 100
            periodSeconds: 60
---
# Pod Disruption Budgets Template
apiVersion: v1
kind: ConfigMap
metadata:
  name: tenant-pdb-template
  namespace: autoscaling-system
data:
  pdb-template.yaml: |
    apiVersion: policy/v1
    kind: PodDisruptionBudget
    metadata:
      name: tenant-{{TENANT_ID}}-backend-pdb
      namespace: tenant-{{TENANT_ID}}
      labels:
        tenant-id: "{{TENANT_ID}}"
        component: availability
    spec:
      minAvailable: 1
      selector:
        matchLabels:
          app: tenant-{{TENANT_ID}}-backend
    ---
    apiVersion: policy/v1
    kind: PodDisruptionBudget
    metadata:
      name: tenant-{{TENANT_ID}}-frontend-pdb
      namespace: tenant-{{TENANT_ID}}
      labels:
        tenant-id: "{{TENANT_ID}}"
        component: availability
    spec:
      minAvailable: 1
      selector:
        matchLabels:
          app: tenant-{{TENANT_ID}}-frontend
---
# Database Performance Optimizer
apiVersion: apps/v1
kind: Deployment
metadata:
  name: database-performance-optimizer
  namespace: autoscaling-system
  labels:
    app: database-performance-optimizer
spec:
  replicas: 1
  selector:
    matchLabels:
      app: database-performance-optimizer
  template:
    metadata:
      labels:
        app: database-performance-optimizer
    spec:
      serviceAccountName: database-optimizer
      securityContext:
        runAsNonRoot: true
        runAsUser: 65534
        runAsGroup: 65534
        fsGroup: 65534
        seccompProfile:
          type: RuntimeDefault
      containers:
      - name: optimizer
        image: python:3.11-alpine
        command: ["/bin/sh", "-c"]
        args:
        - |
          pip install mysql-connector-python prometheus_client kubernetes
          cat > /app/db_optimizer.py << 'EOF'
          #!/usr/bin/env python3
          import time
          import mysql.connector
          from prometheus_client import Gauge, Counter, start_http_server
          from kubernetes import client, config

          # Prometheus metrics
          slow_queries = Gauge('database_slow_queries_total', 'Total slow queries', ['tenant'])
          connection_pool_usage = Gauge('database_connection_pool_usage', 'Connection pool usage', ['tenant'])
          query_cache_hit_rate = Gauge('database_query_cache_hit_rate', 'Query cache hit rate', ['tenant'])

          # Load Kubernetes config
          try:
              config.load_incluster_config()
          except:
              config.load_kube_config()

          def optimize_database_performance():
              while True:
                  try:
                      # Get all tenant namespaces
                      v1 = client.CoreV1Api()
                      namespaces = v1.list_namespace()

                      for ns in namespaces.items:
                          if ns.metadata.name.startswith('tenant-'):
                              tenant_id = ns.metadata.name.replace('tenant-', '')
                              optimize_tenant_database(tenant_id)

                      time.sleep(300)  # Run every 5 minutes
                  except Exception as e:
                      print(f"Error in optimization loop: {e}")
                      time.sleep(60)

          def optimize_tenant_database(tenant_id):
              try:
                  # Connect to database
                  conn = mysql.connector.connect(
                      host='production-aurora-serverless.cluster-cpmagwki2kv8.eu-central-1.rds.amazonaws.com',
                      port=3306,
                      user='admin',
                      password='&BZzY_<AK(=a*UhZ',
                      database='architrave',
                      ssl_disabled=False
                  )

                  cursor = conn.cursor()

                  # Analyze slow queries
                  cursor.execute("SELECT COUNT(*) FROM mysql.slow_log WHERE start_time > NOW() - INTERVAL 1 HOUR")
                  slow_query_count = cursor.fetchone()[0]
                  slow_queries.labels(tenant=tenant_id).set(slow_query_count)

                  # Check connection pool usage
                  cursor.execute("SHOW STATUS LIKE 'Threads_connected'")
                  connected_threads = int(cursor.fetchone()[1])
                  cursor.execute("SHOW VARIABLES LIKE 'max_connections'")
                  max_connections = int(cursor.fetchone()[1])
                  pool_usage = (connected_threads / max_connections) * 100
                  connection_pool_usage.labels(tenant=tenant_id).set(pool_usage)

                  # Check query cache hit rate
                  cursor.execute("SHOW STATUS LIKE 'Qcache_hits'")
                  cache_hits = int(cursor.fetchone()[1])
                  cursor.execute("SHOW STATUS LIKE 'Com_select'")
                  selects = int(cursor.fetchone()[1])
                  if selects > 0:
                      hit_rate = (cache_hits / (cache_hits + selects)) * 100
                      query_cache_hit_rate.labels(tenant=tenant_id).set(hit_rate)

                  # Optimize tenant-specific queries
                  optimize_tenant_queries(cursor, tenant_id)

                  conn.close()

              except Exception as e:
                  print(f"Error optimizing database for tenant {tenant_id}: {e}")

          def optimize_tenant_queries(cursor, tenant_id):
              # Add indexes for common queries
              optimization_queries = [
                  f"CREATE INDEX IF NOT EXISTS idx_tenant_documents_created ON documents(tenant_id, created_at)",
                  f"CREATE INDEX IF NOT EXISTS idx_tenant_users_active ON users(tenant_id, active, last_login)",
                  f"CREATE INDEX IF NOT EXISTS idx_tenant_sessions ON user_sessions(tenant_id, expires_at)",
                  f"ANALYZE TABLE documents",
                  f"ANALYZE TABLE users",
                  f"OPTIMIZE TABLE user_sessions"
              ]

              for query in optimization_queries:
                  try:
                      cursor.execute(query)
                  except Exception as e:
                      print(f"Optimization query failed: {query}, Error: {e}")

          if __name__ == '__main__':
              # Start Prometheus metrics server
              start_http_server(8000)

              # Start optimization loop
              optimize_database_performance()
          EOF

          python /app/db_optimizer.py
        ports:
        - containerPort: 8000
          name: metrics
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          capabilities:
            drop: ["ALL"]
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "200m"
        volumeMounts:
        - name: tmp
          mountPath: /tmp
        - name: app
          mountPath: /app
      volumes:
      - name: tmp
        emptyDir: {}
      - name: app
        emptyDir: {}
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: database-optimizer
  namespace: autoscaling-system
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: database-optimizer
rules:
- apiGroups: [""]
  resources: ["namespaces"]
  verbs: ["get", "list", "watch"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: database-optimizer
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: database-optimizer
subjects:
- kind: ServiceAccount
  name: database-optimizer
  namespace: autoscaling-system
