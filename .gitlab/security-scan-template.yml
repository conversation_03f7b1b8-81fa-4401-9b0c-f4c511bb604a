# GitLab CI/CD Security Scanning Template
# This template provides reusable jobs for security scanning

# TFSec scanning job
.tfsec_scan:
  stage: security
  image: aquasec/tfsec:latest
  before_script:
    - apt-get update && apt-get install -y jq || true
  script:
    - echo "Running TFSec security scan..."
    - tfsec . --format=default || true
    - |
      # Run TFSec with JSON output and handle errors
      TFSEC_OUTPUT=$(tfsec . --format=json || echo '{"results":[]}')
      CRITICAL_COUNT=$(echo "$TFSEC_OUTPUT" | jq '.results | map(select(.severity == "CRITICAL")) | length' || echo "0")
      HIGH_COUNT=$(echo "$TFSEC_OUTPUT" | jq '.results | map(select(.severity == "HIGH")) | length' || echo "0")
      MEDIUM_COUNT=$(echo "$TFSEC_OUTPUT" | jq '.results | map(select(.severity == "MEDIUM")) | length' || echo "0")
      LOW_COUNT=$(echo "$TFSEC_OUTPUT" | jq '.results | map(select(.severity == "LOW")) | length' || echo "0")

      echo "TFSec Results Summary:"
      echo "  Critical issues: $CRITICAL_COUNT"
      echo "  High issues: $HIGH_COUNT"
      echo "  Medium issues: $MEDIUM_COUNT"
      echo "  Low issues: $LOW_COUNT"

      # Display results only, don't fail the job
      if [ "$CRITICAL_COUNT" -gt 0 ]; then
        echo "Found $CRITICAL_COUNT critical security issues!"
      fi

# Checkov scanning job
.checkov_scan:
  stage: security
  image: bridgecrew/checkov:latest
  before_script:
    - apt-get update && apt-get install -y jq || true
  script:
    - echo "Running Checkov security scan..."
    # Run Checkov directly without shell script wrapper
    - checkov -d . --quiet --framework terraform --output cli --soft-fail || true
    - |
      # Run Checkov with JSON output and handle errors
      CHECKOV_OUTPUT=$(checkov -d . --quiet --framework terraform --output json --soft-fail || echo '{"results":{"failed_checks":[]},"summary":{"passed":0,"failed":0,"skipped":0,"parsing_errors":0,"resource_count":0}}')
      CRITICAL_COUNT=$(echo "$CHECKOV_OUTPUT" | jq '.results.failed_checks | map(select(.severity == "CRITICAL")) | length' || echo "0")
      HIGH_COUNT=$(echo "$CHECKOV_OUTPUT" | jq '.results.failed_checks | map(select(.severity == "HIGH")) | length' || echo "0")
      MEDIUM_COUNT=$(echo "$CHECKOV_OUTPUT" | jq '.results.failed_checks | map(select(.severity == "MEDIUM")) | length' || echo "0")
      LOW_COUNT=$(echo "$CHECKOV_OUTPUT" | jq '.results.failed_checks | map(select(.severity == "LOW")) | length' || echo "0")
      PASSED_COUNT=$(echo "$CHECKOV_OUTPUT" | jq '.summary.passed' || echo "0")

      echo "Checkov Results Summary:"
      echo "  Passed checks: $PASSED_COUNT"
      echo "  Critical issues: $CRITICAL_COUNT"
      echo "  High issues: $HIGH_COUNT"
      echo "  Medium issues: $MEDIUM_COUNT"
      echo "  Low issues: $LOW_COUNT"

      # Display results only, don't fail the job
      if [ "$CRITICAL_COUNT" -gt 0 ]; then
        echo "Found $CRITICAL_COUNT critical security issues!"
      fi

# TFLint scanning job
.tflint_scan:
  stage: security
  image: ghcr.io/terraform-linters/tflint:latest
  before_script:
    - apt-get update && apt-get install -y jq || true
  script:
    - echo "Running TFLint scan..."
    # Initialize TFLint
    - tflint --init || true
    # Use --filter instead of command line arguments (for v0.47+)
    - tflint --filter=. || true
    - |
      # Run TFLint with JSON output and handle errors
      TFLINT_OUTPUT=$(tflint --filter=. --format=json || echo '{"issues":[]}')
      ERROR_COUNT=$(echo "$TFLINT_OUTPUT" | jq '.issues | map(select(.rule.severity == "error")) | length' || echo "0")
      WARNING_COUNT=$(echo "$TFLINT_OUTPUT" | jq '.issues | map(select(.rule.severity == "warning")) | length' || echo "0")
      NOTICE_COUNT=$(echo "$TFLINT_OUTPUT" | jq '.issues | map(select(.rule.severity == "notice")) | length' || echo "0")

      echo "TFLint Results Summary:"
      echo "  Errors: $ERROR_COUNT"
      echo "  Warnings: $WARNING_COUNT"
      echo "  Notices: $NOTICE_COUNT"

      # Display results only, don't fail the job
      if [ "$ERROR_COUNT" -gt 0 ]; then
        echo "Found $ERROR_COUNT errors!"
      fi

# OPA Policy Check job
.opa_policy_check:
  stage: security
  image: openpolicyagent/conftest:latest
  before_script:
    - apt-get update && apt-get install -y wget unzip jq || true
    - wget -O terraform.zip https://releases.hashicorp.com/terraform/1.5.7/terraform_1.5.7_linux_amd64.zip || true
    - unzip terraform.zip && mv terraform /usr/local/bin/ || true
    # Create policies directory and basic policy file if they don't exist
    - mkdir -p policies
    - |
      if [ ! -f "policies/terraform.rego" ]; then
        cat > policies/terraform.rego << 'EOF'
      package terraform

      deny[msg] {
        resource := input.resource_changes[_]
        resource.change.actions[_] == "create"
        resource.type == "aws_s3_bucket"
        not resource.change.after.acl == "private"
        msg = sprintf("S3 bucket '%s' is not private", [resource.address])
      }
      EOF
      fi
  script:
    - echo "Running OPA policy check..."
    # Run Terraform commands to generate plan
    - terraform init -reconfigure -backend=false || true
    - terraform plan -out=tfplan || true
    - terraform show -json tfplan > tfplan.json || true
    # Run conftest directly with the policy file
    - conftest test tfplan.json -p policies/terraform.rego || true
    - |
      # Count violations
      if [ -f "tfplan.json" ]; then
        VIOLATIONS=$(conftest test tfplan.json -p policies/terraform.rego -o json 2>/dev/null | jq 'length' || echo "0")
        echo "OPA Policy Check Results Summary:"
        echo "  Policy violations: $VIOLATIONS"

        if [ "$VIOLATIONS" -gt 0 ]; then
          echo "Found $VIOLATIONS policy violations!"
        else
          echo "No policy violations found."
        fi
      else
        echo "No tfplan.json file found. Skipping policy violation count."
      fi

      echo "OPA Policy Check completed"

# Terratest job
.terratest:
  stage: security
  image: golang:1.20
  script:
    - echo "Running Terratest tests..."
    - apt-get update && apt-get install -y terraform
    - cd tests/terratest
    - go mod tidy
    - go test -v ./... || true

# Infrastructure Drift Detection job
.drift_detection:
  stage: security
  script:
    - echo "Running infrastructure drift detection..."
    - ./scripts/drift_detection.sh || true

# Infracost job
.infracost:
  stage: security
  image: infracost/infracost:latest
  before_script:
    - apt-get update && apt-get install -y jq bc || true
  script:
    - echo "Running infrastructure cost estimation..."
    # Set environment variables properly
    - export INFRACOST_API_KEY="00000000000000000000000000000000"
    # Fix the INFRACOST_NO_COLOR environment variable issue
    - export INFRACOST_NO_COLOR="true"
    # Run infracost directly
    - infracost breakdown --path . --format table || true
    - |
      # Generate JSON output for parsing
      infracost breakdown --path . --format json > infracost.json || true

      if [ -f "infracost.json" ]; then
        # Extract cost information
        TOTAL_MONTHLY_COST=$(jq -r '.totalMonthlyCost // 0' infracost.json || echo "0")
        TOTAL_MONTHLY_COST=$(printf "%.2f" "$TOTAL_MONTHLY_COST" 2>/dev/null || echo "0.00")

        # Get resource count
        RESOURCE_COUNT=$(jq -r '.projects[0].breakdown.resources | length' infracost.json || echo "0")

        echo "Infracost Results Summary:"
        echo "  Total Monthly Cost: $TOTAL_MONTHLY_COST USD"
        echo "  Resource Count: $RESOURCE_COUNT"

        # List top 5 most expensive resources
        echo "\nTop 5 Most Expensive Resources:"
        jq -r '.projects[0].breakdown.resources | sort_by(-.monthlyCost) | .[0:5] | .[] | "  \(.name): \(.monthlyCost) USD/month"' infracost.json 2>/dev/null || echo "  Unable to determine top resources"
      else
        echo "No infracost.json file found. Unable to provide cost summary."
      fi

      echo "Infracost estimation completed"

# Container scanning job for application
.container_scanning:
  stage: test
  image:
    name: aquasec/trivy:latest
    entrypoint: [""]
  variables:
    DOCKER_IMAGE: ${DOCKER_REGISTRY}/sample-app:${APP_VERSION}
    TRIVY_NO_PROGRESS: "true"
    TRIVY_CACHE_DIR: .trivycache/
    TRIVY_EXIT_CODE: "1"
    TRIVY_SEVERITY: "CRITICAL,HIGH"
    TRIVY_TIMEOUT: "10m"
  script:
    - trivy --version
    - echo "Scanning Docker image: $DOCKER_IMAGE"
    - trivy image --exit-code $TRIVY_EXIT_CODE --severity $TRIVY_SEVERITY --no-progress --timeout $TRIVY_TIMEOUT $DOCKER_IMAGE
  cache:
    paths:
      - .trivycache/
  rules:
    - if: '$CI_COMMIT_BRANCH == "main" || $CI_PIPELINE_SOURCE == "merge_request_event"'
      changes:
        - "src/**/*"
        - "Dockerfile"
        - "package.json"
        - "package-lock.json"

# Dependency scanning job for application
.dependency_scanning:
  stage: test
  image: node:18-alpine
  script:
    - apk add --no-cache curl
    - curl -sSfL https://raw.githubusercontent.com/anchore/grype/main/install.sh | sh -s -- -b /usr/local/bin
    - npm ci
    - grype dir:. -o json > dependency-scan.json
    - cat dependency-scan.json | grep -i "critical\|high" || echo "No critical or high vulnerabilities found"
  artifacts:
    paths:
      - dependency-scan.json
    expire_in: 1 week
  rules:
    - if: '$CI_COMMIT_BRANCH == "main" || $CI_PIPELINE_SOURCE == "merge_request_event"'
      changes:
        - "package.json"
        - "package-lock.json"

# Static Application Security Testing (SAST) job for application
.sast:
  stage: test
  image: node:18-alpine
  script:
    - apk add --no-cache python3 py3-pip
    - pip3 install njsscan
    - njsscan --json -o njsscan-report.json src/
    - cat njsscan-report.json | grep -i "critical\|high" || echo "No critical or high vulnerabilities found"
  artifacts:
    paths:
      - njsscan-report.json
    expire_in: 1 week
  rules:
    - if: '$CI_COMMIT_BRANCH == "main" || $CI_PIPELINE_SOURCE == "merge_request_event"'
      changes:
        - "src/**/*.js"
        - "src/**/*.ts"
