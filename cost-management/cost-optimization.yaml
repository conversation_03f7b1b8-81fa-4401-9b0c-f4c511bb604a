apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: cost-alerts
  namespace: monitoring
spec:
  groups:
  - name: cost.rules
    rules:
    - alert: HighCostSpike
      expr: |
        sum(aws_cost_explorer_total_cost) > 1000
      for: 1h
      labels:
        severity: warning
      annotations:
        summary: High cost spike detected
        description: AWS costs have exceeded $1000 in the last hour
    - alert: ResourceWaste
      expr: |
        sum(container_cpu_usage_seconds_total) / sum(container_cpu_requests_total) < 0.3
      for: 24h
      labels:
        severity: warning
      annotations:
        summary: Low resource utilization detected
        description: CPU utilization is below 30% for 24 hours
---
apiVersion: batch/v1
kind: CronJob
metadata:
  name: resource-cleanup
  namespace: kube-system
spec:
  schedule: "0 0 * * *"  # Run daily at midnight
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: cleanup
            image: bitnami/kubectl:latest
            command:
            - /bin/sh
            - -c
            - |
              # Clean up completed jobs older than 7 days
              kubectl delete jobs --field-selector status.successful=1 --older-than=168h
              
              # Clean up failed pods older than 3 days
              kubectl delete pods --field-selector status.phase=Failed --older-than=72h
              
              # Clean up unused PVCs
              kubectl get pvc --all-namespaces -o json | jq '.items[] | select(.status.phase=="Bound" and (.metadata.annotations["kubernetes.io/created-by"] | contains("manual"))) | .metadata.name' | xargs -I {} kubectl delete pvc {} --force
              
              # Clean up unused ConfigMaps
              kubectl get configmaps --all-namespaces -o json | jq '.items[] | select(.metadata.ownerReferences == null) | .metadata.name' | xargs -I {} kubectl delete configmap {} --force
          restartPolicy: OnFailure
---
apiVersion: autoscaling.k8s.io/v2
kind: HorizontalPodAutoscaler
metadata:
  name: cost-optimized-hpa
  namespace: default
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: app
  minReplicas: 1
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 0
      policies:
      - type: Percent
        value: 100
        periodSeconds: 60 