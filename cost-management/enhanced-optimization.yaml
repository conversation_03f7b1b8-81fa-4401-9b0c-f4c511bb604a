apiVersion: batch/v1
kind: CronJob
metadata:
  name: cost-optimizer
  namespace: kube-system
spec:
  schedule: "0 */4 * * *"  # Run every 4 hours
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: cost-analyzer
            image: cost-optimizer:latest
            command:
            - /bin/sh
            - -c
            - |
              # Analyze resource usage patterns
              echo "Analyzing resource usage patterns..."
              python3 /scripts/analyze_usage.py --output usage-patterns.json
              
              # Generate optimization recommendations
              echo "Generating optimization recommendations..."
              python3 /scripts/generate_recommendations.py --input usage-patterns.json --output recommendations.json
              
              # Apply automatic optimizations
              echo "Applying automatic optimizations..."
              python3 /scripts/apply_optimizations.py --input recommendations.json
              
              # Send results to monitoring
              curl -X POST -H "Content-Type: application/json" -d @recommendations.json http://cost-monitor:8080/api/v1/optimizations
          restartPolicy: OnFailure
---
apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: enhanced-cost-alerts
  namespace: monitoring
spec:
  groups:
  - name: cost.rules
    rules:
    - alert: CostSpike
      expr: |
        sum(aws_cost_explorer_total_cost) > 1000
      for: 1h
      labels:
        severity: warning
      annotations:
        summary: High cost spike detected
        description: AWS costs have exceeded $1000 in the last hour

    - alert: ResourceWaste
      expr: |
        sum(container_cpu_usage_seconds_total) / sum(container_cpu_requests_total) < 0.3
      for: 24h
      labels:
        severity: warning
      annotations:
        summary: Low resource utilization detected
        description: CPU utilization is below 30% for 24 hours

    - alert: StorageCost
      expr: |
        sum(aws_s3_storage_cost) > 500
      for: 24h
      labels:
        severity: warning
      annotations:
        summary: High storage costs
        description: S3 storage costs have exceeded $500 in 24 hours

    - alert: NetworkCost
      expr: |
        sum(aws_network_cost) > 200
      for: 24h
      labels:
        severity: warning
      annotations:
        summary: High network costs
        description: Network costs have exceeded $200 in 24 hours

    - alert: IdleResources
      expr: |
        sum(container_cpu_usage_seconds_total) == 0
      for: 24h
      labels:
        severity: warning
      annotations:
        summary: Idle resources detected
        description: Resources have been idle for 24 hours
---
apiVersion: autoscaling.k8s.io/v2
kind: HorizontalPodAutoscaler
metadata:
  name: cost-optimized-hpa
  namespace: default
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: app
  minReplicas: 1
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 0
      policies:
      - type: Percent
        value: 100
        periodSeconds: 60
---
apiVersion: batch/v1
kind: CronJob
metadata:
  name: resource-cleanup
  namespace: kube-system
spec:
  schedule: "0 0 * * *"  # Run daily at midnight
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: cleanup
            image: bitnami/kubectl:latest
            command:
            - /bin/sh
            - -c
            - |
              # Clean up completed jobs older than 7 days
              kubectl delete jobs --field-selector status.successful=1 --older-than=168h
              
              # Clean up failed pods older than 3 days
              kubectl delete pods --field-selector status.phase=Failed --older-than=72h
              
              # Clean up unused PVCs
              kubectl get pvc --all-namespaces -o json | jq '.items[] | select(.status.phase=="Bound" and (.metadata.annotations["kubernetes.io/created-by"] | contains("manual"))) | .metadata.name' | xargs -I {} kubectl delete pvc {} --force
              
              # Clean up unused ConfigMaps
              kubectl get configmaps --all-namespaces -o json | jq '.items[] | select(.metadata.ownerReferences == null) | .metadata.name' | xargs -I {} kubectl delete configmap {} --force
              
              # Clean up old logs
              find /var/log -type f -name "*.log" -mtime +7 -delete
              
              # Clean up old backups
              find /backups -type f -mtime +30 -delete
              
              # Clean up old Docker images
              docker image prune -f --filter "until=168h"
          restartPolicy: OnFailure 