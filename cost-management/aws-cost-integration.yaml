---
# Real-time AWS Cost Integration with Cost Explorer API
apiVersion: v1
kind: Namespace
metadata:
  name: cost-management
  labels:
    pod-security.kubernetes.io/enforce: restricted
    pod-security.kubernetes.io/audit: restricted
    pod-security.kubernetes.io/warn: restricted
---
# AWS Cost Explorer Integration
apiVersion: apps/v1
kind: Deployment
metadata:
  name: aws-cost-explorer
  namespace: cost-management
  labels:
    app: aws-cost-explorer
spec:
  replicas: 2
  selector:
    matchLabels:
      app: aws-cost-explorer
  template:
    metadata:
      labels:
        app: aws-cost-explorer
    spec:
      serviceAccountName: aws-cost-explorer
      securityContext:
        runAsNonRoot: true
        runAsUser: 65534
        runAsGroup: 65534
        fsGroup: 65534
        seccompProfile:
          type: RuntimeDefault
      containers:
      - name: cost-explorer
        image: python:3.11-alpine
        command: ["/bin/sh", "-c"]
        args:
        - |
          pip install boto3 flask kubernetes prometheus_client redis mysql-connector-python
          cat > /app/cost_explorer.py << 'EOF'
          #!/usr/bin/env python3
          import boto3
          import json
          import time
          import redis
          import mysql.connector
          from datetime import datetime, timedelta
          from flask import Flask, request, jsonify
          from kubernetes import client, config
          from prometheus_client import Gauge, Counter, start_http_server
          
          app = Flask(__name__)
          
          # Prometheus metrics
          tenant_cost_gauge = Gauge('tenant_cost_usd', 'Tenant cost in USD', ['tenant_id', 'service', 'period'])
          cost_api_calls = Counter('cost_api_calls_total', 'Total Cost Explorer API calls', ['operation'])
          cost_calculation_time = Gauge('cost_calculation_duration_seconds', 'Time to calculate costs')
          
          # AWS clients
          ce_client = boto3.client('ce', region_name='us-east-1')  # Cost Explorer is only in us-east-1
          ec2_client = boto3.client('ec2', region_name='eu-central-1')
          s3_client = boto3.client('s3', region_name='eu-central-1')
          rds_client = boto3.client('rds', region_name='eu-central-1')
          
          # Redis for caching
          redis_client = redis.Redis(
              host='redis-cluster.caching-system.svc.cluster.local',
              port=6379,
              password='redis-cluster-password',
              decode_responses=True
          )
          
          # Load Kubernetes config
          try:
              config.load_incluster_config()
          except:
              config.load_kube_config()
          
          k8s_client = client.CoreV1Api()
          
          @app.route('/api/costs/tenant/<tenant_id>', methods=['GET'])
          def get_tenant_costs(tenant_id):
              """Get comprehensive cost breakdown for a tenant"""
              cost_api_calls.labels(operation='get_tenant_costs').inc()
              
              try:
                  start_time = time.time()
                  
                  # Get query parameters
                  period = request.args.get('period', 'daily')  # daily, weekly, monthly
                  start_date = request.args.get('start_date')
                  end_date = request.args.get('end_date')
                  
                  if not start_date:
                      if period == 'daily':
                          start_date = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
                      elif period == 'weekly':
                          start_date = (datetime.now() - timedelta(days=7)).strftime('%Y-%m-%d')
                      else:  # monthly
                          start_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
                  
                  if not end_date:
                      end_date = datetime.now().strftime('%Y-%m-%d')
                  
                  # Check cache first
                  cache_key = f"tenant_costs:{tenant_id}:{period}:{start_date}:{end_date}"
                  cached_result = redis_client.get(cache_key)
                  
                  if cached_result:
                      cost_calculation_time.set(time.time() - start_time)
                      return jsonify(json.loads(cached_result))
                  
                  # Calculate costs from AWS
                  costs = calculate_tenant_costs(tenant_id, start_date, end_date, period)
                  
                  # Cache result for 1 hour
                  redis_client.setex(cache_key, 3600, json.dumps(costs))
                  
                  # Update Prometheus metrics
                  for service, cost in costs.get('services', {}).items():
                      tenant_cost_gauge.labels(tenant_id=tenant_id, service=service, period=period).set(cost)
                  
                  cost_calculation_time.set(time.time() - start_time)
                  
                  return jsonify(costs)
              
              except Exception as e:
                  return jsonify({"error": str(e)}), 500
          
          @app.route('/api/costs/platform', methods=['GET'])
          def get_platform_costs():
              """Get platform-wide cost breakdown"""
              cost_api_calls.labels(operation='get_platform_costs').inc()
              
              try:
                  period = request.args.get('period', 'monthly')
                  start_date = request.args.get('start_date')
                  end_date = request.args.get('end_date')
                  
                  if not start_date:
                      start_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
                  if not end_date:
                      end_date = datetime.now().strftime('%Y-%m-%d')
                  
                  # Get all tenant namespaces
                  namespaces = k8s_client.list_namespace()
                  tenant_ids = [ns.metadata.name.replace('tenant-', '') 
                               for ns in namespaces.items 
                               if ns.metadata.name.startswith('tenant-')]
                  
                  platform_costs = {
                      "period": period,
                      "start_date": start_date,
                      "end_date": end_date,
                      "total_cost": 0,
                      "tenants": {},
                      "services": {},
                      "unallocated": 0
                  }
                  
                  # Calculate costs for each tenant
                  for tenant_id in tenant_ids:
                      tenant_costs = calculate_tenant_costs(tenant_id, start_date, end_date, period)
                      platform_costs["tenants"][tenant_id] = tenant_costs
                      platform_costs["total_cost"] += tenant_costs.get("total_cost", 0)
                      
                      # Aggregate service costs
                      for service, cost in tenant_costs.get("services", {}).items():
                          platform_costs["services"][service] = platform_costs["services"].get(service, 0) + cost
                  
                  # Calculate unallocated costs
                  total_aws_costs = get_total_aws_costs(start_date, end_date)
                  platform_costs["unallocated"] = max(0, total_aws_costs - platform_costs["total_cost"])
                  
                  return jsonify(platform_costs)
              
              except Exception as e:
                  return jsonify({"error": str(e)}), 500
          
          @app.route('/api/costs/forecast/<tenant_id>', methods=['GET'])
          def forecast_tenant_costs(tenant_id):
              """Forecast future costs for a tenant"""
              cost_api_calls.labels(operation='forecast_costs').inc()
              
              try:
                  days_ahead = int(request.args.get('days', 30))
                  
                  # Get historical costs for trend analysis
                  end_date = datetime.now().strftime('%Y-%m-%d')
                  start_date = (datetime.now() - timedelta(days=90)).strftime('%Y-%m-%d')
                  
                  # Use AWS Cost Explorer forecast API
                  response = ce_client.get_cost_forecast(
                      TimePeriod={
                          'Start': end_date,
                          'End': (datetime.now() + timedelta(days=days_ahead)).strftime('%Y-%m-%d')
                      },
                      Metric='BLENDED_COST',
                      Granularity='DAILY',
                      Filter={
                          'Dimensions': {
                              'Key': 'SERVICE',
                              'Values': ['Amazon Elastic Compute Cloud - Compute', 'Amazon Simple Storage Service', 'Amazon Relational Database Service']
                          }
                      }
                  )
                  
                  forecast = {
                      "tenant_id": tenant_id,
                      "forecast_period_days": days_ahead,
                      "forecast_start": end_date,
                      "forecast_end": (datetime.now() + timedelta(days=days_ahead)).strftime('%Y-%m-%d'),
                      "total_forecast": float(response['Total']['Amount']),
                      "currency": response['Total']['Unit'],
                      "daily_forecast": []
                  }
                  
                  for result in response['ForecastResultsByTime']:
                      forecast["daily_forecast"].append({
                          "date": result['TimePeriod']['Start'],
                          "amount": float(result['MeanValue'])
                      })
                  
                  return jsonify(forecast)
              
              except Exception as e:
                  return jsonify({"error": str(e)}), 500
          
          @app.route('/api/costs/optimization/<tenant_id>', methods=['GET'])
          def get_cost_optimization_recommendations(tenant_id):
              """Get cost optimization recommendations for a tenant"""
              cost_api_calls.labels(operation='optimization_recommendations').inc()
              
              try:
                  recommendations = []
                  
                  # Get Right Sizing recommendations
                  rightsizing_response = ce_client.get_rightsizing_recommendation(
                      Service='AmazonEC2',
                      Configuration={
                          'BenefitsConsidered': True,
                          'RecommendationTarget': 'SAME_INSTANCE_FAMILY'
                      }
                  )
                  
                  for rec in rightsizing_response.get('RightsizingRecommendations', []):
                      if tenant_id in rec.get('CurrentInstance', {}).get('Tags', {}).get('tenant-id', ''):
                          recommendations.append({
                              "type": "rightsizing",
                              "resource": rec['CurrentInstance']['ResourceId'],
                              "current_type": rec['CurrentInstance']['InstanceType'],
                              "recommended_type": rec['RightsizingType'],
                              "estimated_monthly_savings": float(rec['EstimatedMonthlySavings']['Amount']),
                              "description": f"Right-size instance {rec['CurrentInstance']['ResourceId']}"
                          })
                  
                  # Get Reserved Instance recommendations
                  ri_response = ce_client.get_reservation_purchase_recommendation(
                      Service='AmazonEC2',
                      AccountScope='PAYER'
                  )
                  
                  for rec in ri_response.get('Recommendations', []):
                      recommendations.append({
                          "type": "reserved_instance",
                          "instance_type": rec['RecommendationDetails']['InstanceDetails']['EC2InstanceDetails']['InstanceType'],
                          "estimated_monthly_savings": float(rec['RecommendationDetails']['EstimatedMonthlySavingsAmount']),
                          "upfront_cost": float(rec['RecommendationDetails']['UpfrontCost']),
                          "description": f"Purchase Reserved Instance for {rec['RecommendationDetails']['InstanceDetails']['EC2InstanceDetails']['InstanceType']}"
                      })
                  
                  # Add custom recommendations based on usage patterns
                  custom_recommendations = get_custom_recommendations(tenant_id)
                  recommendations.extend(custom_recommendations)
                  
                  return jsonify({
                      "tenant_id": tenant_id,
                      "recommendations": recommendations,
                      "total_potential_savings": sum(r.get('estimated_monthly_savings', 0) for r in recommendations),
                      "generated_at": datetime.utcnow().isoformat()
                  })
              
              except Exception as e:
                  return jsonify({"error": str(e)}), 500
          
          def calculate_tenant_costs(tenant_id, start_date, end_date, period):
              """Calculate detailed costs for a specific tenant"""
              
              # Get costs by service using Cost Explorer
              response = ce_client.get_cost_and_usage(
                  TimePeriod={
                      'Start': start_date,
                      'End': end_date
                  },
                  Granularity='DAILY' if period == 'daily' else 'MONTHLY',
                  Metrics=['BlendedCost', 'UsageQuantity'],
                  GroupBy=[
                      {'Type': 'DIMENSION', 'Key': 'SERVICE'},
                      {'Type': 'TAG', 'Key': 'tenant-id'}
                  ],
                  Filter={
                      'Tags': {
                          'Key': 'tenant-id',
                          'Values': [tenant_id]
                      }
                  }
              )
              
              costs = {
                  "tenant_id": tenant_id,
                  "period": period,
                  "start_date": start_date,
                  "end_date": end_date,
                  "total_cost": 0,
                  "currency": "USD",
                  "services": {},
                  "daily_breakdown": [],
                  "resource_breakdown": {}
              }
              
              # Process Cost Explorer response
              for result in response['ResultsByTime']:
                  daily_cost = 0
                  for group in result['Groups']:
                      service = group['Keys'][0] if group['Keys'] else 'Unknown'
                      amount = float(group['Metrics']['BlendedCost']['Amount'])
                      
                      costs["services"][service] = costs["services"].get(service, 0) + amount
                      daily_cost += amount
                  
                  costs["daily_breakdown"].append({
                      "date": result['TimePeriod']['Start'],
                      "cost": daily_cost
                  })
                  
                  costs["total_cost"] += daily_cost
              
              # Get detailed resource costs
              costs["resource_breakdown"] = get_tenant_resource_costs(tenant_id, start_date, end_date)
              
              return costs
          
          def get_tenant_resource_costs(tenant_id, start_date, end_date):
              """Get detailed resource-level costs for a tenant"""
              resource_costs = {
                  "ec2_instances": [],
                  "s3_buckets": [],
                  "rds_instances": [],
                  "load_balancers": []
              }
              
              try:
                  # Get EC2 instances for tenant
                  ec2_response = ec2_client.describe_instances(
                      Filters=[
                          {'Name': 'tag:tenant-id', 'Values': [tenant_id]},
                          {'Name': 'instance-state-name', 'Values': ['running', 'stopped']}
                      ]
                  )
                  
                  for reservation in ec2_response['Reservations']:
                      for instance in reservation['Instances']:
                          # Calculate instance cost based on type and usage
                          instance_cost = calculate_ec2_instance_cost(instance, start_date, end_date)
                          resource_costs["ec2_instances"].append({
                              "instance_id": instance['InstanceId'],
                              "instance_type": instance['InstanceType'],
                              "state": instance['State']['Name'],
                              "cost": instance_cost
                          })
                  
                  # Get S3 buckets for tenant
                  s3_buckets = s3_client.list_buckets()
                  for bucket in s3_buckets['Buckets']:
                      bucket_name = bucket['Name']
                      if f"tenant-{tenant_id}" in bucket_name:
                          bucket_cost = calculate_s3_bucket_cost(bucket_name, start_date, end_date)
                          resource_costs["s3_buckets"].append({
                              "bucket_name": bucket_name,
                              "cost": bucket_cost
                          })
                  
                  # Get RDS instances for tenant
                  rds_response = rds_client.describe_db_instances()
                  for db_instance in rds_response['DBInstances']:
                      # Check if RDS instance belongs to tenant (by tags or naming convention)
                      if f"tenant-{tenant_id}" in db_instance.get('DBInstanceIdentifier', ''):
                          db_cost = calculate_rds_instance_cost(db_instance, start_date, end_date)
                          resource_costs["rds_instances"].append({
                              "db_instance_id": db_instance['DBInstanceIdentifier'],
                              "db_instance_class": db_instance['DBInstanceClass'],
                              "engine": db_instance['Engine'],
                              "cost": db_cost
                          })
              
              except Exception as e:
                  print(f"Error getting resource costs: {e}")
              
              return resource_costs
          
          def calculate_ec2_instance_cost(instance, start_date, end_date):
              """Calculate cost for an EC2 instance"""
              # Simplified cost calculation - in production, use actual pricing API
              instance_type = instance['InstanceType']
              
              # Basic hourly rates (simplified)
              hourly_rates = {
                  't3.micro': 0.0104,
                  't3.small': 0.0208,
                  't3.medium': 0.0416,
                  't3.large': 0.0832,
                  'm5.large': 0.096,
                  'm5.xlarge': 0.192,
                  'c5.large': 0.085,
                  'c5.xlarge': 0.17
              }
              
              hourly_rate = hourly_rates.get(instance_type, 0.1)  # Default rate
              
              # Calculate hours between start and end date
              start = datetime.strptime(start_date, '%Y-%m-%d')
              end = datetime.strptime(end_date, '%Y-%m-%d')
              hours = (end - start).total_seconds() / 3600
              
              return hourly_rate * hours
          
          def calculate_s3_bucket_cost(bucket_name, start_date, end_date):
              """Calculate cost for an S3 bucket"""
              try:
                  # Get bucket size
                  response = s3_client.list_objects_v2(Bucket=bucket_name)
                  total_size = sum(obj['Size'] for obj in response.get('Contents', []))
                  
                  # S3 Standard pricing: $0.023 per GB per month
                  gb_size = total_size / (1024 ** 3)
                  days = (datetime.strptime(end_date, '%Y-%m-%d') - datetime.strptime(start_date, '%Y-%m-%d')).days
                  monthly_cost = gb_size * 0.023
                  
                  return monthly_cost * (days / 30)
              except:
                  return 0
          
          def calculate_rds_instance_cost(db_instance, start_date, end_date):
              """Calculate cost for an RDS instance"""
              # Simplified RDS cost calculation
              db_class = db_instance['DBInstanceClass']
              
              # Basic hourly rates for RDS (simplified)
              hourly_rates = {
                  'db.t3.micro': 0.017,
                  'db.t3.small': 0.034,
                  'db.t3.medium': 0.068,
                  'db.m5.large': 0.192,
                  'db.m5.xlarge': 0.384
              }
              
              hourly_rate = hourly_rates.get(db_class, 0.1)
              
              # Calculate hours
              start = datetime.strptime(start_date, '%Y-%m-%d')
              end = datetime.strptime(end_date, '%Y-%m-%d')
              hours = (end - start).total_seconds() / 3600
              
              return hourly_rate * hours
          
          def get_total_aws_costs(start_date, end_date):
              """Get total AWS costs for the account"""
              try:
                  response = ce_client.get_cost_and_usage(
                      TimePeriod={
                          'Start': start_date,
                          'End': end_date
                      },
                      Granularity='MONTHLY',
                      Metrics=['BlendedCost']
                  )
                  
                  total_cost = 0
                  for result in response['ResultsByTime']:
                      for group in result['Groups']:
                          total_cost += float(group['Metrics']['BlendedCost']['Amount'])
                  
                  return total_cost
              except:
                  return 0
          
          def get_custom_recommendations(tenant_id):
              """Generate custom cost optimization recommendations"""
              recommendations = []
              
              try:
                  # Get tenant resource usage from Kubernetes
                  pods = k8s_client.list_namespaced_pod(namespace=f'tenant-{tenant_id}')
                  
                  # Check for over-provisioned resources
                  for pod in pods.items:
                      if pod.status.phase == 'Running':
                          for container in pod.spec.containers:
                              if container.resources and container.resources.requests:
                                  cpu_request = container.resources.requests.get('cpu', '0')
                                  memory_request = container.resources.requests.get('memory', '0')
                                  
                                  # Simple heuristic: if requests are very high, suggest optimization
                                  if 'Gi' in memory_request and float(memory_request.replace('Gi', '')) > 4:
                                      recommendations.append({
                                          "type": "resource_optimization",
                                          "resource": f"{pod.metadata.name}/{container.name}",
                                          "estimated_monthly_savings": 50.0,
                                          "description": f"Consider reducing memory request for {container.name}"
                                      })
              except:
                  pass
              
              return recommendations
          
          @app.route('/health', methods=['GET'])
          def health_check():
              return jsonify({"status": "healthy", "timestamp": datetime.utcnow().isoformat()})
          
          if __name__ == '__main__':
              # Start Prometheus metrics server
              start_http_server(8000)
              
              # Start Flask app
              app.run(host='0.0.0.0', port=8080)
          EOF
          
          python /app/cost_explorer.py
        ports:
        - containerPort: 8080
          name: http
        - containerPort: 8000
          name: metrics
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          capabilities:
            drop: ["ALL"]
        resources:
          requests:
            memory: "512Mi"
            cpu: "200m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        volumeMounts:
        - name: tmp
          mountPath: /tmp
        - name: app
          mountPath: /app
        env:
        - name: AWS_DEFAULT_REGION
          value: "eu-central-1"
        - name: AWS_ACCESS_KEY_ID
          valueFrom:
            secretKeyRef:
              name: aws-credentials
              key: access-key-id
        - name: AWS_SECRET_ACCESS_KEY
          valueFrom:
            secretKeyRef:
              name: aws-credentials
              key: secret-access-key
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 10
      volumes:
      - name: tmp
        emptyDir: {}
      - name: app
        emptyDir: {}
---
apiVersion: v1
kind: Secret
metadata:
  name: aws-credentials
  namespace: cost-management
type: Opaque
data:
  access-key-id: eW91ci1hd3MtYWNjZXNzLWtleS1pZA==  # base64 encoded AWS access key
  secret-access-key: eW91ci1hd3Mtc2VjcmV0LWFjY2Vzcy1rZXk=  # base64 encoded AWS secret key
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: aws-cost-explorer
  namespace: cost-management
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::ACCOUNT_ID:role/cost-explorer-role
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: aws-cost-explorer
rules:
- apiGroups: [""]
  resources: ["namespaces", "pods"]
  verbs: ["get", "list", "watch"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: aws-cost-explorer
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: aws-cost-explorer
subjects:
- kind: ServiceAccount
  name: aws-cost-explorer
  namespace: cost-management
---
apiVersion: v1
kind: Service
metadata:
  name: aws-cost-explorer
  namespace: cost-management
  labels:
    app: aws-cost-explorer
spec:
  ports:
  - port: 8080
    targetPort: 8080
    name: http
  - port: 8000
    targetPort: 8000
    name: metrics
  selector:
    app: aws-cost-explorer
