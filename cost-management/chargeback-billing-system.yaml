---
# Chargeback Mechanisms for Internal Billing
apiVersion: v1
kind: Namespace
metadata:
  name: chargeback-billing
  labels:
    pod-security.kubernetes.io/enforce: restricted
    pod-security.kubernetes.io/audit: restricted
    pod-security.kubernetes.io/warn: restricted
---
# Chargeback Billing Engine
apiVersion: apps/v1
kind: Deployment
metadata:
  name: chargeback-billing-engine
  namespace: chargeback-billing
  labels:
    app: chargeback-billing-engine
spec:
  replicas: 2
  selector:
    matchLabels:
      app: chargeback-billing-engine
  template:
    metadata:
      labels:
        app: chargeback-billing-engine
    spec:
      serviceAccountName: chargeback-billing-engine
      securityContext:
        runAsNonRoot: true
        runAsUser: 65534
        runAsGroup: 65534
        fsGroup: 65534
        seccompProfile:
          type: RuntimeDefault
      containers:
      - name: billing-engine
        image: python:3.11-alpine
        command: ["/bin/sh", "-c"]
        args:
        - |
          pip install flask kubernetes boto3 mysql-connector-python prometheus_client requests pandas
          cat > /app/chargeback_engine.py << 'EOF'
          #!/usr/bin/env python3
          import json
          import time
          import boto3
          import requests
          import pandas as pd
          import mysql.connector
          from datetime import datetime, timedelta
          from flask import Flask, request, jsonify, send_file
          from kubernetes import client, config
          from prometheus_client import Gauge, Counter, start_http_server
          import io
          import csv
          
          app = Flask(__name__)
          
          # Prometheus metrics
          billing_calculations = Counter('billing_calculations_total', 'Total billing calculations', ['tenant_id'])
          chargeback_amount = Gauge('chargeback_amount_usd', 'Chargeback amount in USD', ['tenant_id', 'department', 'cost_center'])
          invoice_generation_time = Gauge('invoice_generation_duration_seconds', 'Time to generate invoices')
          
          # Load Kubernetes config
          try:
              config.load_incluster_config()
          except:
              config.load_kube_config()
          
          k8s_client = client.CoreV1Api()
          
          # Pricing models
          PRICING_MODELS = {
              "compute": {
                  "cpu_hour": 0.05,      # $0.05 per CPU hour
                  "memory_gb_hour": 0.01, # $0.01 per GB memory hour
                  "storage_gb_month": 0.10 # $0.10 per GB storage per month
              },
              "network": {
                  "data_transfer_gb": 0.09, # $0.09 per GB data transfer
                  "load_balancer_hour": 0.025 # $0.025 per load balancer hour
              },
              "services": {
                  "database_hour": 0.15,    # $0.15 per database hour
                  "cache_gb_hour": 0.02,    # $0.02 per GB cache hour
                  "monitoring_hour": 0.01   # $0.01 per monitoring hour
              },
              "support": {
                  "basic": 0.05,     # 5% markup for basic support
                  "premium": 0.15,   # 15% markup for premium support
                  "enterprise": 0.25 # 25% markup for enterprise support
              }
          }
          
          @app.route('/api/chargeback/tenant/<tenant_id>', methods=['GET'])
          def get_tenant_chargeback(tenant_id):
              """Get detailed chargeback information for a tenant"""
              try:
                  period = request.args.get('period', 'monthly')
                  start_date = request.args.get('start_date')
                  end_date = request.args.get('end_date')
                  
                  if not start_date:
                      if period == 'monthly':
                          start_date = datetime.now().replace(day=1).strftime('%Y-%m-%d')
                      else:
                          start_date = (datetime.now() - timedelta(days=7)).strftime('%Y-%m-%d')
                  
                  if not end_date:
                      end_date = datetime.now().strftime('%Y-%m-%d')
                  
                  billing_calculations.labels(tenant_id=tenant_id).inc()
                  
                  # Get tenant metadata
                  tenant_metadata = get_tenant_metadata(tenant_id)
                  
                  # Calculate detailed costs
                  chargeback_data = calculate_detailed_chargeback(tenant_id, start_date, end_date, tenant_metadata)
                  
                  # Update Prometheus metrics
                  department = tenant_metadata.get('department', 'unknown')
                  cost_center = tenant_metadata.get('cost_center', 'unknown')
                  chargeback_amount.labels(
                      tenant_id=tenant_id,
                      department=department,
                      cost_center=cost_center
                  ).set(chargeback_data['total_chargeback'])
                  
                  return jsonify(chargeback_data)
              
              except Exception as e:
                  return jsonify({"error": str(e)}), 500
          
          @app.route('/api/chargeback/department/<department>', methods=['GET'])
          def get_department_chargeback(department):
              """Get chargeback summary for a department"""
              try:
                  period = request.args.get('period', 'monthly')
                  start_date = request.args.get('start_date')
                  end_date = request.args.get('end_date')
                  
                  # Get all tenants for the department
                  tenants = get_tenants_by_department(department)
                  
                  department_chargeback = {
                      "department": department,
                      "period": period,
                      "start_date": start_date,
                      "end_date": end_date,
                      "total_chargeback": 0,
                      "tenant_breakdown": [],
                      "cost_categories": {
                          "compute": 0,
                          "network": 0,
                          "storage": 0,
                          "services": 0,
                          "support": 0
                      }
                  }
                  
                  for tenant_id in tenants:
                      tenant_metadata = get_tenant_metadata(tenant_id)
                      tenant_chargeback = calculate_detailed_chargeback(tenant_id, start_date, end_date, tenant_metadata)
                      
                      department_chargeback["tenant_breakdown"].append({
                          "tenant_id": tenant_id,
                          "tenant_name": tenant_metadata.get('name', tenant_id),
                          "cost_center": tenant_metadata.get('cost_center', 'unknown'),
                          "total_cost": tenant_chargeback['total_chargeback'],
                          "primary_contact": tenant_metadata.get('primary_contact', 'unknown')
                      })
                      
                      department_chargeback["total_chargeback"] += tenant_chargeback['total_chargeback']
                      
                      # Aggregate cost categories
                      for category, amount in tenant_chargeback['cost_breakdown'].items():
                          if category in department_chargeback["cost_categories"]:
                              department_chargeback["cost_categories"][category] += amount
                  
                  return jsonify(department_chargeback)
              
              except Exception as e:
                  return jsonify({"error": str(e)}), 500
          
          @app.route('/api/chargeback/invoice/<tenant_id>', methods=['POST'])
          def generate_invoice(tenant_id):
              """Generate invoice for a tenant"""
              try:
                  start_time = time.time()
                  
                  data = request.get_json()
                  period = data.get('period', 'monthly')
                  start_date = data.get('start_date')
                  end_date = data.get('end_date')
                  invoice_format = data.get('format', 'json')  # json, csv, pdf
                  
                  # Get tenant metadata
                  tenant_metadata = get_tenant_metadata(tenant_id)
                  
                  # Calculate chargeback
                  chargeback_data = calculate_detailed_chargeback(tenant_id, start_date, end_date, tenant_metadata)
                  
                  # Generate invoice
                  invoice = generate_invoice_document(tenant_id, chargeback_data, tenant_metadata, invoice_format)
                  
                  # Save invoice to database
                  invoice_id = save_invoice_to_database(tenant_id, invoice, chargeback_data)
                  
                  invoice_generation_time.set(time.time() - start_time)
                  
                  if invoice_format == 'csv':
                      return send_file(
                          io.BytesIO(invoice.encode()),
                          mimetype='text/csv',
                          as_attachment=True,
                          download_name=f'invoice_{tenant_id}_{start_date}_{end_date}.csv'
                      )
                  else:
                      return jsonify({
                          "invoice_id": invoice_id,
                          "tenant_id": tenant_id,
                          "period": period,
                          "total_amount": chargeback_data['total_chargeback'],
                          "currency": "USD",
                          "generated_at": datetime.utcnow().isoformat(),
                          "invoice_data": invoice if invoice_format == 'json' else "Generated successfully"
                      })
              
              except Exception as e:
                  return jsonify({"error": str(e)}), 500
          
          @app.route('/api/chargeback/reports/cost-center', methods=['GET'])
          def cost_center_report():
              """Generate cost center chargeback report"""
              try:
                  period = request.args.get('period', 'monthly')
                  start_date = request.args.get('start_date')
                  end_date = request.args.get('end_date')
                  
                  # Get all tenants grouped by cost center
                  cost_centers = get_all_cost_centers()
                  
                  report = {
                      "report_type": "cost_center_chargeback",
                      "period": period,
                      "start_date": start_date,
                      "end_date": end_date,
                      "generated_at": datetime.utcnow().isoformat(),
                      "cost_centers": []
                  }
                  
                  total_chargeback = 0
                  
                  for cost_center in cost_centers:
                      tenants = get_tenants_by_cost_center(cost_center)
                      cost_center_total = 0
                      cost_center_tenants = []
                      
                      for tenant_id in tenants:
                          tenant_metadata = get_tenant_metadata(tenant_id)
                          tenant_chargeback = calculate_detailed_chargeback(tenant_id, start_date, end_date, tenant_metadata)
                          
                          cost_center_tenants.append({
                              "tenant_id": tenant_id,
                              "tenant_name": tenant_metadata.get('name', tenant_id),
                              "department": tenant_metadata.get('department', 'unknown'),
                              "total_cost": tenant_chargeback['total_chargeback']
                          })
                          
                          cost_center_total += tenant_chargeback['total_chargeback']
                      
                      report["cost_centers"].append({
                          "cost_center": cost_center,
                          "total_chargeback": cost_center_total,
                          "tenant_count": len(tenants),
                          "tenants": cost_center_tenants
                      })
                      
                      total_chargeback += cost_center_total
                  
                  report["total_chargeback"] = total_chargeback
                  
                  return jsonify(report)
              
              except Exception as e:
                  return jsonify({"error": str(e)}), 500
          
          def calculate_detailed_chargeback(tenant_id, start_date, end_date, tenant_metadata):
              """Calculate detailed chargeback for a tenant"""
              
              # Get resource usage data
              resource_usage = get_tenant_resource_usage(tenant_id, start_date, end_date)
              
              # Get AWS costs
              aws_costs = get_tenant_aws_costs(tenant_id, start_date, end_date)
              
              # Calculate compute costs
              compute_costs = calculate_compute_costs(resource_usage)
              
              # Calculate network costs
              network_costs = calculate_network_costs(resource_usage)
              
              # Calculate storage costs
              storage_costs = calculate_storage_costs(resource_usage)
              
              # Calculate service costs
              service_costs = calculate_service_costs(resource_usage)
              
              # Calculate support markup
              support_level = tenant_metadata.get('support_level', 'basic')
              base_cost = compute_costs + network_costs + storage_costs + service_costs
              support_costs = base_cost * PRICING_MODELS['support'][support_level]
              
              # Apply department-specific discounts/markups
              department_modifier = get_department_modifier(tenant_metadata.get('department'))
              
              total_chargeback = (base_cost + support_costs) * department_modifier
              
              return {
                  "tenant_id": tenant_id,
                  "tenant_name": tenant_metadata.get('name', tenant_id),
                  "department": tenant_metadata.get('department', 'unknown'),
                  "cost_center": tenant_metadata.get('cost_center', 'unknown'),
                  "support_level": support_level,
                  "period": f"{start_date} to {end_date}",
                  "cost_breakdown": {
                      "compute": compute_costs,
                      "network": network_costs,
                      "storage": storage_costs,
                      "services": service_costs,
                      "support": support_costs
                  },
                  "aws_costs": aws_costs,
                  "department_modifier": department_modifier,
                  "total_chargeback": total_chargeback,
                  "currency": "USD",
                  "calculated_at": datetime.utcnow().isoformat()
              }
          
          def calculate_compute_costs(resource_usage):
              """Calculate compute costs based on resource usage"""
              cpu_hours = resource_usage.get('cpu_hours', 0)
              memory_gb_hours = resource_usage.get('memory_gb_hours', 0)
              
              cpu_cost = cpu_hours * PRICING_MODELS['compute']['cpu_hour']
              memory_cost = memory_gb_hours * PRICING_MODELS['compute']['memory_gb_hour']
              
              return cpu_cost + memory_cost
          
          def calculate_network_costs(resource_usage):
              """Calculate network costs"""
              data_transfer_gb = resource_usage.get('data_transfer_gb', 0)
              load_balancer_hours = resource_usage.get('load_balancer_hours', 0)
              
              transfer_cost = data_transfer_gb * PRICING_MODELS['network']['data_transfer_gb']
              lb_cost = load_balancer_hours * PRICING_MODELS['network']['load_balancer_hour']
              
              return transfer_cost + lb_cost
          
          def calculate_storage_costs(resource_usage):
              """Calculate storage costs"""
              storage_gb_months = resource_usage.get('storage_gb_months', 0)
              return storage_gb_months * PRICING_MODELS['compute']['storage_gb_month']
          
          def calculate_service_costs(resource_usage):
              """Calculate service costs (database, cache, monitoring)"""
              database_hours = resource_usage.get('database_hours', 0)
              cache_gb_hours = resource_usage.get('cache_gb_hours', 0)
              monitoring_hours = resource_usage.get('monitoring_hours', 0)
              
              db_cost = database_hours * PRICING_MODELS['services']['database_hour']
              cache_cost = cache_gb_hours * PRICING_MODELS['services']['cache_gb_hour']
              monitoring_cost = monitoring_hours * PRICING_MODELS['services']['monitoring_hour']
              
              return db_cost + cache_cost + monitoring_cost
          
          def get_tenant_resource_usage(tenant_id, start_date, end_date):
              """Get resource usage metrics for a tenant"""
              # This would integrate with Prometheus metrics
              # For now, return sample data
              return {
                  "cpu_hours": 720,        # 30 days * 24 hours * 1 CPU
                  "memory_gb_hours": 1440, # 30 days * 24 hours * 2 GB
                  "storage_gb_months": 100, # 100 GB for 1 month
                  "data_transfer_gb": 50,   # 50 GB data transfer
                  "load_balancer_hours": 720, # 30 days * 24 hours
                  "database_hours": 720,    # 30 days * 24 hours
                  "cache_gb_hours": 240,    # 30 days * 24 hours * 0.33 GB
                  "monitoring_hours": 720   # 30 days * 24 hours
              }
          
          def get_tenant_aws_costs(tenant_id, start_date, end_date):
              """Get AWS costs for tenant from cost management API"""
              try:
                  response = requests.get(
                      f"http://aws-cost-explorer.cost-management.svc.cluster.local:8080/api/costs/tenant/{tenant_id}",
                      params={"start_date": start_date, "end_date": end_date},
                      timeout=30
                  )
                  
                  if response.status_code == 200:
                      data = response.json()
                      return data.get('services', {})
                  else:
                      return {}
              except:
                  return {}
          
          def get_tenant_metadata(tenant_id):
              """Get tenant metadata from Kubernetes annotations"""
              try:
                  namespace = k8s_client.read_namespace(f'tenant-{tenant_id}')
                  annotations = namespace.metadata.annotations or {}
                  
                  return {
                      "name": annotations.get('tenant-name', tenant_id),
                      "department": annotations.get('department', 'unknown'),
                      "cost_center": annotations.get('cost-center', 'unknown'),
                      "support_level": annotations.get('support-level', 'basic'),
                      "primary_contact": annotations.get('primary-contact', 'unknown'),
                      "billing_contact": annotations.get('billing-contact', 'unknown')
                  }
              except:
                  return {
                      "name": tenant_id,
                      "department": "unknown",
                      "cost_center": "unknown",
                      "support_level": "basic",
                      "primary_contact": "unknown",
                      "billing_contact": "unknown"
                  }
          
          def get_department_modifier(department):
              """Get department-specific cost modifier"""
              modifiers = {
                  "engineering": 1.0,    # No modifier
                  "sales": 0.9,          # 10% discount
                  "marketing": 0.95,     # 5% discount
                  "finance": 1.1,        # 10% markup for compliance
                  "hr": 0.85,           # 15% discount
                  "unknown": 1.0
              }
              return modifiers.get(department.lower(), 1.0)
          
          def generate_invoice_document(tenant_id, chargeback_data, tenant_metadata, format_type):
              """Generate invoice document in specified format"""
              
              if format_type == 'csv':
                  return generate_csv_invoice(tenant_id, chargeback_data, tenant_metadata)
              else:
                  return generate_json_invoice(tenant_id, chargeback_data, tenant_metadata)
          
          def generate_csv_invoice(tenant_id, chargeback_data, tenant_metadata):
              """Generate CSV invoice"""
              output = io.StringIO()
              writer = csv.writer(output)
              
              # Header
              writer.writerow(['Invoice for Tenant', tenant_id])
              writer.writerow(['Tenant Name', chargeback_data['tenant_name']])
              writer.writerow(['Department', chargeback_data['department']])
              writer.writerow(['Cost Center', chargeback_data['cost_center']])
              writer.writerow(['Period', chargeback_data['period']])
              writer.writerow(['Generated At', chargeback_data['calculated_at']])
              writer.writerow([])
              
              # Cost breakdown
              writer.writerow(['Cost Category', 'Amount (USD)'])
              for category, amount in chargeback_data['cost_breakdown'].items():
                  writer.writerow([category.title(), f"${amount:.2f}"])
              
              writer.writerow([])
              writer.writerow(['Total Chargeback', f"${chargeback_data['total_chargeback']:.2f}"])
              
              return output.getvalue()
          
          def generate_json_invoice(tenant_id, chargeback_data, tenant_metadata):
              """Generate JSON invoice"""
              return {
                  "invoice_header": {
                      "invoice_id": f"INV-{tenant_id}-{datetime.now().strftime('%Y%m%d')}",
                      "tenant_id": tenant_id,
                      "tenant_name": chargeback_data['tenant_name'],
                      "department": chargeback_data['department'],
                      "cost_center": chargeback_data['cost_center'],
                      "billing_contact": tenant_metadata.get('billing_contact', 'unknown'),
                      "period": chargeback_data['period'],
                      "generated_at": chargeback_data['calculated_at']
                  },
                  "line_items": [
                      {
                          "description": category.title() + " Services",
                          "amount": amount,
                          "currency": "USD"
                      }
                      for category, amount in chargeback_data['cost_breakdown'].items()
                  ],
                  "summary": {
                      "subtotal": sum(chargeback_data['cost_breakdown'].values()),
                      "department_modifier": chargeback_data['department_modifier'],
                      "total_amount": chargeback_data['total_chargeback'],
                      "currency": "USD"
                  }
              }
          
          def save_invoice_to_database(tenant_id, invoice, chargeback_data):
              """Save invoice to database"""
              try:
                  conn = mysql.connector.connect(
                      host='production-aurora-serverless.cluster-cpmagwki2kv8.eu-central-1.rds.amazonaws.com',
                      port=3306,
                      user='admin',
                      password='&BZzY_<AK(=a*UhZ',
                      database='architrave'
                  )
                  
                  cursor = conn.cursor()
                  
                  # Create table if not exists
                  cursor.execute("""
                      CREATE TABLE IF NOT EXISTS tenant_invoices (
                          invoice_id VARCHAR(100) PRIMARY KEY,
                          tenant_id VARCHAR(50),
                          invoice_data JSON,
                          total_amount DECIMAL(10,2),
                          currency VARCHAR(3),
                          period_start DATE,
                          period_end DATE,
                          generated_at TIMESTAMP,
                          status VARCHAR(20) DEFAULT 'generated'
                      )
                  """)
                  
                  invoice_id = f"INV-{tenant_id}-{datetime.now().strftime('%Y%m%d-%H%M%S')}"
                  
                  cursor.execute("""
                      INSERT INTO tenant_invoices 
                      (invoice_id, tenant_id, invoice_data, total_amount, currency, generated_at)
                      VALUES (%s, %s, %s, %s, %s, %s)
                  """, (
                      invoice_id,
                      tenant_id,
                      json.dumps(invoice),
                      chargeback_data['total_chargeback'],
                      'USD',
                      datetime.utcnow()
                  ))
                  
                  conn.commit()
                  conn.close()
                  
                  return invoice_id
              except Exception as e:
                  print(f"Error saving invoice: {e}")
                  return f"INV-{tenant_id}-{datetime.now().strftime('%Y%m%d')}"
          
          def get_tenants_by_department(department):
              """Get all tenants for a department"""
              try:
                  namespaces = k8s_client.list_namespace()
                  tenants = []
                  
                  for ns in namespaces.items:
                      if ns.metadata.name.startswith('tenant-'):
                          annotations = ns.metadata.annotations or {}
                          if annotations.get('department', '').lower() == department.lower():
                              tenants.append(ns.metadata.name.replace('tenant-', ''))
                  
                  return tenants
              except:
                  return []
          
          def get_tenants_by_cost_center(cost_center):
              """Get all tenants for a cost center"""
              try:
                  namespaces = k8s_client.list_namespace()
                  tenants = []
                  
                  for ns in namespaces.items:
                      if ns.metadata.name.startswith('tenant-'):
                          annotations = ns.metadata.annotations or {}
                          if annotations.get('cost-center', '') == cost_center:
                              tenants.append(ns.metadata.name.replace('tenant-', ''))
                  
                  return tenants
              except:
                  return []
          
          def get_all_cost_centers():
              """Get all unique cost centers"""
              try:
                  namespaces = k8s_client.list_namespace()
                  cost_centers = set()
                  
                  for ns in namespaces.items:
                      if ns.metadata.name.startswith('tenant-'):
                          annotations = ns.metadata.annotations or {}
                          cost_center = annotations.get('cost-center')
                          if cost_center:
                              cost_centers.add(cost_center)
                  
                  return list(cost_centers)
              except:
                  return []
          
          @app.route('/health', methods=['GET'])
          def health_check():
              return jsonify({"status": "healthy", "timestamp": datetime.utcnow().isoformat()})
          
          if __name__ == '__main__':
              # Start Prometheus metrics server
              start_http_server(8000)
              
              # Start Flask app
              app.run(host='0.0.0.0', port=8080)
          EOF
          
          python /app/chargeback_engine.py
        ports:
        - containerPort: 8080
          name: http
        - containerPort: 8000
          name: metrics
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          capabilities:
            drop: ["ALL"]
        resources:
          requests:
            memory: "512Mi"
            cpu: "200m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        volumeMounts:
        - name: tmp
          mountPath: /tmp
        - name: app
          mountPath: /app
        env:
        - name: AWS_DEFAULT_REGION
          value: "eu-central-1"
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 10
      volumes:
      - name: tmp
        emptyDir: {}
      - name: app
        emptyDir: {}
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: chargeback-billing-engine
  namespace: chargeback-billing
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: chargeback-billing-engine
rules:
- apiGroups: [""]
  resources: ["namespaces"]
  verbs: ["get", "list", "watch"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: chargeback-billing-engine
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: chargeback-billing-engine
subjects:
- kind: ServiceAccount
  name: chargeback-billing-engine
  namespace: chargeback-billing
---
apiVersion: v1
kind: Service
metadata:
  name: chargeback-billing-engine
  namespace: chargeback-billing
  labels:
    app: chargeback-billing-engine
spec:
  ports:
  - port: 8080
    targetPort: 8080
    name: http
  - port: 8000
    targetPort: 8000
    name: metrics
  selector:
    app: chargeback-billing-engine
