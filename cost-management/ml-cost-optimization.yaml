---
# Cost Optimization Recommendations with ML
apiVersion: v1
kind: Namespace
metadata:
  name: ml-cost-optimization
  labels:
    pod-security.kubernetes.io/enforce: restricted
    pod-security.kubernetes.io/audit: restricted
    pod-security.kubernetes.io/warn: restricted
---
# ML Cost Optimization Engine
apiVersion: apps/v1
kind: Deployment
metadata:
  name: ml-cost-optimizer
  namespace: ml-cost-optimization
  labels:
    app: ml-cost-optimizer
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ml-cost-optimizer
  template:
    metadata:
      labels:
        app: ml-cost-optimizer
    spec:
      serviceAccountName: ml-cost-optimizer
      securityContext:
        runAsNonRoot: true
        runAsUser: 65534
        runAsGroup: 65534
        fsGroup: 65534
        seccompProfile:
          type: RuntimeDefault
      containers:
      - name: optimizer
        image: python:3.11-alpine
        command: ["/bin/sh", "-c"]
        args:
        - |
          pip install flask kubernetes boto3 mysql-connector-python prometheus_client requests pandas numpy scikit-learn
          cat > /app/ml_optimizer.py << 'EOF'
          #!/usr/bin/env python3
          import json
          import time
          import boto3
          import requests
          import pandas as pd
          import numpy as np
          import mysql.connector
          from datetime import datetime, timedelta
          from flask import Flask, request, jsonify
          from kubernetes import client, config
          from prometheus_client import Gauge, Counter, start_http_server
          from sklearn.ensemble import RandomForestRegressor
          from sklearn.preprocessing import StandardScaler
          from sklearn.model_selection import train_test_split
          import pickle
          import warnings
          warnings.filterwarnings('ignore')
          
          app = Flask(__name__)
          
          # Prometheus metrics
          optimization_recommendations = Counter('optimization_recommendations_total', 'Total optimization recommendations', ['tenant_id', 'type'])
          potential_savings = Gauge('potential_savings_usd', 'Potential savings in USD', ['tenant_id', 'recommendation_type'])
          ml_model_accuracy = Gauge('ml_model_accuracy', 'ML model accuracy score', ['model_type'])
          
          # Load Kubernetes config
          try:
              config.load_incluster_config()
          except:
              config.load_kube_config()
          
          k8s_client = client.CoreV1Api()
          apps_client = client.AppsV1Api()
          
          # ML Models (will be trained and cached)
          cost_prediction_model = None
          resource_optimization_model = None
          scaler = StandardScaler()
          
          @app.route('/api/ml-optimization/tenant/<tenant_id>', methods=['GET'])
          def get_tenant_optimization_recommendations(tenant_id):
              """Get ML-powered cost optimization recommendations for a tenant"""
              try:
                  # Get historical data
                  historical_data = get_tenant_historical_data(tenant_id)
                  
                  if len(historical_data) < 7:  # Need at least 7 days of data
                      return jsonify({
                          "error": "Insufficient historical data for ML analysis",
                          "minimum_days_required": 7,
                          "current_days": len(historical_data)
                      }), 400
                  
                  # Generate ML-powered recommendations
                  recommendations = generate_ml_recommendations(tenant_id, historical_data)
                  
                  # Update Prometheus metrics
                  for rec in recommendations:
                      optimization_recommendations.labels(
                          tenant_id=tenant_id,
                          type=rec['type']
                      ).inc()
                      
                      potential_savings.labels(
                          tenant_id=tenant_id,
                          recommendation_type=rec['type']
                      ).set(rec['potential_savings'])
                  
                  return jsonify({
                      "tenant_id": tenant_id,
                      "recommendations": recommendations,
                      "total_potential_savings": sum(r['potential_savings'] for r in recommendations),
                      "confidence_score": calculate_confidence_score(recommendations),
                      "generated_at": datetime.utcnow().isoformat(),
                      "data_points_analyzed": len(historical_data)
                  })
              
              except Exception as e:
                  return jsonify({"error": str(e)}), 500
          
          @app.route('/api/ml-optimization/forecast/<tenant_id>', methods=['GET'])
          def forecast_tenant_costs(tenant_id):
              """Forecast future costs using ML"""
              try:
                  days_ahead = int(request.args.get('days', 30))
                  
                  # Get historical data
                  historical_data = get_tenant_historical_data(tenant_id)
                  
                  if len(historical_data) < 14:
                      return jsonify({
                          "error": "Insufficient data for forecasting",
                          "minimum_days_required": 14
                      }), 400
                  
                  # Train or load forecasting model
                  forecast_model = train_cost_forecasting_model(historical_data)
                  
                  # Generate forecast
                  forecast = generate_cost_forecast(forecast_model, historical_data, days_ahead)
                  
                  # Identify cost anomalies and optimization opportunities
                  anomalies = detect_cost_anomalies(forecast)
                  
                  return jsonify({
                      "tenant_id": tenant_id,
                      "forecast_period_days": days_ahead,
                      "forecast": forecast,
                      "anomalies": anomalies,
                      "model_accuracy": forecast_model.get('accuracy', 0),
                      "generated_at": datetime.utcnow().isoformat()
                  })
              
              except Exception as e:
                  return jsonify({"error": str(e)}), 500
          
          @app.route('/api/ml-optimization/rightsizing/<tenant_id>', methods=['GET'])
          def get_rightsizing_recommendations(tenant_id):
              """Get ML-powered rightsizing recommendations"""
              try:
                  # Get resource usage data
                  resource_data = get_tenant_resource_usage_data(tenant_id)
                  
                  # Generate rightsizing recommendations using ML
                  rightsizing_recs = generate_rightsizing_recommendations(tenant_id, resource_data)
                  
                  return jsonify({
                      "tenant_id": tenant_id,
                      "rightsizing_recommendations": rightsizing_recs,
                      "total_potential_savings": sum(r['monthly_savings'] for r in rightsizing_recs),
                      "generated_at": datetime.utcnow().isoformat()
                  })
              
              except Exception as e:
                  return jsonify({"error": str(e)}), 500
          
          @app.route('/api/ml-optimization/train-models', methods=['POST'])
          def train_ml_models():
              """Train ML models with latest data"""
              try:
                  # Get training data from all tenants
                  training_data = collect_training_data()
                  
                  if len(training_data) < 100:
                      return jsonify({
                          "error": "Insufficient training data",
                          "minimum_samples_required": 100,
                          "current_samples": len(training_data)
                      }), 400
                  
                  # Train cost prediction model
                  cost_model_results = train_cost_prediction_model(training_data)
                  
                  # Train resource optimization model
                  resource_model_results = train_resource_optimization_model(training_data)
                  
                  # Update model accuracy metrics
                  ml_model_accuracy.labels(model_type='cost_prediction').set(cost_model_results['accuracy'])
                  ml_model_accuracy.labels(model_type='resource_optimization').set(resource_model_results['accuracy'])
                  
                  return jsonify({
                      "status": "success",
                      "models_trained": ["cost_prediction", "resource_optimization"],
                      "cost_model_accuracy": cost_model_results['accuracy'],
                      "resource_model_accuracy": resource_model_results['accuracy'],
                      "training_samples": len(training_data),
                      "trained_at": datetime.utcnow().isoformat()
                  })
              
              except Exception as e:
                  return jsonify({"error": str(e)}), 500
          
          def generate_ml_recommendations(tenant_id, historical_data):
              """Generate ML-powered optimization recommendations"""
              recommendations = []
              
              # Convert to DataFrame for analysis
              df = pd.DataFrame(historical_data)
              
              # 1. Resource Utilization Analysis
              resource_recs = analyze_resource_utilization(tenant_id, df)
              recommendations.extend(resource_recs)
              
              # 2. Cost Pattern Analysis
              pattern_recs = analyze_cost_patterns(tenant_id, df)
              recommendations.extend(pattern_recs)
              
              # 3. Scaling Optimization
              scaling_recs = analyze_scaling_patterns(tenant_id, df)
              recommendations.extend(scaling_recs)
              
              # 4. Service Optimization
              service_recs = analyze_service_usage(tenant_id, df)
              recommendations.extend(service_recs)
              
              # 5. Scheduling Optimization
              schedule_recs = analyze_usage_schedules(tenant_id, df)
              recommendations.extend(schedule_recs)
              
              return sorted(recommendations, key=lambda x: x['potential_savings'], reverse=True)
          
          def analyze_resource_utilization(tenant_id, df):
              """Analyze resource utilization patterns"""
              recommendations = []
              
              # CPU utilization analysis
              if 'cpu_utilization' in df.columns:
                  avg_cpu = df['cpu_utilization'].mean()
                  max_cpu = df['cpu_utilization'].max()
                  
                  if avg_cpu < 30 and max_cpu < 60:  # Consistently low CPU usage
                      recommendations.append({
                          "type": "cpu_rightsizing",
                          "title": "Reduce CPU allocation",
                          "description": f"Average CPU utilization is {avg_cpu:.1f}%. Consider reducing CPU requests.",
                          "potential_savings": calculate_cpu_savings(tenant_id, 0.5),  # 50% reduction
                          "confidence": 0.85,
                          "implementation_effort": "low",
                          "risk_level": "low"
                      })
              
              # Memory utilization analysis
              if 'memory_utilization' in df.columns:
                  avg_memory = df['memory_utilization'].mean()
                  max_memory = df['memory_utilization'].max()
                  
                  if avg_memory < 40 and max_memory < 70:
                      recommendations.append({
                          "type": "memory_rightsizing",
                          "title": "Reduce memory allocation",
                          "description": f"Average memory utilization is {avg_memory:.1f}%. Consider reducing memory requests.",
                          "potential_savings": calculate_memory_savings(tenant_id, 0.3),  # 30% reduction
                          "confidence": 0.80,
                          "implementation_effort": "low",
                          "risk_level": "low"
                      })
              
              return recommendations
          
          def analyze_cost_patterns(tenant_id, df):
              """Analyze cost patterns and trends"""
              recommendations = []
              
              if 'daily_cost' in df.columns and len(df) >= 14:
                  # Detect cost spikes
                  cost_std = df['daily_cost'].std()
                  cost_mean = df['daily_cost'].mean()
                  
                  spikes = df[df['daily_cost'] > cost_mean + 2 * cost_std]
                  
                  if len(spikes) > 0:
                      recommendations.append({
                          "type": "cost_spike_investigation",
                          "title": "Investigate cost spikes",
                          "description": f"Detected {len(spikes)} cost spikes. Average spike cost: ${spikes['daily_cost'].mean():.2f}",
                          "potential_savings": spikes['daily_cost'].sum() * 0.5,  # Assume 50% can be optimized
                          "confidence": 0.70,
                          "implementation_effort": "medium",
                          "risk_level": "low"
                      })
                  
                  # Detect weekend/holiday usage
                  if 'day_of_week' in df.columns:
                      weekend_costs = df[df['day_of_week'].isin([5, 6])]['daily_cost'].mean()
                      weekday_costs = df[~df['day_of_week'].isin([5, 6])]['daily_cost'].mean()
                      
                      if weekend_costs > weekday_costs * 0.8:  # High weekend usage
                          recommendations.append({
                              "type": "schedule_optimization",
                              "title": "Implement weekend scaling",
                              "description": "High weekend costs detected. Consider scaling down non-critical services.",
                              "potential_savings": (weekend_costs - weekday_costs * 0.3) * 8,  # 8 weekend days per month
                              "confidence": 0.75,
                              "implementation_effort": "medium",
                              "risk_level": "low"
                          })
              
              return recommendations
          
          def analyze_scaling_patterns(tenant_id, df):
              """Analyze auto-scaling patterns"""
              recommendations = []
              
              if 'replica_count' in df.columns:
                  # Check for over-provisioning
                  avg_replicas = df['replica_count'].mean()
                  min_replicas = df['replica_count'].min()
                  max_replicas = df['replica_count'].max()
                  
                  if min_replicas > 1 and avg_replicas < max_replicas * 0.6:
                      recommendations.append({
                          "type": "scaling_optimization",
                          "title": "Optimize auto-scaling thresholds",
                          "description": f"Average replicas ({avg_replicas:.1f}) much lower than max ({max_replicas}). Adjust scaling policies.",
                          "potential_savings": calculate_scaling_savings(tenant_id, avg_replicas, max_replicas),
                          "confidence": 0.80,
                          "implementation_effort": "low",
                          "risk_level": "medium"
                      })
              
              return recommendations
          
          def analyze_service_usage(tenant_id, df):
              """Analyze service usage patterns"""
              recommendations = []
              
              # Database usage analysis
              if 'database_connections' in df.columns:
                  avg_connections = df['database_connections'].mean()
                  max_connections = df['database_connections'].max()
                  
                  if avg_connections < max_connections * 0.3:
                      recommendations.append({
                          "type": "database_rightsizing",
                          "title": "Consider smaller database instance",
                          "description": f"Database utilization is low (avg: {avg_connections:.0f}, max: {max_connections:.0f} connections).",
                          "potential_savings": 150.0,  # Estimated monthly savings
                          "confidence": 0.70,
                          "implementation_effort": "high",
                          "risk_level": "medium"
                      })
              
              # Cache usage analysis
              if 'cache_hit_rate' in df.columns:
                  avg_hit_rate = df['cache_hit_rate'].mean()
                  
                  if avg_hit_rate < 0.6:  # Low cache hit rate
                      recommendations.append({
                          "type": "cache_optimization",
                          "title": "Optimize cache configuration",
                          "description": f"Cache hit rate is low ({avg_hit_rate:.1%}). Review cache keys and TTL settings.",
                          "potential_savings": 75.0,  # Estimated savings from better cache usage
                          "confidence": 0.65,
                          "implementation_effort": "medium",
                          "risk_level": "low"
                      })
              
              return recommendations
          
          def analyze_usage_schedules(tenant_id, df):
              """Analyze usage schedules for optimization opportunities"""
              recommendations = []
              
              if 'hour_of_day' in df.columns and 'cpu_utilization' in df.columns:
                  # Analyze hourly patterns
                  hourly_usage = df.groupby('hour_of_day')['cpu_utilization'].mean()
                  
                  # Find low usage periods
                  low_usage_hours = hourly_usage[hourly_usage < hourly_usage.mean() * 0.5]
                  
                  if len(low_usage_hours) >= 6:  # At least 6 hours of low usage
                      recommendations.append({
                          "type": "scheduled_scaling",
                          "title": "Implement scheduled scaling",
                          "description": f"Low usage detected during {len(low_usage_hours)} hours. Implement scheduled scaling.",
                          "potential_savings": calculate_scheduled_scaling_savings(tenant_id, len(low_usage_hours)),
                          "confidence": 0.75,
                          "implementation_effort": "medium",
                          "risk_level": "low"
                      })
              
              return recommendations
          
          def train_cost_forecasting_model(historical_data):
              """Train ML model for cost forecasting"""
              global cost_prediction_model, scaler
              
              df = pd.DataFrame(historical_data)
              
              # Feature engineering
              features = ['cpu_utilization', 'memory_utilization', 'replica_count', 'day_of_week', 'hour_of_day']
              available_features = [f for f in features if f in df.columns]
              
              if len(available_features) < 3:
                  return {"accuracy": 0, "error": "Insufficient features for training"}
              
              X = df[available_features].fillna(0)
              y = df['daily_cost'].fillna(0)
              
              # Split data
              X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
              
              # Scale features
              X_train_scaled = scaler.fit_transform(X_train)
              X_test_scaled = scaler.transform(X_test)
              
              # Train model
              model = RandomForestRegressor(n_estimators=100, random_state=42)
              model.fit(X_train_scaled, y_train)
              
              # Calculate accuracy
              accuracy = model.score(X_test_scaled, y_test)
              
              cost_prediction_model = {
                  "model": model,
                  "features": available_features,
                  "accuracy": accuracy
              }
              
              return {"accuracy": accuracy}
          
          def generate_rightsizing_recommendations(tenant_id, resource_data):
              """Generate rightsizing recommendations using ML"""
              recommendations = []
              
              # Get current deployments
              try:
                  namespace = f"tenant-{tenant_id}"
                  deployments = apps_client.list_namespaced_deployment(namespace=namespace)
                  
                  for deployment in deployments.items:
                      for container in deployment.spec.template.spec.containers:
                          if container.resources and container.resources.requests:
                              current_cpu = container.resources.requests.get('cpu', '0')
                              current_memory = container.resources.requests.get('memory', '0')
                              
                              # Analyze usage patterns
                              usage_analysis = analyze_container_usage(tenant_id, container.name, resource_data)
                              
                              if usage_analysis['cpu_recommendation'] != current_cpu:
                                  recommendations.append({
                                      "resource_type": "cpu",
                                      "deployment": deployment.metadata.name,
                                      "container": container.name,
                                      "current_request": current_cpu,
                                      "recommended_request": usage_analysis['cpu_recommendation'],
                                      "monthly_savings": usage_analysis['cpu_savings'],
                                      "confidence": usage_analysis['confidence']
                                  })
                              
                              if usage_analysis['memory_recommendation'] != current_memory:
                                  recommendations.append({
                                      "resource_type": "memory",
                                      "deployment": deployment.metadata.name,
                                      "container": container.name,
                                      "current_request": current_memory,
                                      "recommended_request": usage_analysis['memory_recommendation'],
                                      "monthly_savings": usage_analysis['memory_savings'],
                                      "confidence": usage_analysis['confidence']
                                  })
              
              except Exception as e:
                  print(f"Error generating rightsizing recommendations: {e}")
              
              return recommendations
          
          def analyze_container_usage(tenant_id, container_name, resource_data):
              """Analyze container resource usage"""
              # Simplified analysis - in production, use actual metrics
              return {
                  "cpu_recommendation": "500m",  # Recommended CPU
                  "memory_recommendation": "1Gi",  # Recommended memory
                  "cpu_savings": 25.0,  # Monthly savings
                  "memory_savings": 15.0,  # Monthly savings
                  "confidence": 0.80
              }
          
          def get_tenant_historical_data(tenant_id):
              """Get historical data for ML analysis"""
              # This would integrate with Prometheus/monitoring systems
              # For now, return sample data
              data = []
              for i in range(30):  # 30 days of data
                  date = datetime.now() - timedelta(days=i)
                  data.append({
                      "date": date.strftime('%Y-%m-%d'),
                      "daily_cost": 15.0 + np.random.normal(0, 2),
                      "cpu_utilization": 45 + np.random.normal(0, 10),
                      "memory_utilization": 60 + np.random.normal(0, 15),
                      "replica_count": 3 + np.random.randint(-1, 2),
                      "database_connections": 25 + np.random.randint(-5, 10),
                      "cache_hit_rate": 0.75 + np.random.normal(0, 0.1),
                      "day_of_week": date.weekday(),
                      "hour_of_day": 12  # Simplified
                  })
              return data
          
          def get_tenant_resource_usage_data(tenant_id):
              """Get detailed resource usage data"""
              # Placeholder - integrate with actual monitoring
              return {
                  "cpu_usage_percentiles": {"p50": 30, "p95": 60, "p99": 80},
                  "memory_usage_percentiles": {"p50": 40, "p95": 70, "p99": 85},
                  "network_usage": {"avg_mbps": 10, "peak_mbps": 50}
              }
          
          def calculate_cpu_savings(tenant_id, reduction_factor):
              """Calculate potential CPU cost savings"""
              return 50.0 * reduction_factor  # Simplified calculation
          
          def calculate_memory_savings(tenant_id, reduction_factor):
              """Calculate potential memory cost savings"""
              return 30.0 * reduction_factor  # Simplified calculation
          
          def calculate_scaling_savings(tenant_id, avg_replicas, max_replicas):
              """Calculate scaling optimization savings"""
              return (max_replicas - avg_replicas) * 20.0  # $20 per replica per month
          
          def calculate_scheduled_scaling_savings(tenant_id, low_usage_hours):
              """Calculate scheduled scaling savings"""
              return low_usage_hours * 2.0  # $2 per hour of scaling down
          
          def calculate_confidence_score(recommendations):
              """Calculate overall confidence score for recommendations"""
              if not recommendations:
                  return 0
              return sum(r['confidence'] for r in recommendations) / len(recommendations)
          
          def collect_training_data():
              """Collect training data from all tenants"""
              # Placeholder - collect data from all tenants
              return []
          
          def train_cost_prediction_model(training_data):
              """Train cost prediction model"""
              return {"accuracy": 0.85}
          
          def train_resource_optimization_model(training_data):
              """Train resource optimization model"""
              return {"accuracy": 0.80}
          
          def generate_cost_forecast(model, historical_data, days_ahead):
              """Generate cost forecast"""
              # Simplified forecast
              base_cost = 15.0
              forecast = []
              for i in range(days_ahead):
                  date = datetime.now() + timedelta(days=i+1)
                  predicted_cost = base_cost + np.random.normal(0, 1)
                  forecast.append({
                      "date": date.strftime('%Y-%m-%d'),
                      "predicted_cost": max(0, predicted_cost),
                      "confidence_interval": [predicted_cost - 2, predicted_cost + 2]
                  })
              return forecast
          
          def detect_cost_anomalies(forecast):
              """Detect cost anomalies in forecast"""
              anomalies = []
              costs = [f['predicted_cost'] for f in forecast]
              mean_cost = np.mean(costs)
              std_cost = np.std(costs)
              
              for i, f in enumerate(forecast):
                  if f['predicted_cost'] > mean_cost + 2 * std_cost:
                      anomalies.append({
                          "date": f['date'],
                          "predicted_cost": f['predicted_cost'],
                          "anomaly_type": "cost_spike",
                          "severity": "high" if f['predicted_cost'] > mean_cost + 3 * std_cost else "medium"
                      })
              
              return anomalies
          
          @app.route('/health', methods=['GET'])
          def health_check():
              return jsonify({"status": "healthy", "timestamp": datetime.utcnow().isoformat()})
          
          if __name__ == '__main__':
              # Start Prometheus metrics server
              start_http_server(8000)
              
              # Start Flask app
              app.run(host='0.0.0.0', port=8080)
          EOF
          
          python /app/ml_optimizer.py
        ports:
        - containerPort: 8080
          name: http
        - containerPort: 8000
          name: metrics
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          capabilities:
            drop: ["ALL"]
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        volumeMounts:
        - name: tmp
          mountPath: /tmp
        - name: app
          mountPath: /app
        - name: models
          mountPath: /models
        env:
        - name: AWS_DEFAULT_REGION
          value: "eu-central-1"
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 60
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
      volumes:
      - name: tmp
        emptyDir: {}
      - name: app
        emptyDir: {}
      - name: models
        emptyDir: {}
---
# ML Model Training CronJob
apiVersion: batch/v1
kind: CronJob
metadata:
  name: ml-model-training
  namespace: ml-cost-optimization
spec:
  schedule: "0 2 * * 0"  # Weekly on Sunday at 2 AM
  jobTemplate:
    spec:
      template:
        spec:
          serviceAccountName: ml-cost-optimizer
          securityContext:
            runAsNonRoot: true
            runAsUser: 65534
            runAsGroup: 65534
            fsGroup: 65534
            seccompProfile:
              type: RuntimeDefault
          containers:
          - name: trainer
            image: curlimages/curl:latest
            command: ["/bin/sh", "-c"]
            args:
            - |
              echo "Starting ML model training..."
              curl -X POST http://ml-cost-optimizer.ml-cost-optimization.svc.cluster.local:8080/api/ml-optimization/train-models
              echo "ML model training completed"
            securityContext:
              allowPrivilegeEscalation: false
              readOnlyRootFilesystem: true
              capabilities:
                drop: ["ALL"]
            resources:
              requests:
                memory: "64Mi"
                cpu: "50m"
              limits:
                memory: "128Mi"
                cpu: "100m"
          restartPolicy: OnFailure
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: ml-cost-optimizer
  namespace: ml-cost-optimization
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: ml-cost-optimizer
rules:
- apiGroups: [""]
  resources: ["namespaces", "pods"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["apps"]
  resources: ["deployments"]
  verbs: ["get", "list", "watch"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: ml-cost-optimizer
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: ml-cost-optimizer
subjects:
- kind: ServiceAccount
  name: ml-cost-optimizer
  namespace: ml-cost-optimization
---
apiVersion: v1
kind: Service
metadata:
  name: ml-cost-optimizer
  namespace: ml-cost-optimization
  labels:
    app: ml-cost-optimizer
spec:
  ports:
  - port: 8080
    targetPort: 8080
    name: http
  - port: 8000
    targetPort: 8000
    name: metrics
  selector:
    app: ml-cost-optimizer
