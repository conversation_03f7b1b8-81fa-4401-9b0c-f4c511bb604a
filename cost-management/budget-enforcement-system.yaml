---
# Budget Enforcement with Automatic Scaling Limits
apiVersion: v1
kind: Namespace
metadata:
  name: budget-enforcement
  labels:
    pod-security.kubernetes.io/enforce: restricted
    pod-security.kubernetes.io/audit: restricted
    pod-security.kubernetes.io/warn: restricted
---
# Budget Enforcement Controller
apiVersion: apps/v1
kind: Deployment
metadata:
  name: budget-enforcement-controller
  namespace: budget-enforcement
  labels:
    app: budget-enforcement-controller
spec:
  replicas: 2
  selector:
    matchLabels:
      app: budget-enforcement-controller
  template:
    metadata:
      labels:
        app: budget-enforcement-controller
    spec:
      serviceAccountName: budget-enforcement-controller
      securityContext:
        runAsNonRoot: true
        runAsUser: 65534
        runAsGroup: 65534
        fsGroup: 65534
        seccompProfile:
          type: RuntimeDefault
      containers:
      - name: controller
        image: python:3.11-alpine
        command: ["/bin/sh", "-c"]
        args:
        - |
          pip install flask kubernetes boto3 mysql-connector-python prometheus_client
          cat > /app/budget_controller.py << 'EOF'
          #!/usr/bin/env python3
          import json
          import time
          import boto3
          import mysql.connector
          from datetime import datetime, timedelta
          from flask import Flask, request, jsonify
          from kubernetes import client, config
          from prometheus_client import Gauge, Counter, start_http_server
          
          app = Flask(__name__)
          
          # Prometheus metrics
          budget_utilization = Gauge('budget_utilization_percentage', 'Budget utilization percentage', ['tenant_id'])
          budget_violations = Counter('budget_violations_total', 'Total budget violations', ['tenant_id', 'action'])
          scaling_actions = Counter('scaling_actions_total', 'Total scaling actions', ['tenant_id', 'action'])
          
          # Load Kubernetes config
          try:
              config.load_incluster_config()
          except:
              config.load_kube_config()
          
          k8s_client = client.CoreV1Api()
          apps_client = client.AppsV1Api()
          autoscaling_client = client.AutoscalingV2Api()
          
          # AWS clients
          budgets_client = boto3.client('budgets', region_name='us-east-1')
          ce_client = boto3.client('ce', region_name='us-east-1')
          
          # Budget enforcement rules
          ENFORCEMENT_RULES = {
              "warning_threshold": 80,    # 80% of budget
              "critical_threshold": 95,   # 95% of budget
              "emergency_threshold": 100, # 100% of budget
              "actions": {
                  "warning": ["notify", "log"],
                  "critical": ["notify", "log", "scale_down_non_critical"],
                  "emergency": ["notify", "log", "scale_down_all", "suspend_scaling"]
              }
          }
          
          @app.route('/api/budgets/tenant/<tenant_id>', methods=['GET'])
          def get_tenant_budget(tenant_id):
              """Get budget information for a tenant"""
              try:
                  budget_info = get_budget_from_database(tenant_id)
                  current_spend = get_current_spend(tenant_id)
                  
                  utilization = (current_spend / budget_info['monthly_limit']) * 100 if budget_info['monthly_limit'] > 0 else 0
                  budget_utilization.labels(tenant_id=tenant_id).set(utilization)
                  
                  return jsonify({
                      "tenant_id": tenant_id,
                      "budget": budget_info,
                      "current_spend": current_spend,
                      "utilization_percentage": utilization,
                      "status": get_budget_status(utilization),
                      "remaining_budget": max(0, budget_info['monthly_limit'] - current_spend),
                      "days_remaining": get_days_remaining_in_month()
                  })
              
              except Exception as e:
                  return jsonify({"error": str(e)}), 500
          
          @app.route('/api/budgets/tenant/<tenant_id>', methods=['POST'])
          def set_tenant_budget(tenant_id):
              """Set or update budget for a tenant"""
              try:
                  data = request.get_json()
                  monthly_limit = data.get('monthly_limit')
                  auto_scaling_limit = data.get('auto_scaling_limit', monthly_limit * 0.8)
                  emergency_actions = data.get('emergency_actions', ['scale_down', 'notify'])
                  
                  if not monthly_limit or monthly_limit <= 0:
                      return jsonify({"error": "Invalid monthly_limit"}), 400
                  
                  # Save budget to database
                  save_budget_to_database(tenant_id, {
                      "monthly_limit": monthly_limit,
                      "auto_scaling_limit": auto_scaling_limit,
                      "emergency_actions": emergency_actions,
                      "created_at": datetime.utcnow().isoformat(),
                      "updated_at": datetime.utcnow().isoformat()
                  })
                  
                  # Create AWS Budget
                  create_aws_budget(tenant_id, monthly_limit)
                  
                  # Update HPA limits based on budget
                  update_hpa_limits(tenant_id, auto_scaling_limit)
                  
                  return jsonify({
                      "status": "success",
                      "tenant_id": tenant_id,
                      "monthly_limit": monthly_limit,
                      "auto_scaling_limit": auto_scaling_limit,
                      "message": "Budget set successfully"
                  })
              
              except Exception as e:
                  return jsonify({"error": str(e)}), 500
          
          @app.route('/api/budgets/enforce', methods=['POST'])
          def enforce_budgets():
              """Manually trigger budget enforcement for all tenants"""
              try:
                  enforcement_results = []
                  
                  # Get all tenants
                  tenants = get_all_tenants()
                  
                  for tenant_id in tenants:
                      result = enforce_tenant_budget(tenant_id)
                      enforcement_results.append(result)
                  
                  return jsonify({
                      "status": "success",
                      "enforcement_results": enforcement_results,
                      "timestamp": datetime.utcnow().isoformat()
                  })
              
              except Exception as e:
                  return jsonify({"error": str(e)}), 500
          
          @app.route('/api/budgets/violations', methods=['GET'])
          def get_budget_violations():
              """Get recent budget violations"""
              try:
                  violations = get_violations_from_database()
                  return jsonify({
                      "violations": violations,
                      "count": len(violations)
                  })
              
              except Exception as e:
                  return jsonify({"error": str(e)}), 500
          
          def enforce_tenant_budget(tenant_id):
              """Enforce budget limits for a specific tenant"""
              try:
                  budget_info = get_budget_from_database(tenant_id)
                  current_spend = get_current_spend(tenant_id)
                  
                  if not budget_info:
                      return {"tenant_id": tenant_id, "status": "no_budget_set"}
                  
                  utilization = (current_spend / budget_info['monthly_limit']) * 100
                  budget_utilization.labels(tenant_id=tenant_id).set(utilization)
                  
                  actions_taken = []
                  
                  # Determine enforcement level
                  if utilization >= ENFORCEMENT_RULES["emergency_threshold"]:
                      level = "emergency"
                  elif utilization >= ENFORCEMENT_RULES["critical_threshold"]:
                      level = "critical"
                  elif utilization >= ENFORCEMENT_RULES["warning_threshold"]:
                      level = "warning"
                  else:
                      level = "normal"
                  
                  # Execute enforcement actions
                  if level != "normal":
                      for action in ENFORCEMENT_RULES["actions"][level]:
                          result = execute_enforcement_action(tenant_id, action, utilization)
                          actions_taken.append(result)
                          
                          # Record violation
                          budget_violations.labels(tenant_id=tenant_id, action=action).inc()
                  
                  # Log enforcement result
                  log_enforcement_action(tenant_id, level, utilization, actions_taken)
                  
                  return {
                      "tenant_id": tenant_id,
                      "utilization": utilization,
                      "level": level,
                      "actions_taken": actions_taken,
                      "status": "enforced"
                  }
              
              except Exception as e:
                  return {"tenant_id": tenant_id, "status": "error", "error": str(e)}
          
          def execute_enforcement_action(tenant_id, action, utilization):
              """Execute a specific enforcement action"""
              try:
                  if action == "notify":
                      return send_budget_notification(tenant_id, utilization)
                  
                  elif action == "log":
                      return log_budget_violation(tenant_id, utilization)
                  
                  elif action == "scale_down_non_critical":
                      return scale_down_non_critical_workloads(tenant_id)
                  
                  elif action == "scale_down_all":
                      return scale_down_all_workloads(tenant_id)
                  
                  elif action == "suspend_scaling":
                      return suspend_auto_scaling(tenant_id)
                  
                  else:
                      return {"action": action, "status": "unknown_action"}
              
              except Exception as e:
                  return {"action": action, "status": "error", "error": str(e)}
          
          def scale_down_non_critical_workloads(tenant_id):
              """Scale down non-critical workloads to reduce costs"""
              try:
                  namespace = f"tenant-{tenant_id}"
                  deployments = apps_client.list_namespaced_deployment(namespace=namespace)
                  
                  scaled_deployments = []
                  
                  for deployment in deployments.items:
                      # Check if deployment is marked as non-critical
                      labels = deployment.metadata.labels or {}
                      if labels.get('criticality') in ['low', 'non-critical']:
                          current_replicas = deployment.spec.replicas
                          new_replicas = max(1, current_replicas // 2)  # Scale down by 50%
                          
                          # Update deployment
                          deployment.spec.replicas = new_replicas
                          apps_client.patch_namespaced_deployment(
                              name=deployment.metadata.name,
                              namespace=namespace,
                              body=deployment
                          )
                          
                          scaled_deployments.append({
                              "name": deployment.metadata.name,
                              "old_replicas": current_replicas,
                              "new_replicas": new_replicas
                          })
                          
                          scaling_actions.labels(tenant_id=tenant_id, action='scale_down_non_critical').inc()
                  
                  return {
                      "action": "scale_down_non_critical",
                      "status": "success",
                      "scaled_deployments": scaled_deployments
                  }
              
              except Exception as e:
                  return {"action": "scale_down_non_critical", "status": "error", "error": str(e)}
          
          def scale_down_all_workloads(tenant_id):
              """Scale down all workloads to minimum replicas"""
              try:
                  namespace = f"tenant-{tenant_id}"
                  deployments = apps_client.list_namespaced_deployment(namespace=namespace)
                  
                  scaled_deployments = []
                  
                  for deployment in deployments.items:
                      current_replicas = deployment.spec.replicas
                      
                      # Don't scale down critical infrastructure (database, etc.)
                      labels = deployment.metadata.labels or {}
                      if labels.get('criticality') == 'critical':
                          continue
                      
                      new_replicas = 1  # Scale to minimum
                      
                      if current_replicas > new_replicas:
                          deployment.spec.replicas = new_replicas
                          apps_client.patch_namespaced_deployment(
                              name=deployment.metadata.name,
                              namespace=namespace,
                              body=deployment
                          )
                          
                          scaled_deployments.append({
                              "name": deployment.metadata.name,
                              "old_replicas": current_replicas,
                              "new_replicas": new_replicas
                          })
                          
                          scaling_actions.labels(tenant_id=tenant_id, action='scale_down_all').inc()
                  
                  return {
                      "action": "scale_down_all",
                      "status": "success",
                      "scaled_deployments": scaled_deployments
                  }
              
              except Exception as e:
                  return {"action": "scale_down_all", "status": "error", "error": str(e)}
          
          def suspend_auto_scaling(tenant_id):
              """Suspend auto-scaling for the tenant"""
              try:
                  namespace = f"tenant-{tenant_id}"
                  hpas = autoscaling_client.list_namespaced_horizontal_pod_autoscaler(namespace=namespace)
                  
                  suspended_hpas = []
                  
                  for hpa in hpas.items:
                      # Add annotation to suspend HPA
                      if not hpa.metadata.annotations:
                          hpa.metadata.annotations = {}
                      
                      hpa.metadata.annotations['budget-enforcement/suspended'] = 'true'
                      hpa.metadata.annotations['budget-enforcement/suspended-at'] = datetime.utcnow().isoformat()
                      
                      # Set max replicas to current replicas to prevent scaling up
                      current_replicas = hpa.status.current_replicas or 1
                      hpa.spec.max_replicas = current_replicas
                      
                      autoscaling_client.patch_namespaced_horizontal_pod_autoscaler(
                          name=hpa.metadata.name,
                          namespace=namespace,
                          body=hpa
                      )
                      
                      suspended_hpas.append({
                          "name": hpa.metadata.name,
                          "max_replicas_set_to": current_replicas
                      })
                      
                      scaling_actions.labels(tenant_id=tenant_id, action='suspend_scaling').inc()
                  
                  return {
                      "action": "suspend_scaling",
                      "status": "success",
                      "suspended_hpas": suspended_hpas
                  }
              
              except Exception as e:
                  return {"action": "suspend_scaling", "status": "error", "error": str(e)}
          
          def update_hpa_limits(tenant_id, budget_limit):
              """Update HPA max replicas based on budget"""
              try:
                  namespace = f"tenant-{tenant_id}"
                  hpas = autoscaling_client.list_namespaced_horizontal_pod_autoscaler(namespace=namespace)
                  
                  # Calculate max replicas based on budget
                  # Simplified: assume each replica costs $10/month
                  max_replicas_per_hpa = max(1, int(budget_limit / 100))  # Conservative estimate
                  
                  for hpa in hpas.items:
                      if hpa.spec.max_replicas > max_replicas_per_hpa:
                          hpa.spec.max_replicas = max_replicas_per_hpa
                          
                          autoscaling_client.patch_namespaced_horizontal_pod_autoscaler(
                              name=hpa.metadata.name,
                              namespace=namespace,
                              body=hpa
                          )
              
              except Exception as e:
                  print(f"Error updating HPA limits: {e}")
          
          def send_budget_notification(tenant_id, utilization):
              """Send budget notification (placeholder)"""
              # In production, integrate with email/Slack/webhook
              message = f"Budget alert for tenant {tenant_id}: {utilization:.1f}% utilized"
              print(f"NOTIFICATION: {message}")
              
              return {
                  "action": "notify",
                  "status": "success",
                  "message": message
              }
          
          def log_budget_violation(tenant_id, utilization):
              """Log budget violation"""
              violation = {
                  "tenant_id": tenant_id,
                  "utilization": utilization,
                  "timestamp": datetime.utcnow().isoformat(),
                  "type": "budget_violation"
              }
              
              # Save to database
              save_violation_to_database(violation)
              
              return {
                  "action": "log",
                  "status": "success",
                  "violation": violation
              }
          
          def get_budget_from_database(tenant_id):
              """Get budget information from database"""
              try:
                  conn = mysql.connector.connect(
                      host='production-aurora-serverless.cluster-cpmagwki2kv8.eu-central-1.rds.amazonaws.com',
                      port=3306,
                      user='admin',
                      password='&BZzY_<AK(=a*UhZ',
                      database='architrave'
                  )
                  
                  cursor = conn.cursor(dictionary=True)
                  cursor.execute(
                      "SELECT * FROM tenant_budgets WHERE tenant_id = %s",
                      (tenant_id,)
                  )
                  
                  result = cursor.fetchone()
                  conn.close()
                  
                  return result
              except:
                  return None
          
          def save_budget_to_database(tenant_id, budget_data):
              """Save budget information to database"""
              try:
                  conn = mysql.connector.connect(
                      host='production-aurora-serverless.cluster-cpmagwki2kv8.eu-central-1.rds.amazonaws.com',
                      port=3306,
                      user='admin',
                      password='&BZzY_<AK(=a*UhZ',
                      database='architrave'
                  )
                  
                  cursor = conn.cursor()
                  
                  # Create table if not exists
                  cursor.execute("""
                      CREATE TABLE IF NOT EXISTS tenant_budgets (
                          tenant_id VARCHAR(50) PRIMARY KEY,
                          monthly_limit DECIMAL(10,2),
                          auto_scaling_limit DECIMAL(10,2),
                          emergency_actions JSON,
                          created_at TIMESTAMP,
                          updated_at TIMESTAMP
                      )
                  """)
                  
                  # Insert or update budget
                  cursor.execute("""
                      INSERT INTO tenant_budgets 
                      (tenant_id, monthly_limit, auto_scaling_limit, emergency_actions, created_at, updated_at)
                      VALUES (%s, %s, %s, %s, %s, %s)
                      ON DUPLICATE KEY UPDATE
                      monthly_limit = VALUES(monthly_limit),
                      auto_scaling_limit = VALUES(auto_scaling_limit),
                      emergency_actions = VALUES(emergency_actions),
                      updated_at = VALUES(updated_at)
                  """, (
                      tenant_id,
                      budget_data['monthly_limit'],
                      budget_data['auto_scaling_limit'],
                      json.dumps(budget_data['emergency_actions']),
                      budget_data['created_at'],
                      budget_data['updated_at']
                  ))
                  
                  conn.commit()
                  conn.close()
              except Exception as e:
                  print(f"Error saving budget: {e}")
          
          def get_current_spend(tenant_id):
              """Get current month spend for tenant"""
              # This would integrate with the cost explorer API
              # For now, return a placeholder value
              return 450.0  # Placeholder
          
          def get_budget_status(utilization):
              """Get budget status based on utilization"""
              if utilization >= 100:
                  return "emergency"
              elif utilization >= 95:
                  return "critical"
              elif utilization >= 80:
                  return "warning"
              else:
                  return "normal"
          
          def get_days_remaining_in_month():
              """Get days remaining in current month"""
              now = datetime.now()
              next_month = now.replace(day=28) + timedelta(days=4)
              last_day = next_month - timedelta(days=next_month.day)
              return (last_day - now).days
          
          def get_all_tenants():
              """Get list of all tenant IDs"""
              try:
                  namespaces = k8s_client.list_namespace()
                  return [ns.metadata.name.replace('tenant-', '') 
                         for ns in namespaces.items 
                         if ns.metadata.name.startswith('tenant-')]
              except:
                  return []
          
          def create_aws_budget(tenant_id, monthly_limit):
              """Create AWS Budget for tenant"""
              try:
                  budget_name = f"tenant-{tenant_id}-monthly-budget"
                  
                  budgets_client.create_budget(
                      AccountId='************',  # Replace with actual account ID
                      Budget={
                          'BudgetName': budget_name,
                          'BudgetLimit': {
                              'Amount': str(monthly_limit),
                              'Unit': 'USD'
                          },
                          'TimeUnit': 'MONTHLY',
                          'BudgetType': 'COST',
                          'CostFilters': {
                              'TagKey': ['tenant-id'],
                              'TagValue': [tenant_id]
                          }
                      }
                  )
              except Exception as e:
                  print(f"Error creating AWS budget: {e}")
          
          def save_violation_to_database(violation):
              """Save budget violation to database"""
              # Placeholder - implement database logging
              pass
          
          def get_violations_from_database():
              """Get recent budget violations from database"""
              # Placeholder - implement database retrieval
              return []
          
          def log_enforcement_action(tenant_id, level, utilization, actions):
              """Log enforcement action"""
              print(f"ENFORCEMENT: Tenant {tenant_id}, Level: {level}, Utilization: {utilization:.1f}%, Actions: {actions}")
          
          @app.route('/health', methods=['GET'])
          def health_check():
              return jsonify({"status": "healthy", "timestamp": datetime.utcnow().isoformat()})
          
          if __name__ == '__main__':
              # Start Prometheus metrics server
              start_http_server(8000)
              
              # Start Flask app
              app.run(host='0.0.0.0', port=8080)
          EOF
          
          python /app/budget_controller.py
        ports:
        - containerPort: 8080
          name: http
        - containerPort: 8000
          name: metrics
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          capabilities:
            drop: ["ALL"]
        resources:
          requests:
            memory: "512Mi"
            cpu: "200m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        volumeMounts:
        - name: tmp
          mountPath: /tmp
        - name: app
          mountPath: /app
        env:
        - name: AWS_DEFAULT_REGION
          value: "eu-central-1"
        - name: AWS_ACCESS_KEY_ID
          valueFrom:
            secretKeyRef:
              name: aws-credentials
              key: access-key-id
        - name: AWS_SECRET_ACCESS_KEY
          valueFrom:
            secretKeyRef:
              name: aws-credentials
              key: secret-access-key
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 10
      volumes:
      - name: tmp
        emptyDir: {}
      - name: app
        emptyDir: {}
---
# Budget Enforcement CronJob
apiVersion: batch/v1
kind: CronJob
metadata:
  name: budget-enforcement-job
  namespace: budget-enforcement
spec:
  schedule: "*/15 * * * *"  # Every 15 minutes
  jobTemplate:
    spec:
      template:
        spec:
          serviceAccountName: budget-enforcement-controller
          securityContext:
            runAsNonRoot: true
            runAsUser: 65534
            runAsGroup: 65534
            fsGroup: 65534
            seccompProfile:
              type: RuntimeDefault
          containers:
          - name: enforcer
            image: curlimages/curl:latest
            command: ["/bin/sh", "-c"]
            args:
            - |
              echo "Running budget enforcement..."
              curl -X POST http://budget-enforcement-controller.budget-enforcement.svc.cluster.local:8080/api/budgets/enforce
              echo "Budget enforcement completed"
            securityContext:
              allowPrivilegeEscalation: false
              readOnlyRootFilesystem: true
              capabilities:
                drop: ["ALL"]
            resources:
              requests:
                memory: "64Mi"
                cpu: "50m"
              limits:
                memory: "128Mi"
                cpu: "100m"
          restartPolicy: OnFailure
---
apiVersion: v1
kind: Secret
metadata:
  name: aws-credentials
  namespace: budget-enforcement
type: Opaque
data:
  access-key-id: eW91ci1hd3MtYWNjZXNzLWtleS1pZA==  # base64 encoded AWS access key
  secret-access-key: eW91ci1hd3Mtc2VjcmV0LWFjY2Vzcy1rZXk=  # base64 encoded AWS secret key
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: budget-enforcement-controller
  namespace: budget-enforcement
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::ACCOUNT_ID:role/budget-enforcement-role
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: budget-enforcement-controller
rules:
- apiGroups: [""]
  resources: ["namespaces", "pods"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["apps"]
  resources: ["deployments"]
  verbs: ["get", "list", "watch", "update", "patch"]
- apiGroups: ["autoscaling"]
  resources: ["horizontalpodautoscalers"]
  verbs: ["get", "list", "watch", "update", "patch"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: budget-enforcement-controller
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: budget-enforcement-controller
subjects:
- kind: ServiceAccount
  name: budget-enforcement-controller
  namespace: budget-enforcement
---
apiVersion: v1
kind: Service
metadata:
  name: budget-enforcement-controller
  namespace: budget-enforcement
  labels:
    app: budget-enforcement-controller
spec:
  ports:
  - port: 8080
    targetPort: 8080
    name: http
  - port: 8000
    targetPort: 8000
    name: metrics
  selector:
    app: budget-enforcement-controller
