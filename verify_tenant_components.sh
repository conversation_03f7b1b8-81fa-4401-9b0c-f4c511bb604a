#!/bin/bash

# Comprehensive Tenant Component Verification Script
# Tests all aspects of tenant onboarding and functionality

set -e

TENANT_ID="test-tenant-maintenance"
NAMESPACE="tenant-test-tenant-maintenance"
DOMAIN="test-tenant-maintenance.architrave-assets.com"

echo "=========================================="
echo "COMPREHENSIVE TENANT COMPONENT VERIFICATION"
echo "=========================================="
echo "Tenant ID: $TENANT_ID"
echo "Namespace: $NAMESPACE"
echo "Domain: $DOMAIN"
echo "=========================================="

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    local status=$1
    local message=$2
    case $status in
        "PASS")
            echo -e "${GREEN}✓ PASS${NC}: $message"
            ;;
        "FAIL")
            echo -e "${RED}✗ FAIL${NC}: $message"
            ;;
        "WARN")
            echo -e "${YELLOW}⚠ WARN${NC}: $message"
            ;;
        "INFO")
            echo -e "${BLUE}ℹ INFO${NC}: $message"
            ;;
    esac
}

# 1. VERIFY ONBOARDING SCRIPT CONFIGURATION
echo -e "\n${BLUE}1. VERIFYING ONBOARDING SCRIPT CONFIGURATION${NC}"
echo "=========================================="

# Check if onboarding script exists
if [ -f "advanced_tenant_onboard.go" ]; then
    print_status "PASS" "Onboarding script exists"
else
    print_status "FAIL" "Onboarding script not found"
    exit 1
fi

# Verify image configurations
BACKEND_IMAGE=$(grep "DEFAULT_BACKEND_IMAGE" advanced_tenant_onboard.go | head -1 | grep -o '"[^"]*"' | tr -d '"')
RABBITMQ_IMAGE=$(grep "DEFAULT_RABBITMQ_IMAGE" advanced_tenant_onboard.go | head -1 | grep -o '"[^"]*"' | tr -d '"')
FRONTEND_IMAGE=$(grep "DEFAULT_FRONTEND_IMAGE" advanced_tenant_onboard.go | head -1 | grep -o '"[^"]*"' | tr -d '"')

EXPECTED_BACKEND="545009857703.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:2.0.56-test"
EXPECTED_RABBITMQ="545009857703.dkr.ecr.eu-central-1.amazonaws.com/rabbitmq_dev:1.02"
EXPECTED_FRONTEND="545009857703.dkr.ecr.eu-central-1.amazonaws.com/nginx_dev:1.0.6-update_ssl"

if [ "$BACKEND_IMAGE" = "$EXPECTED_BACKEND" ]; then
    print_status "PASS" "Backend image configured correctly: $BACKEND_IMAGE"
else
    print_status "FAIL" "Backend image mismatch. Expected: $EXPECTED_BACKEND, Got: $BACKEND_IMAGE"
fi

if [ "$RABBITMQ_IMAGE" = "$EXPECTED_RABBITMQ" ]; then
    print_status "PASS" "RabbitMQ image configured correctly: $RABBITMQ_IMAGE"
else
    print_status "FAIL" "RabbitMQ image mismatch. Expected: $EXPECTED_RABBITMQ, Got: $RABBITMQ_IMAGE"
fi

if [ "$FRONTEND_IMAGE" = "$EXPECTED_FRONTEND" ]; then
    print_status "PASS" "Frontend image configured correctly: $FRONTEND_IMAGE"
else
    print_status "FAIL" "Frontend image mismatch. Expected: $EXPECTED_FRONTEND, Got: $FRONTEND_IMAGE"
fi

# 2. VERIFY CURRENT DEPLOYMENT IMAGES
echo -e "\n${BLUE}2. VERIFYING CURRENT DEPLOYMENT IMAGES${NC}"
echo "=========================================="

# Get actual deployed images
ACTUAL_BACKEND_IMAGE=$(kubectl get deployment -n $NAMESPACE test-$TENANT_ID-backend -o jsonpath='{.spec.template.spec.containers[0].image}' 2>/dev/null || echo "not-found")
ACTUAL_FRONTEND_IMAGE=$(kubectl get deployment -n $NAMESPACE test-$TENANT_ID-frontend -o jsonpath='{.spec.template.spec.containers[0].image}' 2>/dev/null || echo "not-found")
ACTUAL_RABBITMQ_IMAGE=$(kubectl get deployment -n $NAMESPACE test-$TENANT_ID-rabbitmq -o jsonpath='{.spec.template.spec.containers[0].image}' 2>/dev/null || echo "not-found")

if [ "$ACTUAL_BACKEND_IMAGE" = "$EXPECTED_BACKEND" ]; then
    print_status "PASS" "Backend deployment using correct image: $ACTUAL_BACKEND_IMAGE"
else
    print_status "FAIL" "Backend deployment using wrong image. Expected: $EXPECTED_BACKEND, Got: $ACTUAL_BACKEND_IMAGE"
fi

if [ "$ACTUAL_FRONTEND_IMAGE" = "$EXPECTED_FRONTEND" ]; then
    print_status "PASS" "Frontend deployment using correct image: $ACTUAL_FRONTEND_IMAGE"
else
    print_status "FAIL" "Frontend deployment using wrong image. Expected: $EXPECTED_FRONTEND, Got: $ACTUAL_FRONTEND_IMAGE"
fi

if [ "$ACTUAL_RABBITMQ_IMAGE" = "$EXPECTED_RABBITMQ" ]; then
    print_status "PASS" "RabbitMQ deployment using correct image: $ACTUAL_RABBITMQ_IMAGE"
else
    print_status "FAIL" "RabbitMQ deployment using wrong image. Expected: $EXPECTED_RABBITMQ, Got: $ACTUAL_RABBITMQ_IMAGE"
fi

# 3. VERIFY POD STATUS AND COMMUNICATION
echo -e "\n${BLUE}3. VERIFYING POD STATUS AND COMMUNICATION${NC}"
echo "=========================================="

# Check pod status
PODS=$(kubectl get pods -n $NAMESPACE --no-headers | wc -l)
READY_PODS=$(kubectl get pods -n $NAMESPACE --no-headers | grep "Running" | wc -l)

if [ "$PODS" -eq "$READY_PODS" ]; then
    print_status "PASS" "All pods are running ($READY_PODS/$PODS)"
else
    print_status "FAIL" "Not all pods are running ($READY_PODS/$PODS)"
fi

# Test pod-to-pod communication
echo -e "\n${YELLOW}Testing pod-to-pod communication...${NC}"

# Frontend to Backend
if kubectl exec -n $NAMESPACE $(kubectl get pods -n $NAMESPACE -l app=test-$TENANT_ID-frontend -o jsonpath='{.items[0].metadata.name}') -- curl -s -o /dev/null -w "%{http_code}" http://test-$TENANT_ID-backend-service:8080/health | grep -q "200\|500"; then
    print_status "PASS" "Frontend can reach backend service"
else
    print_status "FAIL" "Frontend cannot reach backend service"
fi

# Backend to RabbitMQ (test AMQP port)
if kubectl exec -n $NAMESPACE $(kubectl get pods -n $NAMESPACE -l app=test-$TENANT_ID-backend -o jsonpath='{.items[0].metadata.name}') -c backend -- timeout 5 bash -c "echo 'test' | nc test-$TENANT_ID-rabbitmq-service 5672" >/dev/null 2>&1; then
    print_status "PASS" "Backend can reach RabbitMQ service"
else
    print_status "FAIL" "Backend cannot reach RabbitMQ service"
fi

# 4. VERIFY DATABASE CONNECTIVITY
echo -e "\n${BLUE}4. VERIFYING DATABASE CONNECTIVITY${NC}"
echo "=========================================="

# Check if database secret exists
if kubectl get secret -n $NAMESPACE test-$TENANT_ID-db-secret >/dev/null 2>&1; then
    print_status "PASS" "Database secret exists"
else
    print_status "FAIL" "Database secret not found"
fi

# Test database connectivity
DB_HOST="production-aurora-serverless.cluster-cpmagwki2kv8.eu-central-1.rds.amazonaws.com"
DB_PASSWORD=$(kubectl get secret -n $NAMESPACE test-$TENANT_ID-db-secret -o jsonpath='{.data.password}' | base64 -d 2>/dev/null || echo "failed")

if [ "$DB_PASSWORD" != "failed" ]; then
    if kubectl exec -n $NAMESPACE $(kubectl get pods -n $NAMESPACE -l app=test-$TENANT_ID-backend -o jsonpath='{.items[0].metadata.name}') -c backend -- mysql -h $DB_HOST -u admin -p$DB_PASSWORD -e "SELECT 1;" >/dev/null 2>&1; then
        print_status "PASS" "Database connectivity successful"
    else
        print_status "FAIL" "Database connectivity failed"
    fi
else
    print_status "FAIL" "Cannot retrieve database password"
fi

# 5. VERIFY S3 ACCESS
echo -e "\n${BLUE}5. VERIFYING S3 ACCESS${NC}"
echo "=========================================="

if kubectl exec -n $NAMESPACE $(kubectl get pods -n $NAMESPACE -l app=test-$TENANT_ID-backend -o jsonpath='{.items[0].metadata.name}') -c backend -- aws s3 ls s3://architravetestdb --region eu-central-1 >/dev/null 2>&1; then
    print_status "PASS" "S3 access from web app successful"
else
    print_status "FAIL" "S3 access from web app failed"
fi

# 6. VERIFY SSL/TLS CONFIGURATION
echo -e "\n${BLUE}6. VERIFYING SSL/TLS CONFIGURATION${NC}"
echo "=========================================="

# Check Istio gateway
if kubectl get gateway -n istio-system tenant-gateway >/dev/null 2>&1; then
    print_status "PASS" "Istio gateway exists"
else
    print_status "FAIL" "Istio gateway not found"
fi

# Check SSL certificate
if kubectl get secret -n istio-system architrave-assets-wildcard-cert >/dev/null 2>&1; then
    print_status "PASS" "SSL certificate exists"
else
    print_status "FAIL" "SSL certificate not found"
fi

# Check virtual service
if kubectl get virtualservice -n $NAMESPACE tenant-$TENANT_ID-vs >/dev/null 2>&1; then
    print_status "PASS" "Virtual service exists"
else
    print_status "FAIL" "Virtual service not found"
fi

# 7. VERIFY NAMESPACE ISOLATION
echo -e "\n${BLUE}7. VERIFYING NAMESPACE ISOLATION${NC}"
echo "=========================================="

# Check network policies
NP_COUNT=$(kubectl get networkpolicy -n $NAMESPACE --no-headers 2>/dev/null | wc -l)
if [ "$NP_COUNT" -gt 0 ]; then
    print_status "PASS" "Network policies configured ($NP_COUNT found)"
else
    print_status "WARN" "No network policies found (namespace isolation may be limited)"
fi

# Check resource quotas
if kubectl get resourcequota -n $NAMESPACE >/dev/null 2>&1; then
    print_status "PASS" "Resource quotas configured"
else
    print_status "WARN" "No resource quotas found"
fi

# 8. VERIFY EXTERNAL ACCESS
echo -e "\n${BLUE}8. VERIFYING EXTERNAL ACCESS${NC}"
echo "=========================================="

# Check DNS resolution
if nslookup $DOMAIN >/dev/null 2>&1; then
    print_status "PASS" "DNS resolution working for $DOMAIN"
else
    print_status "FAIL" "DNS resolution failed for $DOMAIN"
fi

# Check ALB
ALB_DNS=$(kubectl get svc -n istio-system istio-ingressgateway -o jsonpath='{.status.loadBalancer.ingress[0].hostname}' 2>/dev/null || echo "none")
if [ "$ALB_DNS" != "none" ]; then
    print_status "PASS" "ALB configured: $ALB_DNS"
else
    print_status "FAIL" "ALB not configured"
fi

# Test external access (if DNS is working)
if nslookup $DOMAIN >/dev/null 2>&1; then
    HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" https://$DOMAIN 2>/dev/null || echo "000")
    if [ "$HTTP_CODE" = "200" ] || [ "$HTTP_CODE" = "301" ] || [ "$HTTP_CODE" = "302" ]; then
        print_status "PASS" "External HTTPS access working (HTTP $HTTP_CODE)"
    else
        print_status "FAIL" "External HTTPS access failed (HTTP $HTTP_CODE)"
    fi
else
    print_status "WARN" "Cannot test external access - DNS not resolved"
fi

# 9. VERIFY WAF PROTECTION
echo -e "\n${BLUE}9. VERIFYING WAF PROTECTION${NC}"
echo "=========================================="

# Check if WAF is configured (this would require AWS CLI access to WAF)
print_status "INFO" "WAF verification requires AWS WAF access - skipping"

# 10. VERIFY SERVICE MESH
echo -e "\n${BLUE}10. VERIFYING SERVICE MESH${NC}"
echo "=========================================="

# Check Istio sidecar injection
SIDECAR_COUNT=$(kubectl get pods -n $NAMESPACE -o jsonpath='{.items[*].spec.containers[*].name}' | grep -o istio-proxy | wc -l)
if [ "$SIDECAR_COUNT" -gt 0 ]; then
    print_status "PASS" "Istio sidecar injection working ($SIDECAR_COUNT sidecars)"
else
    print_status "WARN" "No Istio sidecars found"
fi

# 11. VERIFY AUTOSCALING
echo -e "\n${BLUE}11. VERIFYING AUTOSCALING${NC}"
echo "=========================================="

# Check HPA
HPA_COUNT=$(kubectl get hpa -n $NAMESPACE --no-headers 2>/dev/null | wc -l)
if [ "$HPA_COUNT" -gt 0 ]; then
    print_status "PASS" "Horizontal Pod Autoscaler configured ($HPA_COUNT found)"
else
    print_status "WARN" "No Horizontal Pod Autoscaler found"
fi

# Check cluster autoscaler
if kubectl get deployment -n kube-system cluster-autoscaler >/dev/null 2>&1; then
    print_status "PASS" "Cluster autoscaler deployed"
else
    print_status "WARN" "Cluster autoscaler not found"
fi

# 12. VERIFY MONITORING
echo -e "\n${BLUE}12. VERIFYING MONITORING${NC}"
echo "=========================================="

# Check if monitoring is configured
if kubectl get servicemonitor -n $NAMESPACE >/dev/null 2>&1; then
    print_status "PASS" "Service monitoring configured"
else
    print_status "WARN" "No service monitoring found"
fi

# Check if Grafana dashboards exist
if kubectl get configmap -n $NAMESPACE | grep -q grafana; then
    print_status "PASS" "Grafana dashboards configured"
else
    print_status "WARN" "No Grafana dashboards found"
fi

# 13. VERIFY SECURITY
echo -e "\n${BLUE}13. VERIFYING SECURITY${NC}"
echo "=========================================="

# Check pod security standards
PODS_WITH_SECURITY=$(kubectl get pods -n $NAMESPACE -o jsonpath='{.items[*].spec.securityContext}' | grep -c "runAsNonRoot\|true" || echo "0")
if [ "$PODS_WITH_SECURITY" -gt 0 ]; then
    print_status "PASS" "Pod security contexts configured"
else
    print_status "WARN" "Pod security contexts not found"
fi

# Check secrets management
SECRETS_COUNT=$(kubectl get secrets -n $NAMESPACE --no-headers | wc -l)
if [ "$SECRETS_COUNT" -gt 0 ]; then
    print_status "PASS" "Secrets configured ($SECRETS_COUNT found)"
else
    print_status "FAIL" "No secrets found"
fi

# 14. VERIFY BACKUP AND DISASTER RECOVERY
echo -e "\n${BLUE}14. VERIFYING BACKUP AND DISASTER RECOVERY${NC}"
echo "=========================================="

# Check if backup jobs exist
if kubectl get cronjob -n $NAMESPACE | grep -q backup; then
    print_status "PASS" "Backup jobs configured"
else
    print_status "WARN" "No backup jobs found"
fi

# 15. FINAL SUMMARY
echo -e "\n${BLUE}15. FINAL SUMMARY${NC}"
echo "=========================================="

print_status "INFO" "Verification completed for tenant: $TENANT_ID"
print_status "INFO" "Namespace: $NAMESPACE"
print_status "INFO" "Domain: $DOMAIN"

echo -e "\n${GREEN}✓ VERIFICATION COMPLETED${NC}"
echo "==========================================" 