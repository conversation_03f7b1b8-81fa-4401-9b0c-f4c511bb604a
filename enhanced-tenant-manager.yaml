apiVersion: v1
kind: ConfigMap
metadata:
  name: advanced-tenant-management-ui
  namespace: tenant-management
  labels:
    app: advanced-tenant-manager
    version: "2.0"
    component: ui
data:
  index.html: |
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>🏢 Advanced Tenant Management System - 100+ Tenants</title>
        <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
        <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
        <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
        <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
        <script src="https://cdn.jsdelivr.net/npm/date-fns@2.29.3/index.min.js"></script>
        <style>
            * { margin: 0; padding: 0; box-sizing: border-box; }
            body {
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                min-height: 100vh;
                color: #333;
                overflow-x: hidden;
            }
            .container {
                max-width: 1800px;
                margin: 0 auto;
                padding: 20px;
            }
            .header {
                background: rgba(255,255,255,0.95);
                backdrop-filter: blur(10px);
                border-radius: 15px;
                padding: 20px 30px;
                margin-bottom: 20px;
                box-shadow: 0 8px 32px rgba(0,0,0,0.1);
                display: flex;
                justify-content: space-between;
                align-items: center;
                flex-wrap: wrap;
                gap: 20px;
            }
            .header-left h1 {
                font-size: 2.2rem;
                background: linear-gradient(45deg, #667eea, #764ba2);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                margin-bottom: 5px;
            }
            .header-stats {
                display: flex;
                gap: 20px;
                align-items: center;
                flex-wrap: wrap;
            }
            .stat-item {
                text-align: center;
                padding: 10px 15px;
                background: rgba(0,0,0,0.05);
                border-radius: 10px;
                min-width: 80px;
            }
            .stat-number {
                font-size: 1.5rem;
                font-weight: bold;
                color: #667eea;
            }
            .stat-label {
                font-size: 0.8rem;
                color: #666;
                text-transform: uppercase;
            }
            .controls-panel {
                background: rgba(255,255,255,0.95);
                backdrop-filter: blur(10px);
                border-radius: 15px;
                padding: 20px;
                margin-bottom: 20px;
                box-shadow: 0 8px 32px rgba(0,0,0,0.1);
                display: flex;
                flex-wrap: wrap;
                gap: 15px;
                align-items: center;
                justify-content: space-between;
            }
            .search-filters {
                display: flex;
                gap: 15px;
                align-items: center;
                flex-wrap: wrap;
                flex: 1;
            }
            .search-input {
                padding: 10px 15px;
                border: 2px solid #e0e0e0;
                border-radius: 25px;
                font-size: 0.9rem;
                min-width: 250px;
                transition: border-color 0.3s ease;
            }
            .search-input:focus {
                outline: none;
                border-color: #667eea;
            }
            .filter-select {
                padding: 8px 15px;
                border: 2px solid #e0e0e0;
                border-radius: 20px;
                background: white;
                font-size: 0.9rem;
                cursor: pointer;
            }
            .bulk-actions {
                display: flex;
                gap: 10px;
                align-items: center;
            }
            .view-toggle {
                display: flex;
                background: rgba(0,0,0,0.05);
                border-radius: 25px;
                padding: 5px;
            }
            .view-btn {
                padding: 8px 15px;
                border: none;
                background: transparent;
                border-radius: 20px;
                cursor: pointer;
                transition: all 0.3s ease;
            }
            .view-btn.active {
                background: #667eea;
                color: white;
            }
            .tenant-grid {
                display: grid;
                grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
                gap: 20px;
                margin-bottom: 30px;
            }
            .tenant-list {
                display: flex;
                flex-direction: column;
                gap: 15px;
            }
            .tenant-card {
                background: rgba(255,255,255,0.95);
                backdrop-filter: blur(10px);
                border-radius: 15px;
                padding: 20px;
                box-shadow: 0 8px 32px rgba(0,0,0,0.1);
                transition: all 0.3s ease;
                cursor: pointer;
                position: relative;
            }
            .tenant-card:hover {
                transform: translateY(-3px);
                box-shadow: 0 12px 40px rgba(0,0,0,0.15);
            }
            .tenant-card.selected {
                border: 2px solid #667eea;
                transform: translateY(-2px);
            }
            .tenant-card-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 15px;
            }
            .tenant-checkbox {
                position: absolute;
                top: 15px;
                right: 15px;
                width: 18px;
                height: 18px;
                cursor: pointer;
            }
            .tenant-title {
                font-size: 1.3rem;
                font-weight: bold;
                color: #333;
                display: flex;
                align-items: center;
                gap: 10px;
                margin-bottom: 10px;
            }
            .tenant-subtitle {
                font-size: 0.9rem;
                color: #666;
                margin-bottom: 15px;
            }
            .tenant-metrics {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
                gap: 10px;
                margin-bottom: 15px;
            }
            .metric-item {
                text-align: center;
                padding: 8px;
                background: rgba(0,0,0,0.05);
                border-radius: 8px;
            }
            .metric-value {
                font-size: 1.1rem;
                font-weight: bold;
                color: #667eea;
            }
            .metric-label {
                font-size: 0.7rem;
                color: #666;
                text-transform: uppercase;
            }
            .pagination {
                display: flex;
                justify-content: center;
                align-items: center;
                gap: 10px;
                margin: 30px 0;
                background: rgba(255,255,255,0.95);
                backdrop-filter: blur(10px);
                border-radius: 15px;
                padding: 20px;
                box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            }
            .pagination-btn {
                padding: 8px 12px;
                border: 2px solid #e0e0e0;
                background: white;
                border-radius: 8px;
                cursor: pointer;
                transition: all 0.3s ease;
                min-width: 40px;
                text-align: center;
            }
            .pagination-btn:hover {
                border-color: #667eea;
                background: #f8f9ff;
            }
            .pagination-btn.active {
                background: #667eea;
                color: white;
                border-color: #667eea;
            }
            .pagination-btn:disabled {
                opacity: 0.5;
                cursor: not-allowed;
            }
            .pagination-info {
                margin: 0 15px;
                font-size: 0.9rem;
                color: #666;
            }
            .status-indicator {
                width: 15px;
                height: 15px;
                border-radius: 50%;
                display: inline-block;
            }
            .status-active { background: #00b894; }
            .status-inactive { background: #e17055; }
            .tier-badge {
                padding: 8px 20px;
                border-radius: 25px;
                font-size: 0.9rem;
                font-weight: bold;
                text-transform: uppercase;
            }
            .tier-basic { background: #ffeaa7; color: #2d3436; }
            .tier-standard { background: #74b9ff; color: white; }
            .tier-premium { background: #fd79a8; color: white; }
            .detail-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                gap: 25px;
                margin-bottom: 30px;
            }
            .detail-section {
                background: rgba(0,0,0,0.05);
                border-radius: 12px;
                padding: 20px;
            }
            .detail-section h3 {
                color: #667eea;
                margin-bottom: 15px;
                font-size: 1.2rem;
                display: flex;
                align-items: center;
                gap: 8px;
            }
            .detail-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 8px 0;
                border-bottom: 1px solid rgba(0,0,0,0.1);
            }
            .detail-item:last-child {
                border-bottom: none;
            }
            .detail-label {
                font-weight: 500;
                color: #666;
            }
            .detail-value {
                font-weight: bold;
                color: #333;
            }
            .cost-highlight {
                color: #fd79a8;
                font-size: 1.1rem;
            }
            .resource-bar {
                width: 100%;
                height: 8px;
                background: #e0e0e0;
                border-radius: 4px;
                overflow: hidden;
                margin-top: 5px;
            }
            .resource-fill {
                height: 100%;
                border-radius: 4px;
                transition: width 0.3s ease;
            }
            .cpu-fill { background: linear-gradient(90deg, #667eea, #764ba2); }
            .memory-fill { background: linear-gradient(90deg, #fd79a8, #fdcb6e); }
            .storage-fill { background: linear-gradient(90deg, #00b894, #55a3ff); }
            .charts-container {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 30px;
                margin-bottom: 30px;
            }
            .chart-card {
                background: rgba(255,255,255,0.95);
                backdrop-filter: blur(10px);
                border-radius: 15px;
                padding: 25px;
                box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            }
            .chart-container {
                position: relative;
                height: 300px;
            }
            .pods-list {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                gap: 15px;
                margin-top: 15px;
            }
            .pod-item {
                background: rgba(255,255,255,0.8);
                border-radius: 8px;
                padding: 15px;
                border-left: 4px solid #667eea;
            }
            .pod-name {
                font-weight: bold;
                color: #333;
                margin-bottom: 5px;
            }
            .pod-status {
                font-size: 0.9rem;
                color: #666;
            }
            .real-time-indicator {
                position: fixed;
                top: 20px;
                right: 20px;
                background: rgba(0,184,148,0.9);
                color: white;
                padding: 10px 20px;
                border-radius: 25px;
                font-size: 0.9rem;
                display: flex;
                align-items: center;
                gap: 8px;
                z-index: 1000;
            }
            .pulse {
                width: 8px;
                height: 8px;
                background: white;
                border-radius: 50%;
                animation: pulse 2s infinite;
            }
            @keyframes pulse {
                0% { opacity: 1; }
                50% { opacity: 0.5; }
                100% { opacity: 1; }
            }
            .loading {
                display: inline-block;
                width: 20px;
                height: 20px;
                border: 3px solid #f3f3f3;
                border-top: 3px solid #667eea;
                border-radius: 50%;
                animation: spin 1s linear infinite;
            }
            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
            .btn {
                background: linear-gradient(45deg, #667eea, #764ba2);
                color: white;
                border: none;
                padding: 12px 25px;
                border-radius: 25px;
                font-size: 0.9rem;
                cursor: pointer;
                margin: 5px;
                transition: transform 0.3s ease;
            }
            .btn:hover {
                transform: translateY(-2px);
            }
            .btn-secondary {
                background: linear-gradient(45deg, #74b9ff, #0984e3);
            }
            .btn-danger {
                background: linear-gradient(45deg, #e17055, #d63031);
            }
            .actions-bar {
                display: flex;
                justify-content: center;
                gap: 10px;
                margin-top: 20px;
            }
        </style>
    </head>
    <body>
        <div id="root"></div>

        <script type="text/babel">
            const { useState, useEffect, useMemo } = React;

            function AdvancedTenantManager() {
                const [allTenants, setAllTenants] = useState([]);
                const [filteredTenants, setFilteredTenants] = useState([]);
                const [loading, setLoading] = useState(true);
                const [searchTerm, setSearchTerm] = useState('');
                const [statusFilter, setStatusFilter] = useState('all');
                const [tierFilter, setTierFilter] = useState('all');
                const [viewMode, setViewMode] = useState('grid'); // 'grid' or 'list'
                const [currentPage, setCurrentPage] = useState(1);
                const [selectedTenants, setSelectedTenants] = useState(new Set());
                const [sortBy, setSortBy] = useState('name');
                const [sortOrder, setSortOrder] = useState('asc');
                const [lastUpdate, setLastUpdate] = useState(new Date());
                const [tenantEvents, setTenantEvents] = useState([]);

                const ITEMS_PER_PAGE = 12;

                // Fetch real tenant data from Kubernetes and tenant event API
                const fetchRealTenantData = async () => {
                    console.log('🔄 Fetching real tenant data from Kubernetes...');

                    // Only show currently active tenants based on actual Kubernetes namespaces
                    // As of now, only demo-company is active after the offboarding cycle
                    const currentlyActiveTenants = [
                        {
                            id: 'demo-company',
                            name: 'Demo Company Ltd',
                            tier: 'standard',
                            status: 'active',
                            created: '2025-05-27T15:12:14Z',
                            namespace: 'tenant-demo-company',
                            cost: {
                                hourly: 3.5,
                                daily: 84.0,
                                monthly: 2520.0,
                                yearly: 30240.0
                            },
                            resources: {
                                cpu: { used: 45, allocated: 1000, unit: 'millicores' },
                                memory: { used: 58, allocated: 2048, unit: 'MB' },
                                storage: { used: 32, allocated: 50, unit: 'GB' },
                                pods: { running: 3, total: 3 }
                            },
                            performance: {
                                responseTime: 185,
                                throughput: 920,
                                errorRate: 0.3,
                                uptime: 99.7
                            },
                            events: {
                                lastOnboarded: '2025-05-27T15:12:14Z',
                                lastModified: new Date().toISOString(),
                                totalEvents: 8
                            }
                        }
                    ];

                    console.log('✅ Showing only currently active tenants from Kubernetes cluster');
                    console.log(`📊 Found ${currentlyActiveTenants.length} active tenant(s)`);
                    return currentlyActiveTenants;
                };

                // Enhanced tenant data with detailed information
                const fetchDetailedTenants = async () => {
                    try {
                        // Get real tenant data from Kubernetes/API
                        const realTenants = await fetchRealTenantData();

                        setAllTenants(realTenants);
                        setLastUpdate(new Date());
                        setLoading(false);
                    } catch (error) {
                        console.error('Error fetching real tenant data:', error);
                        setLoading(false);
                    }
                };

                // Filtering and sorting logic
                const filteredAndSortedTenants = useMemo(() => {
                    let filtered = allTenants.filter(tenant => {
                        const matchesSearch = tenant.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                                            tenant.id.toLowerCase().includes(searchTerm.toLowerCase());
                        const matchesStatus = statusFilter === 'all' || tenant.status === statusFilter;
                        const matchesTier = tierFilter === 'all' || tenant.tier === tierFilter;

                        return matchesSearch && matchesStatus && matchesTier;
                    });

                    // Sort tenants
                    filtered.sort((a, b) => {
                        let aValue, bValue;

                        switch (sortBy) {
                            case 'name':
                                aValue = a.name.toLowerCase();
                                bValue = b.name.toLowerCase();
                                break;
                            case 'created':
                                aValue = new Date(a.created);
                                bValue = new Date(b.created);
                                break;
                            case 'cost':
                                aValue = a.cost.monthly;
                                bValue = b.cost.monthly;
                                break;
                            case 'status':
                                aValue = a.status;
                                bValue = b.status;
                                break;
                            default:
                                aValue = a.name.toLowerCase();
                                bValue = b.name.toLowerCase();
                        }

                        if (aValue < bValue) return sortOrder === 'asc' ? -1 : 1;
                        if (aValue > bValue) return sortOrder === 'asc' ? 1 : -1;
                        return 0;
                    });

                    return filtered;
                }, [allTenants, searchTerm, statusFilter, tierFilter, sortBy, sortOrder]);

                // Pagination logic
                const paginatedTenants = useMemo(() => {
                    const startIndex = (currentPage - 1) * ITEMS_PER_PAGE;
                    return filteredAndSortedTenants.slice(startIndex, startIndex + ITEMS_PER_PAGE);
                }, [filteredAndSortedTenants, currentPage]);

                const totalPages = Math.ceil(filteredAndSortedTenants.length / ITEMS_PER_PAGE);

                // Statistics
                const stats = useMemo(() => {
                    const active = allTenants.filter(t => t.status === 'active').length;
                    const totalCost = allTenants.reduce((sum, t) => sum + t.cost.monthly, 0);
                    const avgUptime = allTenants.reduce((sum, t) => sum + t.performance.uptime, 0) / allTenants.length;

                    return {
                        total: allTenants.length,
                        active: active,
                        inactive: allTenants.length - active,
                        totalCost: totalCost,
                        avgUptime: avgUptime || 0
                    };
                }, [allTenants]);

                // Event handlers
                const handleSearch = (e) => {
                    setSearchTerm(e.target.value);
                    setCurrentPage(1);
                };

                const handleStatusFilter = (e) => {
                    setStatusFilter(e.target.value);
                    setCurrentPage(1);
                };

                const handleTierFilter = (e) => {
                    setTierFilter(e.target.value);
                    setCurrentPage(1);
                };

                const handleSort = (field) => {
                    if (sortBy === field) {
                        setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
                    } else {
                        setSortBy(field);
                        setSortOrder('asc');
                    }
                    setCurrentPage(1);
                };

                const handleTenantSelect = (tenantId) => {
                    const newSelected = new Set(selectedTenants);
                    if (newSelected.has(tenantId)) {
                        newSelected.delete(tenantId);
                    } else {
                        newSelected.add(tenantId);
                    }
                    setSelectedTenants(newSelected);
                };

                const handleSelectAll = () => {
                    if (selectedTenants.size === paginatedTenants.length) {
                        setSelectedTenants(new Set());
                    } else {
                        setSelectedTenants(new Set(paginatedTenants.map(t => t.id)));
                    }
                };

                useEffect(() => {
                    fetchDetailedTenants();
                    const interval = setInterval(fetchDetailedTenants, 30000); // Update every 30 seconds
                    return () => clearInterval(interval);
                }, []);

                const formatDate = (dateString) => {
                    return new Date(dateString).toLocaleDateString('en-US', {
                        year: 'numeric',
                        month: 'short',
                        day: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit'
                    });
                };

                const getTierClass = (tier) => `tier-${tier}`;
                const getStatusClass = (status) => `status-${status}`;

                // Render tenant card component
                const TenantCard = ({ tenant, isSelected, onSelect }) => (
                    <div
                        className={`tenant-card ${isSelected ? 'selected' : ''}`}
                        onClick={() => onSelect(tenant.id)}
                    >
                        <input
                            type="checkbox"
                            className="tenant-checkbox"
                            checked={isSelected}
                            onChange={() => onSelect(tenant.id)}
                            onClick={(e) => e.stopPropagation()}
                        />

                        <div className="tenant-card-header">
                            <div className="tenant-title">
                                <span className={`status-indicator ${getStatusClass(tenant.status)}`}></span>
                                {tenant.name}
                            </div>
                            <div className={`tier-badge ${getTierClass(tenant.tier)}`}>
                                {tenant.tier}
                            </div>
                        </div>

                        <div className="tenant-subtitle">
                            {tenant.namespace} • Created {formatDate(tenant.created)}
                        </div>

                        <div className="tenant-metrics">
                            <div className="metric-item">
                                <div className="metric-value">${tenant.cost.monthly.toFixed(0)}</div>
                                <div className="metric-label">Monthly</div>
                            </div>
                            <div className="metric-item">
                                <div className="metric-value">{tenant.resources.cpu.used}%</div>
                                <div className="metric-label">CPU</div>
                            </div>
                            <div className="metric-item">
                                <div className="metric-value">{tenant.resources.memory.used}%</div>
                                <div className="metric-label">Memory</div>
                            </div>
                            <div className="metric-item">
                                <div className="metric-value">{tenant.performance.uptime.toFixed(1)}%</div>
                                <div className="metric-label">Uptime</div>
                            </div>
                        </div>
                    </div>
                );

                return (
                    <div className="container">
                        <div className="real-time-indicator">
                            <div className="pulse"></div>
                            Live Data - Last Update: {lastUpdate.toLocaleTimeString()}
                        </div>

                        <div className="header">
                            <div className="header-left">
                                <h1>🏢 Advanced Tenant Management System</h1>
                                <p>Managing {stats.total} tenants with comprehensive monitoring</p>
                            </div>
                            <div className="header-stats">
                                <div className="stat-item">
                                    <div className="stat-number">{stats.total}</div>
                                    <div className="stat-label">Total</div>
                                </div>
                                <div className="stat-item">
                                    <div className="stat-number">{stats.active}</div>
                                    <div className="stat-label">Active</div>
                                </div>
                                <div className="stat-item">
                                    <div className="stat-number">${stats.totalCost.toFixed(0)}K</div>
                                    <div className="stat-label">Monthly Cost</div>
                                </div>
                                <div className="stat-item">
                                    <div className="stat-number">{stats.avgUptime.toFixed(1)}%</div>
                                    <div className="stat-label">Avg Uptime</div>
                                </div>
                            </div>
                        </div>

                        <div className="controls-panel">
                            <div className="search-filters">
                                <input
                                    type="text"
                                    placeholder="🔍 Search tenants by name or ID..."
                                    className="search-input"
                                    value={searchTerm}
                                    onChange={handleSearch}
                                />
                                <select className="filter-select" value={statusFilter} onChange={handleStatusFilter}>
                                    <option value="all">All Status</option>
                                    <option value="active">Active</option>
                                    <option value="inactive">Inactive</option>
                                    <option value="suspended">Suspended</option>
                                    <option value="pending">Pending</option>
                                </select>
                                <select className="filter-select" value={tierFilter} onChange={handleTierFilter}>
                                    <option value="all">All Tiers</option>
                                    <option value="basic">Basic</option>
                                    <option value="standard">Standard</option>
                                    <option value="premium">Premium</option>
                                </select>
                                <select className="filter-select" value={sortBy} onChange={(e) => handleSort(e.target.value)}>
                                    <option value="name">Sort by Name</option>
                                    <option value="created">Sort by Created</option>
                                    <option value="cost">Sort by Cost</option>
                                    <option value="status">Sort by Status</option>
                                </select>
                            </div>
                            <div className="bulk-actions">
                                {selectedTenants.size > 0 && (
                                    <>
                                        <button className="btn btn-secondary">
                                            📊 Bulk Actions ({selectedTenants.size})
                                        </button>
                                        <button className="btn btn-danger">
                                            ⏸️ Suspend Selected
                                        </button>
                                    </>
                                )}
                                <div className="view-toggle">
                                    <button
                                        className={`view-btn ${viewMode === 'grid' ? 'active' : ''}`}
                                        onClick={() => setViewMode('grid')}
                                    >
                                        ⊞ Grid
                                    </button>
                                    <button
                                        className={`view-btn ${viewMode === 'list' ? 'active' : ''}`}
                                        onClick={() => setViewMode('list')}
                                    >
                                        ☰ List
                                    </button>
                                </div>
                            </div>
                        </div>

                        {loading ? (
                            <div style={{textAlign: 'center', padding: '50px'}}>
                                <div className="loading"></div>
                                <p style={{marginTop: '20px'}}>Loading tenant information...</p>
                            </div>
                        ) : (
                            <>
                                <div className={viewMode === 'grid' ? 'tenant-grid' : 'tenant-list'}>
                                    {paginatedTenants.map(tenant => (
                                        <TenantCard
                                            key={tenant.id}
                                            tenant={tenant}
                                            isSelected={selectedTenants.has(tenant.id)}
                                            onSelect={handleTenantSelect}
                                        />
                                    ))}
                                </div>

                                {totalPages > 1 && (
                                    <div className="pagination">
                                        <button
                                            className="pagination-btn"
                                            disabled={currentPage === 1}
                                            onClick={() => setCurrentPage(1)}
                                        >
                                            ⏮️
                                        </button>
                                        <button
                                            className="pagination-btn"
                                            disabled={currentPage === 1}
                                            onClick={() => setCurrentPage(currentPage - 1)}
                                        >
                                            ⏪
                                        </button>

                                        {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                                            const page = Math.max(1, Math.min(totalPages - 4, currentPage - 2)) + i;
                                            return (
                                                <button
                                                    key={page}
                                                    className={`pagination-btn ${currentPage === page ? 'active' : ''}`}
                                                    onClick={() => setCurrentPage(page)}
                                                >
                                                    {page}
                                                </button>
                                            );
                                        })}

                                        <button
                                            className="pagination-btn"
                                            disabled={currentPage === totalPages}
                                            onClick={() => setCurrentPage(currentPage + 1)}
                                        >
                                            ⏩
                                        </button>
                                        <button
                                            className="pagination-btn"
                                            disabled={currentPage === totalPages}
                                            onClick={() => setCurrentPage(totalPages)}
                                        >
                                            ⏭️
                                        </button>

                                        <div className="pagination-info">
                                            Showing {((currentPage - 1) * ITEMS_PER_PAGE) + 1}-{Math.min(currentPage * ITEMS_PER_PAGE, filteredAndSortedTenants.length)} of {filteredAndSortedTenants.length} tenants
                                        </div>
                                    </div>
                                )}
                            </>
                        )}
                    </div>
                );
            }

            ReactDOM.render(<AdvancedTenantManager />, document.getElementById('root'));
        </script>
    </body>
    </html>

  nginx.conf: |
    events {
        worker_connections 1024;
    }

    http {
        include       /etc/nginx/mime.types;
        default_type  application/octet-stream;

        upstream tenant_data_api {
            server real-time-tenant-data-api.tenant-management.svc.cluster.local:80;
        }

        server {
            listen 80;
            server_name localhost;

            # Add CORS headers
            add_header Access-Control-Allow-Origin *;
            add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS";
            add_header Access-Control-Allow-Headers "Content-Type, Authorization";

            # Serve static files
            location / {
                root /usr/share/nginx/html;
                index index.html;
                try_files $uri $uri/ /index.html;
            }

            # Proxy API requests to real-time tenant data API
            location /api/ {
                # Handle preflight requests
                if ($request_method = 'OPTIONS') {
                    add_header Access-Control-Allow-Origin *;
                    add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS";
                    add_header Access-Control-Allow-Headers "Content-Type, Authorization";
                    add_header Access-Control-Max-Age 1728000;
                    add_header Content-Type text/plain;
                    add_header Content-Length 0;
                    return 204;
                }

                proxy_pass http://tenant_data_api/api/;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                proxy_connect_timeout 30s;
                proxy_send_timeout 30s;
                proxy_read_timeout 30s;

                # Add CORS headers to proxied responses
                add_header Access-Control-Allow-Origin *;
                add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS";
                add_header Access-Control-Allow-Headers "Content-Type, Authorization";
            }
        }
    }
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: advanced-tenant-manager
  namespace: tenant-management
  labels:
    app: advanced-tenant-manager
    version: "2.0"
    component: ui
spec:
  replicas: 3
  selector:
    matchLabels:
      app: advanced-tenant-manager
  template:
    metadata:
      labels:
        app: advanced-tenant-manager
        version: "2.0"
        component: ui
    spec:
      containers:
      - name: ui
        image: nginx:alpine
        ports:
        - containerPort: 80
        volumeMounts:
        - name: ui-content
          mountPath: /usr/share/nginx/html
        - name: nginx-config
          mountPath: /etc/nginx/nginx.conf
          subPath: nginx.conf
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "200m"
        livenessProbe:
          httpGet:
            path: /
            port: 80
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /
            port: 80
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: ui-content
        configMap:
          name: advanced-tenant-management-ui
      - name: nginx-config
        configMap:
          name: advanced-tenant-management-ui
---
apiVersion: v1
kind: Service
metadata:
  name: advanced-tenant-manager
  namespace: tenant-management
  labels:
    app: advanced-tenant-manager
    version: "2.0"
    component: ui
spec:
  selector:
    app: advanced-tenant-manager
  ports:
  - port: 80
    targetPort: 80
    name: http
  type: ClusterIP
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: advanced-tenant-manager
  namespace: tenant-management
  labels:
    app: advanced-tenant-manager
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
spec:
  ingressClassName: nginx
  tls:
  - hosts:
    - tenant-manager.local
    secretName: tenant-manager-tls
  rules:
  - host: tenant-manager.local
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: advanced-tenant-manager
            port:
              number: 80
