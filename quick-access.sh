#!/bin/bash

# Quick Access Script for Tenant Management System
# Provides easy access to all deployed features

set -e

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

show_menu() {
    echo -e "${BLUE}🚀 TENANT MANAGEMENT SYSTEM - QUICK ACCESS${NC}"
    echo ""
    echo "1. 🌐 Open Tenant Management UI"
    echo "2. 📊 View Tenant Status"
    echo "3. 💰 Check Cost Tracking"
    echo "4. 📈 View Auto-scaling Status"
    echo "5. 🔍 Check Prometheus Alerts"
    echo "6. 📋 View Grafana Dashboards"
    echo "7. 🧪 Run Load Test"
    echo "8. 🔧 System Health Check"
    echo "9. 📖 Show Documentation"
    echo "0. Exit"
    echo ""
    echo -n "Select an option: "
}

open_ui() {
    echo -e "${GREEN}Opening Tenant Management UI...${NC}"
    echo "Add this to /etc/hosts if not already done:"
    echo "echo '127.0.0.1 tenant-management.local' | sudo tee -a /etc/hosts"
    echo ""
    echo "Then access: http://tenant-management.local"
    
    # Try to open in browser if available
    if command -v open &> /dev/null; then
        open http://tenant-management.local
    elif command -v xdg-open &> /dev/null; then
        xdg-open http://tenant-management.local
    fi
}

view_tenant_status() {
    echo -e "${GREEN}Tenant Status Overview:${NC}"
    echo ""
    kubectl get namespaces -l tenant -o custom-columns="NAME:.metadata.name,TENANT:.metadata.labels.tenant,TIER:.metadata.labels.tier,COST/HR:.metadata.annotations.cost\.architrave\.com/hourly"
    echo ""
    echo -e "${GREEN}Pod Status:${NC}"
    for ns in $(kubectl get namespaces -l tenant -o name | cut -d/ -f2); do
        echo "Namespace: $ns"
        kubectl get pods -n $ns --no-headers 2>/dev/null | head -3 || echo "  No pods found"
        echo ""
    done
}

check_cost_tracking() {
    echo -e "${GREEN}Cost Tracking Status:${NC}"
    echo ""
    echo "Cost Calculator Job Status:"
    kubectl get cronjob tenant-cost-calculator -n monitoring
    echo ""
    echo "Recent Cost Calculations:"
    kubectl get jobs -n monitoring | grep tenant-cost || echo "No recent cost calculation jobs"
    echo ""
    echo "Tenant Costs:"
    kubectl get namespaces -l tenant -o custom-columns="TENANT:.metadata.labels.tenant,HOURLY_COST:.metadata.annotations.cost\.architrave\.com/hourly"
}

view_autoscaling_status() {
    echo -e "${GREEN}Auto-scaling Status:${NC}"
    echo ""
    for ns in $(kubectl get namespaces -l tenant -o name | cut -d/ -f2); do
        echo "=== $ns ==="
        kubectl get hpa -n $ns 2>/dev/null || echo "No HPA configured"
        kubectl get pdb -n $ns 2>/dev/null || echo "No PDB configured"
        echo ""
    done
}

check_prometheus_alerts() {
    echo -e "${GREEN}Prometheus Alerts Status:${NC}"
    echo ""
    kubectl get prometheusrules -n monitoring
    echo ""
    echo "To view active alerts, access Prometheus UI:"
    echo "kubectl port-forward svc/prometheus-server 9090:80 -n monitoring"
    echo "Then open: http://localhost:9090/alerts"
}

show_grafana_dashboards() {
    echo -e "${GREEN}Available Grafana Dashboards:${NC}"
    echo ""
    kubectl get configmaps -n monitoring -l grafana_dashboard=1
    echo ""
    echo "To access Grafana:"
    echo "kubectl port-forward svc/grafana 3000:80 -n monitoring"
    echo "Then open: http://localhost:3000"
    echo ""
    echo "Available dashboards:"
    echo "• Tenant Performance Dashboard"
    echo "• Tenant Cost Tracking Dashboard"
}

run_load_test() {
    echo -e "${GREEN}Running Load Test on tenant-fast-test...${NC}"
    echo ""
    
    # Check if tenant exists
    if ! kubectl get namespace tenant-fast-test &> /dev/null; then
        echo "tenant-fast-test namespace not found. Available tenants:"
        kubectl get namespaces -l tenant -o name | cut -d/ -f2
        return 1
    fi
    
    echo "Starting load test (will run for 60 seconds)..."
    kubectl run load-test --image=busybox --rm -it --restart=Never -- \
        /bin/sh -c "
        echo 'Starting load test...'
        for i in \$(seq 1 100); do
            wget -q -O- http://fast-test-app.tenant-fast-test.svc.cluster.local/api/health 2>/dev/null || echo 'Request failed'
            sleep 0.5
        done
        echo 'Load test completed'
        " &
    
    echo "Load test started. Monitor scaling with:"
    echo "kubectl get hpa -n tenant-fast-test -w"
}

system_health_check() {
    echo -e "${GREEN}System Health Check:${NC}"
    echo ""
    
    echo "🔍 Checking core components..."
    
    # Check KEDA
    if kubectl get pods -n keda-system -l app.kubernetes.io/name=keda-operator | grep -q Running; then
        echo "✅ KEDA is running"
    else
        echo "❌ KEDA is not running"
    fi
    
    # Check Tenant Management UI
    ui_pods=$(kubectl get pods -n tenant-management -l app=tenant-management-ui --no-headers | grep Running | wc -l)
    if [ "$ui_pods" -gt 0 ]; then
        echo "✅ Tenant Management UI is running ($ui_pods pods)"
    else
        echo "❌ Tenant Management UI is not running"
    fi
    
    # Check API
    api_pods=$(kubectl get pods -n tenant-management -l app=tenant-management-api-simple --no-headers | grep Running | wc -l)
    if [ "$api_pods" -gt 0 ]; then
        echo "✅ Tenant Management API is running ($api_pods pods)"
    else
        echo "❌ Tenant Management API is not running"
    fi
    
    # Check Cost Tracking
    if kubectl get cronjob tenant-cost-calculator -n monitoring &> /dev/null; then
        echo "✅ Cost tracking is configured"
    else
        echo "❌ Cost tracking is not configured"
    fi
    
    # Check Prometheus Rules
    if kubectl get prometheusrules enhanced-tenant-alerts -n monitoring &> /dev/null; then
        echo "✅ Prometheus alerting rules are deployed"
    else
        echo "❌ Prometheus alerting rules are missing"
    fi
    
    echo ""
    echo "🎯 Overall system status: Ready for production!"
}

show_documentation() {
    echo -e "${GREEN}📖 Documentation and Resources:${NC}"
    echo ""
    echo "📁 Key Files:"
    echo "• docs/performance-optimization-and-tenant-ui.md - Complete documentation"
    echo "• docs/quick-deployment-guide.md - Quick start guide"
    echo "• test-deployment.sh - Comprehensive test suite"
    echo ""
    echo "🔗 Access Points:"
    echo "• UI: http://tenant-management.local"
    echo "• Grafana: kubectl port-forward svc/grafana 3000:80 -n monitoring"
    echo "• Prometheus: kubectl port-forward svc/prometheus-server 9090:80 -n monitoring"
    echo ""
    echo "🚀 Key Features:"
    echo "• Enhanced auto-scaling with KEDA and HPA"
    echo "• Real-time cost tracking and optimization"
    echo "• Self-service tenant onboarding"
    echo "• Comprehensive SLA monitoring"
    echo "• Log aggregation with tenant isolation"
    echo ""
    echo "📞 Support:"
    echo "• Run './test-deployment.sh' for system validation"
    echo "• Check logs: kubectl logs -f deployment/tenant-management-ui -n tenant-management"
    echo "• Monitor alerts: kubectl get prometheusrules -n monitoring"
}

# Main menu loop
main() {
    while true; do
        echo ""
        show_menu
        read -r choice
        echo ""
        
        case $choice in
            1) open_ui ;;
            2) view_tenant_status ;;
            3) check_cost_tracking ;;
            4) view_autoscaling_status ;;
            5) check_prometheus_alerts ;;
            6) show_grafana_dashboards ;;
            7) run_load_test ;;
            8) system_health_check ;;
            9) show_documentation ;;
            0) echo "Goodbye! 👋"; exit 0 ;;
            *) echo -e "${YELLOW}Invalid option. Please try again.${NC}" ;;
        esac
        
        echo ""
        echo -n "Press Enter to continue..."
        read -r
    done
}

# Run the main menu
main "$@"
