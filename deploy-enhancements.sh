#!/bin/bash

# Exit on error
set -e

echo "🚀 Deploying enhanced components..."

# Create necessary namespaces
kubectl create namespace monitoring --dry-run=client -o yaml | kubectl apply -f -
kubectl create namespace cost-management --dry-run=client -o yaml | kubectl apply -f -

# Apply enhanced monitoring
echo "📊 Applying enhanced monitoring..."
kubectl apply -f monitoring/enhanced-metrics.yaml

# Apply enhanced security scanning
echo "🔒 Applying enhanced security scanning..."
kubectl apply -f security/enhanced-scanning.yaml

# Apply enhanced cost optimization
echo "💰 Applying enhanced cost optimization..."
kubectl apply -f cost-management/enhanced-optimization.yaml

# Wait for components to be ready
echo "⏳ Waiting for components to be ready..."
kubectl wait --for=condition=ready pod -l app=metrics-exporter -n monitoring --timeout=300s
kubectl wait --for=condition=ready pod -l app=cost-optimizer -n kube-system --timeout=300s

# Verify installations
echo "✅ Verifying installations..."

# Check monitoring
kubectl get pods -n monitoring

# Check security scanning
kubectl get cronjob -n kube-system enhanced-security-scan

# Check cost optimization
kubectl get cronjob -n kube-system cost-optimizer

echo "🎉 Enhanced components deployment completed!"
echo "Next steps:"
echo "1. Monitor the enhanced metrics in Grafana"
echo "2. Review security scan results"
echo "3. Check cost optimization recommendations"
echo "4. Verify alerting is working" 