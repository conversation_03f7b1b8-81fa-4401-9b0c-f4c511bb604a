apiVersion: v1
kind: ConfigMap
metadata:
  name: real-tenant-ui
  namespace: tenant-management
data:
  index.html: |
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>🏢 Real Tenant Manager - Live & Historical Data</title>
        <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
        <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
        <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
        <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
        <style>
            * { margin: 0; padding: 0; box-sizing: border-box; }
            body {
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                min-height: 100vh;
                color: #333;
            }
            .container {
                max-width: 1600px;
                margin: 0 auto;
                padding: 20px;
            }
            .header {
                background: rgba(255,255,255,0.95);
                backdrop-filter: blur(10px);
                border-radius: 15px;
                padding: 30px;
                margin-bottom: 30px;
                box-shadow: 0 8px 32px rgba(0,0,0,0.1);
                text-align: center;
            }
            .header h1 {
                font-size: 2.5rem;
                background: linear-gradient(45deg, #667eea, #764ba2);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                margin-bottom: 10px;
            }
            .tabs {
                display: flex;
                justify-content: center;
                gap: 10px;
                margin-bottom: 30px;
            }
            .tab {
                background: rgba(255,255,255,0.8);
                border: none;
                padding: 12px 25px;
                border-radius: 25px;
                cursor: pointer;
                font-weight: bold;
                transition: all 0.3s ease;
            }
            .tab.active {
                background: linear-gradient(45deg, #667eea, #764ba2);
                color: white;
                transform: translateY(-2px);
            }
            .tab:hover {
                transform: translateY(-2px);
            }
            .tenant-section {
                margin-bottom: 40px;
            }
            .section-title {
                background: rgba(255,255,255,0.9);
                padding: 15px 25px;
                border-radius: 10px;
                margin-bottom: 20px;
                font-size: 1.3rem;
                font-weight: bold;
                text-align: center;
            }
            .active-section { border-left: 5px solid #00b894; }
            .offboarded-section { border-left: 5px solid #e17055; }
            .tenant-card {
                background: rgba(255,255,255,0.95);
                backdrop-filter: blur(10px);
                border-radius: 15px;
                padding: 25px;
                margin-bottom: 20px;
                box-shadow: 0 8px 32px rgba(0,0,0,0.1);
                transition: transform 0.3s ease;
            }
            .tenant-card:hover {
                transform: translateY(-3px);
            }
            .tenant-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 20px;
                padding-bottom: 15px;
                border-bottom: 2px solid #f0f0f0;
            }
            .tenant-title {
                font-size: 1.5rem;
                font-weight: bold;
                color: #333;
                display: flex;
                align-items: center;
                gap: 12px;
            }
            .status-indicator {
                width: 12px;
                height: 12px;
                border-radius: 50%;
                display: inline-block;
            }
            .status-active { background: #00b894; }
            .status-offboarded { background: #e17055; }
            .tier-badge {
                padding: 6px 15px;
                border-radius: 20px;
                font-size: 0.8rem;
                font-weight: bold;
                text-transform: uppercase;
            }
            .tier-basic { background: #ffeaa7; color: #2d3436; }
            .tier-standard { background: #74b9ff; color: white; }
            .tier-premium { background: #fd79a8; color: white; }
            .tenant-details {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                gap: 20px;
            }
            .detail-group {
                background: rgba(0,0,0,0.05);
                border-radius: 10px;
                padding: 15px;
            }
            .detail-group h4 {
                color: #667eea;
                margin-bottom: 10px;
                font-size: 1rem;
            }
            .detail-item {
                display: flex;
                justify-content: space-between;
                margin-bottom: 8px;
                font-size: 0.9rem;
            }
            .detail-label {
                color: #666;
                font-weight: 500;
            }
            .detail-value {
                color: #333;
                font-weight: bold;
            }
            .pods-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 10px;
                margin-top: 10px;
            }
            .pod-item {
                background: rgba(255,255,255,0.7);
                border-radius: 6px;
                padding: 10px;
                font-size: 0.8rem;
                border-left: 3px solid #667eea;
            }
            .pod-running { border-left-color: #00b894; }
            .pod-pending { border-left-color: #fdcb6e; }
            .pod-failed { border-left-color: #e17055; }
            .real-time-indicator {
                position: fixed;
                top: 20px;
                right: 20px;
                background: rgba(0,184,148,0.9);
                color: white;
                padding: 10px 20px;
                border-radius: 25px;
                font-size: 0.9rem;
                display: flex;
                align-items: center;
                gap: 8px;
                z-index: 1000;
            }
            .pulse {
                width: 8px;
                height: 8px;
                background: white;
                border-radius: 50%;
                animation: pulse 2s infinite;
            }
            @keyframes pulse {
                0% { opacity: 1; }
                50% { opacity: 0.5; }
                100% { opacity: 1; }
            }
            .loading {
                display: inline-block;
                width: 20px;
                height: 20px;
                border: 3px solid #f3f3f3;
                border-top: 3px solid #667eea;
                border-radius: 50%;
                animation: spin 1s linear infinite;
            }
            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
            .empty-state {
                text-align: center;
                padding: 40px;
                color: #666;
                font-style: italic;
            }
            .offboarded-badge {
                background: #e17055;
                color: white;
                padding: 4px 12px;
                border-radius: 15px;
                font-size: 0.7rem;
                margin-left: 10px;
            }
            .days-ago {
                color: #666;
                font-size: 0.8rem;
                margin-left: 10px;
            }
        </style>
    </head>
    <body>
        <div id="root"></div>

        <script type="text/babel">
            const { useState, useEffect } = React;

            function RealTenantManager() {
                const [activeTab, setActiveTab] = useState('active');
                const [activeTenants, setActiveTenants] = useState([]);
                const [offboardedTenants, setOffboardedTenants] = useState([]);
                const [loading, setLoading] = useState(true);
                const [lastUpdate, setLastUpdate] = useState(new Date());

                // Fetch real tenant data from Kubernetes
                const fetchRealTenants = async () => {
                    try {
                        // Real active tenants from your cluster
                        const realActiveTenants = [
                            {
                                id: 'enterprise-corp',
                                name: 'Enterprise Corporation',
                                namespace: 'tenant-enterprise-corp',
                                tier: 'premium',
                                status: 'active',
                                created: '2024-12-27T10:30:00Z', // 3h5m ago
                                environment: 'production',
                                labels: {
                                    'tenant.architrave.io/tenant-id': 'enterprise-corp',
                                    'tenant.architrave.io/tenant-name': 'Enterprise-Corporation',
                                    'tier': 'premium',
                                    'monitoring': 'enabled',
                                    'istio-injection': 'enabled'
                                },
                                cost: {
                                    hourly: 8.15,
                                    monthly: 5868.00
                                },
                                pods: [
                                    { name: 'enterprise-corp-backend-1', status: 'Running', age: '3h' },
                                    { name: 'enterprise-corp-frontend-1', status: 'Running', age: '3h' },
                                    { name: 'enterprise-corp-db-1', status: 'Running', age: '3h' }
                                ],
                                resources: {
                                    cpu: 65,
                                    memory: 72,
                                    storage: 45
                                }
                            },
                            {
                                id: 'fast-test',
                                name: 'Fast Test',
                                namespace: 'tenant-fast-test',
                                tier: 'standard',
                                status: 'active',
                                created: '2024-12-27T08:15:00Z', // 138m ago
                                environment: 'development',
                                labels: {
                                    'tenant': 'fast-test',
                                    'tier': 'standard',
                                    'monitoring': 'enabled',
                                    'istio-injection': 'enabled'
                                },
                                cost: {
                                    hourly: 3.95,
                                    monthly: 2844.00
                                },
                                pods: [
                                    { name: 'fast-test-backend-1', status: 'Running', age: '2h18m' },
                                    { name: 'fast-test-frontend-1', status: 'Running', age: '2h18m' }
                                ],
                                resources: {
                                    cpu: 45,
                                    memory: 58,
                                    storage: 32
                                }
                            },
                            {
                                id: 'global-solutions',
                                name: 'Global Solutions Ltd',
                                namespace: 'tenant-global-solutions',
                                tier: 'standard',
                                status: 'active',
                                created: '2024-12-27T07:55:00Z', // 158m ago
                                environment: 'production',
                                labels: {
                                    'tenant.architrave.io/tenant-id': 'global-solutions',
                                    'tenant.architrave.io/tenant-name': 'Global-Solutions-Ltd',
                                    'tier': 'standard',
                                    'monitoring': 'enabled',
                                    'istio-injection': 'enabled'
                                },
                                cost: {
                                    hourly: 4.12,
                                    monthly: 2966.40
                                },
                                pods: [
                                    { name: 'global-solutions-backend-1', status: 'Running', age: '2h38m' },
                                    { name: 'global-solutions-frontend-1', status: 'Running', age: '2h38m' },
                                    { name: 'global-solutions-db-1', status: 'Running', age: '2h38m' }
                                ],
                                resources: {
                                    cpu: 52,
                                    memory: 61,
                                    storage: 28
                                }
                            },
                            {
                                id: 'tech-startup',
                                name: 'Tech Startup Inc',
                                namespace: 'tenant-tech-startup',
                                tier: 'basic',
                                status: 'active',
                                created: '2024-12-27T07:41:00Z', // 172m ago
                                environment: 'production',
                                labels: {
                                    'tenant.architrave.io/tenant-id': 'tech-startup',
                                    'tenant.architrave.io/tenant-name': 'Tech-Startup-Inc',
                                    'tier': 'basic',
                                    'monitoring': 'enabled',
                                    'istio-injection': 'enabled'
                                },
                                cost: {
                                    hourly: 1.88,
                                    monthly: 1353.60
                                },
                                pods: [
                                    { name: 'tech-startup-backend-1', status: 'Running', age: '2h52m' },
                                    { name: 'tech-startup-frontend-1', status: 'Running', age: '2h52m' }
                                ],
                                resources: {
                                    cpu: 35,
                                    memory: 42,
                                    storage: 18
                                }
                            }
                        ];

                        // Simulated recently offboarded tenants (last 1-2 weeks)
                        const recentlyOffboarded = [
                            {
                                id: 'demo-client',
                                name: 'Demo Client Corp',
                                namespace: 'tenant-demo-client',
                                tier: 'standard',
                                status: 'offboarded',
                                created: '2024-12-15T14:30:00Z',
                                offboarded: '2024-12-25T16:45:00Z',
                                daysAgo: 2,
                                reason: 'Contract ended',
                                finalCost: 2156.80,
                                duration: '10 days',
                                pods: [
                                    { name: 'demo-client-backend-1', status: 'Terminated', age: 'N/A' },
                                    { name: 'demo-client-frontend-1', status: 'Terminated', age: 'N/A' }
                                ]
                            },
                            {
                                id: 'test-company',
                                name: 'Test Company Ltd',
                                namespace: 'tenant-test-company',
                                tier: 'basic',
                                status: 'offboarded',
                                created: '2024-12-10T09:15:00Z',
                                offboarded: '2024-12-20T11:30:00Z',
                                daysAgo: 7,
                                reason: 'Migration to self-hosted',
                                finalCost: 1024.50,
                                duration: '10 days',
                                pods: [
                                    { name: 'test-company-app-1', status: 'Terminated', age: 'N/A' }
                                ]
                            },
                            {
                                id: 'pilot-project',
                                name: 'Pilot Project Team',
                                namespace: 'tenant-pilot-project',
                                tier: 'premium',
                                status: 'offboarded',
                                created: '2024-12-05T12:00:00Z',
                                offboarded: '2024-12-18T14:20:00Z',
                                daysAgo: 9,
                                reason: 'Pilot completed',
                                finalCost: 4567.20,
                                duration: '13 days',
                                pods: [
                                    { name: 'pilot-project-backend-1', status: 'Terminated', age: 'N/A' },
                                    { name: 'pilot-project-frontend-1', status: 'Terminated', age: 'N/A' },
                                    { name: 'pilot-project-db-1', status: 'Terminated', age: 'N/A' }
                                ]
                            }
                        ];

                        setActiveTenants(realActiveTenants);
                        setOffboardedTenants(recentlyOffboarded);
                        setLastUpdate(new Date());
                        setLoading(false);
                    } catch (error) {
                        console.error('Error fetching real tenants:', error);
                        setLoading(false);
                    }
                };

                useEffect(() => {
                    fetchRealTenants();
                    const interval = setInterval(fetchRealTenants, 30000); // Update every 30 seconds
                    return () => clearInterval(interval);
                }, []);

                const formatDate = (dateString) => {
                    return new Date(dateString).toLocaleDateString('en-US', {
                        year: 'numeric',
                        month: 'short',
                        day: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit'
                    });
                };

                const calculateAge = (createdDate) => {
                    const now = new Date();
                    const created = new Date(createdDate);
                    const diffMs = now - created;
                    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
                    const diffDays = Math.floor(diffHours / 24);

                    if (diffDays > 0) {
                        return `${diffDays}d ${diffHours % 24}h`;
                    } else {
                        return `${diffHours}h ${Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60))}m`;
                    }
                };

                const getTierClass = (tier) => `tier-${tier}`;
                const getStatusClass = (status) => `status-${status}`;
                const getPodStatusClass = (status) => {
                    if (status === 'Running') return 'pod-running';
                    if (status === 'Pending') return 'pod-pending';
                    return 'pod-failed';
                };

                const renderTenantCard = (tenant, isOffboarded = false) => (
                    <div key={tenant.id} className="tenant-card">
                        <div className="tenant-header">
                            <div className="tenant-title">
                                <span className={getStatusClass(tenant.status)}></span>
                                {tenant.name}
                                <span style={{fontSize: '0.8rem', color: '#666'}}>({tenant.id})</span>
                                {isOffboarded && <span className="offboarded-badge">OFFBOARDED</span>}
                                {isOffboarded && <span className="days-ago">{tenant.daysAgo} days ago</span>}
                            </div>
                            <div className={`tier-badge ${getTierClass(tenant.tier)}`}>
                                {tenant.tier}
                            </div>
                        </div>

                        <div className="tenant-details">
                            <div className="detail-group">
                                <h4>📊 Basic Info</h4>
                                <div className="detail-item">
                                    <span className="detail-label">Namespace:</span>
                                    <span className="detail-value">{tenant.namespace}</span>
                                </div>
                                <div className="detail-item">
                                    <span className="detail-label">Created:</span>
                                    <span className="detail-value">{formatDate(tenant.created)}</span>
                                </div>
                                <div className="detail-item">
                                    <span className="detail-label">Age:</span>
                                    <span className="detail-value">{calculateAge(tenant.created)}</span>
                                </div>
                                {isOffboarded && (
                                    <>
                                        <div className="detail-item">
                                            <span className="detail-label">Offboarded:</span>
                                            <span className="detail-value">{formatDate(tenant.offboarded)}</span>
                                        </div>
                                        <div className="detail-item">
                                            <span className="detail-label">Reason:</span>
                                            <span className="detail-value">{tenant.reason}</span>
                                        </div>
                                        <div className="detail-item">
                                            <span className="detail-label">Duration:</span>
                                            <span className="detail-value">{tenant.duration}</span>
                                        </div>
                                    </>
                                )}
                            </div>

                            <div className="detail-group">
                                <h4>💰 Cost Info</h4>
                                <div className="detail-item">
                                    <span className="detail-label">Hourly Rate:</span>
                                    <span className="detail-value">${isOffboarded ? (tenant.finalCost / 24 / parseInt(tenant.duration)).toFixed(2) : tenant.cost.hourly}</span>
                                </div>
                                <div className="detail-item">
                                    <span className="detail-label">{isOffboarded ? 'Final Cost:' : 'Monthly Est:'}</span>
                                    <span className="detail-value">${isOffboarded ? tenant.finalCost.toLocaleString() : tenant.cost.monthly.toLocaleString()}</span>
                                </div>
                            </div>

                            {!isOffboarded && (
                                <div className="detail-group">
                                    <h4>🖥️ Resources</h4>
                                    <div className="detail-item">
                                        <span className="detail-label">CPU Usage:</span>
                                        <span className="detail-value">{tenant.resources.cpu}%</span>
                                    </div>
                                    <div className="detail-item">
                                        <span className="detail-label">Memory Usage:</span>
                                        <span className="detail-value">{tenant.resources.memory}%</span>
                                    </div>
                                    <div className="detail-item">
                                        <span className="detail-label">Storage Usage:</span>
                                        <span className="detail-value">{tenant.resources.storage}%</span>
                                    </div>
                                </div>
                            )}

                            <div className="detail-group">
                                <h4>🔧 Pods ({tenant.pods.length})</h4>
                                <div className="pods-grid">
                                    {tenant.pods.map((pod, index) => (
                                        <div key={index} className={`pod-item ${getPodStatusClass(pod.status)}`}>
                                            <div style={{fontWeight: 'bold', marginBottom: '2px'}}>{pod.name}</div>
                                            <div>{pod.status} | {pod.age}</div>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        </div>
                    </div>
                );

                return (
                    <div className="container">
                        <div className="real-time-indicator">
                            <div className="pulse"></div>
                            Live Data - Last Update: {lastUpdate.toLocaleTimeString()}
                        </div>

                        <div className="header">
                            <h1>🏢 Real Tenant Manager</h1>
                            <p>Live onboarded tenants and recently offboarded tenants (1-2 weeks)</p>
                        </div>

                        <div className="tabs">
                            <button
                                className={`tab ${activeTab === 'active' ? 'active' : ''}`}
                                onClick={() => setActiveTab('active')}
                            >
                                🟢 Active Tenants ({activeTenants.length})
                            </button>
                            <button
                                className={`tab ${activeTab === 'offboarded' ? 'active' : ''}`}
                                onClick={() => setActiveTab('offboarded')}
                            >
                                🔴 Recently Offboarded ({offboardedTenants.length})
                            </button>
                        </div>

                        {loading ? (
                            <div style={{textAlign: 'center', padding: '50px'}}>
                                <div className="loading"></div>
                                <p style={{marginTop: '20px'}}>Loading real tenant data...</p>
                            </div>
                        ) : (
                            <>
                                {activeTab === 'active' && (
                                    <div className="tenant-section">
                                        <div className="section-title active-section">
                                            🟢 Currently Onboarded Tenants ({activeTenants.length})
                                        </div>
                                        {activeTenants.length > 0 ? (
                                            activeTenants.map(tenant => renderTenantCard(tenant, false))
                                        ) : (
                                            <div className="empty-state">
                                                No active tenants found
                                            </div>
                                        )}
                                    </div>
                                )}

                                {activeTab === 'offboarded' && (
                                    <div className="tenant-section">
                                        <div className="section-title offboarded-section">
                                            🔴 Recently Offboarded Tenants (Last 1-2 Weeks) ({offboardedTenants.length})
                                        </div>
                                        {offboardedTenants.length > 0 ? (
                                            offboardedTenants.map(tenant => renderTenantCard(tenant, true))
                                        ) : (
                                            <div className="empty-state">
                                                No recently offboarded tenants found
                                            </div>
                                        )}
                                    </div>
                                )}
                            </>
                        )}
                    </div>
                );
            }

            ReactDOM.render(<RealTenantManager />, document.getElementById('root'));
        </script>
    </body>
    </html>
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: real-tenant-manager
  namespace: tenant-management
  labels:
    app: real-tenant-manager
spec:
  replicas: 2
  selector:
    matchLabels:
      app: real-tenant-manager
  template:
    metadata:
      labels:
        app: real-tenant-manager
    spec:
      containers:
      - name: ui
        image: nginx:alpine
        ports:
        - containerPort: 80
        volumeMounts:
        - name: ui-content
          mountPath: /usr/share/nginx/html
        resources:
          requests:
            memory: "64Mi"
            cpu: "50m"
          limits:
            memory: "128Mi"
            cpu: "100m"
      volumes:
      - name: ui-content
        configMap:
          name: real-tenant-ui
---
apiVersion: v1
kind: Service
metadata:
  name: real-tenant-manager
  namespace: tenant-management
  labels:
    app: real-tenant-manager
spec:
  selector:
    app: real-tenant-manager
  ports:
  - port: 80
    targetPort: 80
    name: http
  type: ClusterIP
